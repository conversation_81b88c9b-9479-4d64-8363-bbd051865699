# Firebase Authentication Persistence in Expo App

## Overview

This document explains how Firebase Authentication persistence is implemented in the Namoshkar Messenger app to ensure users remain logged in even after app reloads or restarts.

## Implementation Details

### 1. Firebase Configuration (`config/firebase.ts`)

The Firebase configuration now properly handles platform-specific persistence initialization:

- **Web**: Uses `browserLocalPersistence` for storing auth state in localStorage
- **React Native**: Uses `getReactNativePersistence` with AsyncStorage for storing auth state

Key aspects:
- Conditional imports to prevent SSR errors
- Proper error handling with fallbacks
- Promise-based initialization tracking
- Explicit persistence configuration for both platforms

### 2. Auth Service (`services/authService.ts`)

The AuthService provides a wrapper around Firebase Auth operations:

- Waits for auth initialization before performing operations
- Uses Firebase's built-in persistence mechanisms
- <PERSON><PERSON><PERSON> handles user state changes

### 3. Auth Context (`contexts/AuthContext.tsx`)

The AuthContext manages the global auth state:

- Listens for auth state changes using Firebase's `onAuthStateChanged`
- Automatically updates UI when user logs in/out
- <PERSON><PERSON> profile completion checks

## How Persistence Works

### On App Start
1. Firebase is initialized with platform-appropriate persistence
2. Firebase automatically restores the previous auth state from storage
3. `onAuthStateChanged` listener is triggered with the restored user
4. AuthContext updates the global state
5. UI redirects appropriately based on auth state

### During Auth Operations
1. Login/signup operations update Firebase's persisted state
2. Logout operations clear the persisted state
3. All operations are automatically persisted by Firebase

## Testing Persistence

To test that persistence is working:

1. Log in to the app
2. Close and reopen the app (or reload in Expo Go)
3. Verify you're still logged in and redirected to the correct screen

You can also run the test script:
```bash
node test-auth-persistence.js
```

## Troubleshooting

### Common Issues

1. **"Component auth has not been registered yet"**
   - Caused by dependency conflicts
   - Solution: Use only `firebase` package, not `@react-native-firebase/*`

2. **Auth state not persisting**
   - Check that `getReactNativePersistence` is properly configured
   - Verify AsyncStorage is correctly imported and available
   - Ensure `initializeAuth` is called with the persistence option during auth initialization

3. **Infinite loading on app start**
   - May occur if auth initialization promise never resolves
   - Check that initialization promises resolve correctly

### Debugging Steps

1. Check console logs for initialization messages
2. Verify Firebase config is loading correctly
3. Ensure no dependency conflicts exist
4. Test with a fresh app install

## Dependencies

Current working setup:
```json
{
  "dependencies": {
    "firebase": "^12.2.1",
    "@react-native-async-storage/async-storage": "^2.1.2"
  }
}
```

Note: Removed `@react-native-firebase/*` packages to avoid conflicts with the JavaScript SDK.