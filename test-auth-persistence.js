// Simple test script to verify Firebase Auth persistence
// This can be run in the browser console or as a Node.js script

import { getAuth } from './config/firebase';

async function testAuthPersistence() {
  console.log('Testing Firebase Auth Persistence...');
  
  try {
    const auth = await getAuth();
    if (!auth) {
      console.log('❌ Firebase Auth not initialized');
      return;
    }
    
    console.log('✅ Firebase Auth initialized');
    console.log('Current user:', auth.currentUser);
    
    if (auth.currentUser) {
      console.log('✅ User is logged in');
      console.log('User UID:', auth.currentUser.uid);
      console.log('User email:', auth.currentUser.email);
    } else {
      console.log('ℹ️ No user currently logged in');
    }
    
    // Set up auth state listener to monitor changes
    const { onAuthStateChanged } = await import('firebase/auth');
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      console.log('Auth state changed:', user ? `User: ${user.email}` : 'No user');
    });
    
    console.log('✅ Auth state listener set up');
    
    // Return unsubscribe function for cleanup
    return unsubscribe;
  } catch (error) {
    console.error('❌ Error testing auth persistence:', error);
  }
}

// Run the test
testAuthPersistence().then(unsubscribe => {
  console.log('Test completed. To unsubscribe from listener, call the returned function.');
}).catch(error => {
  console.error('Test failed:', error);
});

export default testAuthPersistence;