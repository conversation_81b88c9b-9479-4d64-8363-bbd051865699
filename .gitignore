# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.lerna-debug.log*

# Production
/build
/dist
/out
/.next/
/coverage/
*.log

# Native build folders
/ios/build
/android/build
/backend/out

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log

# Editor directories and files
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw*

# OS generated files
Thumbs.db

# Firebase
# Protect sensitive Firebase service account keys
serviceAccountKey.json
*serviceAccountKey*.json
firebase-service-account.json
*-firebase-adminsdk-*.json

# Expo
.expo/
web-build/
npm-debug.*
*.jks
*.p8
*.p12

# Environment files
.env*.local
.env.development
.env.production

# Logs
logs
*.log
.xcodebuild.log
.xcodebuild-error.log

# Temporary files
tmp/
temp/

# Database
*.sqlite
*.sqlite3

# IDE
*.iml
*.ipr
*.iws
.out/

# Typescript
*.tsbuildinfo
tsconfig.tsbuildinfo

# Backend specific
backend/.env
backend/.env*.local
backend/node_modules/
backend/.next/
backend/.expo/
backend/web-build/
backend/npm-debug.*
backend/*.log
backend/tmp/
backend/temp/

# Security
*.pem
*.key
*.csr
*.crt
# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

expo-env.d.ts
# @end expo-cli
backend/.env.example
