import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Image as ExpoImage } from 'expo-image';
import { IconSymbol } from './ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Story, StoryStats } from '@/types/story';
import { storyService } from '@/services/storyService';
import * as Haptics from 'expo-haptics';

interface StoryViewerProps {
  visible: boolean;
  onClose: () => void;
  stories: Story[];
  initialStoryIndex?: number;
  currentUserId: string;
}

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export default function StoryViewer({ 
  visible, 
  onClose, 
  stories, 
  initialStoryIndex = 0,
  currentUserId 
}: StoryViewerProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [currentIndex, setCurrentIndex] = useState(initialStoryIndex);
  const [showStats, setShowStats] = useState(false);
  const [storyStats, setStoryStats] = useState<StoryStats>({ totalViews: 0, viewerList: [] });
  const [loadingStats, setLoadingStats] = useState(false);

  // Ensure currentIndex is within bounds
  const safeIndex = Math.min(Math.max(0, currentIndex), stories.length - 1);
  const currentStory = stories[safeIndex];

  useEffect(() => {
    if (visible && currentStory) {
      // Mark story as viewed when opened
      markStoryAsViewed(currentStory.id);
      setCurrentIndex(initialStoryIndex);
    }
  }, [visible, initialStoryIndex]);

  useEffect(() => {
    if (currentIndex >= 0 && currentIndex < stories.length) {
      const story = stories[currentIndex];
      if (story) {
        markStoryAsViewed(story.id);
      }
    }
  }, [currentIndex]);

  const markStoryAsViewed = async (storyId: string) => {
    try {
      await storyService.markStoryAsViewed(storyId, currentUserId);
    } catch (error) {
      console.error('Error marking story as viewed:', error);
    }
  };

  const handleViewStats = async () => {
    if (!currentStory) return;

    // Only show stats if it's the user's own story
    if (currentStory.userId !== currentUserId) {
      return;
    }

    setLoadingStats(true);
    try {
      const stats = await storyService.getStoryStats(currentStory.id);
      setStoryStats(stats);
      setShowStats(true);
    } catch (error) {
      console.error('Error getting story stats:', error);
      Alert.alert('Error', 'Failed to load story statistics');
    } finally {
      setLoadingStats(false);
    }
  };

  const handleDeleteStory = () => {
    if (!currentStory || currentStory.userId !== currentUserId) {
      return;
    }

    Alert.alert(
      'Delete Story',
      'Are you sure you want to delete this story?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await storyService.deleteStory(currentStory.id);
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
              onClose();
            } catch (error) {
              console.error('Error deleting story:', error);
              Alert.alert('Error', 'Failed to delete story');
            }
          },
        },
      ]
    );
  };

  const goToNextStory = () => {
    Haptics.selectionAsync();
    if (currentIndex < stories.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else {
      onClose();
    }
  };

  const goToPreviousStory = () => {
    Haptics.selectionAsync();
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleClose = () => {
    Haptics.selectionAsync();
    onClose();
  };

  const formatTimeAgo = (date: Date | undefined) => {
    if (!date) return 'Unknown time';
    
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  if (!currentStory) {
    return null;
  }

  const isOwnStory = currentStory.userId === currentUserId;
  const hasViewed = currentStory.viewedBy?.includes(currentUserId) || false;

  return (
    <Modal
      visible={visible}
      animationType="fade"
      presentationStyle="fullScreen"
      onRequestClose={handleClose}
    >
      <View style={[styles.container, { backgroundColor: '#000' }]}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <IconSymbol name="xmark" size={24} color="#fff" />
          </TouchableOpacity>
          
          <View style={styles.userInfo}>
            <ExpoImage
              source={{ uri: currentStory.userAvatar }}
              style={styles.userAvatar}
            />
            <View>
              <Text style={styles.userName}>{currentStory.userName}</Text>
              <Text style={styles.storyTime}>{formatTimeAgo(currentStory.timestamp)}</Text>
            </View>
          </View>

          {isOwnStory && (
            <View style={styles.actionButtons}>
              <TouchableOpacity onPress={handleViewStats} style={styles.actionButton}>
                <IconSymbol name="eye.fill" size={20} color="#fff" />
              </TouchableOpacity>
              <TouchableOpacity onPress={handleDeleteStory} style={styles.actionButton}>
                <IconSymbol name="trash.fill" size={20} color="#fff" />
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Story Content */}
        <View style={styles.content}>
          <ExpoImage
            source={{ uri: currentStory.mediaUrl }}
            style={styles.storyMedia}
            contentFit="contain"
          />
          
          {currentStory.caption && (
            <View style={styles.captionContainer}>
              <Text style={styles.captionText}>{currentStory.caption}</Text>
            </View>
          )}
        </View>

        {/* Progress Indicators */}
        <View style={styles.progressContainer}>
          {stories.map((story, index) => (
            <View
              key={`progress-${story.id}-${index}`}
              style={[
                styles.progressBar,
                index === safeIndex && styles.progressBarActive,
                index < safeIndex && styles.progressBarCompleted,
              ]}
            />
          ))}
        </View>

        {/* Navigation Areas */}
        <TouchableOpacity style={styles.navAreaLeft} onPress={goToPreviousStory} />
        <TouchableOpacity style={styles.navAreaRight} onPress={goToNextStory} />

        {/* Story Stats Modal */}
        <Modal
          visible={showStats}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowStats(false)}
        >
          <View style={[styles.statsContainer, { backgroundColor: isDark ? '#000' : '#fff' }]}>
            <View style={styles.statsHeader}>
              <TouchableOpacity onPress={() => setShowStats(false)}>
                <IconSymbol name="xmark" size={24} color={isDark ? '#fff' : '#000'} />
              </TouchableOpacity>
              <Text style={[styles.statsTitle, { color: isDark ? '#fff' : '#000' }]}>
                Story Stats
              </Text>
              <View style={{ width: 24 }} />
            </View>

            {loadingStats ? (
              <View style={styles.statsLoading}>
                <ActivityIndicator size="large" color="#FF6B35" />
              </View>
            ) : (
              <ScrollView style={styles.statsContent}>
                <View style={[styles.statCard, { backgroundColor: isDark ? '#1a1a1a' : '#f8f9fa' }]}>
                  <IconSymbol name="eye.fill" size={24} color="#FF6B35" />
                  <View>
                    <Text style={[styles.statNumber, { color: isDark ? '#fff' : '#000' }]}>
                      {storyStats.totalViews}
                    </Text>
                    <Text style={[styles.statLabel, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
                      Views
                    </Text>
                  </View>
                </View>

                <Text style={[styles.viewersTitle, { color: isDark ? '#fff' : '#000' }]}>
                  Viewers
                </Text>
                
                {storyStats.viewerList.length > 0 ? (
                  storyStats.viewerList.map((viewer, index) => (
                    <View
                      key={viewer.userId}
                      style={[styles.viewerItem, { backgroundColor: isDark ? '#1a1a1a' : '#f8f9fa' }]}
                    >
                      <ExpoImage
                        source={{ uri: `https://ui-avatars.com/api/?name=${encodeURIComponent(viewer.userName)}&background=random` }}
                        style={styles.viewerAvatar}
                      />
                      <Text style={[styles.viewerName, { color: isDark ? '#fff' : '#000' }]}>
                        {viewer.userName}
                      </Text>
                    </View>
                  ))
                ) : (
                  <Text style={[styles.noViewersText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
                    No viewers yet
                  </Text>
                )}
              </ScrollView>
            )}
          </View>
        </Modal>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'linear-gradient(to bottom, rgba(0,0,0,0.8), transparent)',
  },
  closeButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  userName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  storyTime: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 16,
  },
  actionButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  storyMedia: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT * 0.85,
  },
  captionContainer: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    right: 20,
  },
  captionText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  progressContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    flexDirection: 'row',
    gap: 4,
    zIndex: 11,
  },
  progressBar: {
    flex: 1,
    height: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 1,
  },
  progressBarActive: {
    backgroundColor: '#fff',
  },
  progressBarCompleted: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
  },
  navAreaLeft: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: '30%',
    zIndex: 5,
  },
  navAreaRight: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    width: '30%',
    zIndex: 5,
  },
  statsContainer: {
    flex: 1,
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  statsLoading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsContent: {
    flex: 1,
    padding: 20,
  },
  statCard: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
  },
  statLabel: {
    fontSize: 14,
  },
  viewersTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  viewerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
  },
  viewerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  viewerName: {
    fontSize: 16,
    fontWeight: '500',
  },
  noViewersText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
  },
});