import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { getAndroidVersionAwarePadding } from '@/utils/platform';
import { Message } from '@/types/messenger';
import * as ImagePicker from 'expo-image-picker';
import React, { useState } from 'react';
import {
    ActionSheetIOS,
    Alert,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

interface MessageInputProps {
  onSend: (text: string) => void;
  onSendImage: (imageUri: string) => void;
  onSendVideo: (videoUri: string) => void;
  replyingTo?: Message | null;
  onCancelReply?: () => void;
  isDark?: boolean;
  placeholder?: string;
}

export default function MessageInput({ 
  onSend, 
  onSendImage, 
  onSendVideo,
  replyingTo,
  onCancelReply,
  isDark: propIsDark,
  placeholder = "Message..." 
}: MessageInputProps) {
  const [messageText, setMessageText] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = propIsDark !== undefined ? propIsDark : colorScheme === 'dark';

  const emojis = [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
    '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
    '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
    '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
    '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗',
    '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯',
    '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
    '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈',
    '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉',
    '👆', '🖕', '👇', '☝️', '👋', '🤚', '🖐', '✋', '🖖', '👏',
    '🙌', '🤲', '🤝', '🙏', '✍️', '💅', '🤳', '💪', '🦾', '🦿',
    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
    '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
    '✝️', '☪️', '🕉', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐',
    '⭐', '🌟', '✨', '⚡', '☄️', '💥', '🔥', '🌪', '🌈', '☀️',
  ];

  const handleSend = () => {
    if (messageText.trim()) {
      onSend(messageText.trim());
      setMessageText('');
    }
  };

  const showAttachmentOptions = () => {
    const options = ['Camera', 'Photo Library', 'Video', 'Emoji', 'Cancel'];
    const cancelButtonIndex = 4;

    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options,
          cancelButtonIndex,
        },
        (buttonIndex) => {
          if (buttonIndex === 0) {
            pickImageFromCamera();
          } else if (buttonIndex === 1) {
            pickImageFromLibrary();
          } else if (buttonIndex === 2) {
            pickVideoFromLibrary();
          } else if (buttonIndex === 3) {
            setShowEmojiPicker(!showEmojiPicker);
          }
        }
      );
    } else {
      Alert.alert(
        'Add Attachment',
        'Choose an option',
        [
          { text: 'Camera', onPress: pickImageFromCamera },
          { text: 'Photo Library', onPress: pickImageFromLibrary },
          { text: 'Video', onPress: pickVideoFromLibrary },
          { text: 'Emoji', onPress: () => setShowEmojiPicker(!showEmojiPicker) },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    }
  };

  const addEmoji = (emoji: string) => {
    setMessageText(prev => prev + emoji);
  };

  const pickImageFromCamera = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Camera permission is required to take photos');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: false,
      quality: 0.7,
    });

    if (!result.canceled && result.assets[0]) {
      onSendImage(result.assets[0].uri);
    }
  };

  const pickImageFromLibrary = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: false,
      quality: 0.7,
    });

    if (!result.canceled && result.assets[0]) {
      onSendImage(result.assets[0].uri);
    }
  };

  const pickVideoFromLibrary = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['videos'],
      allowsEditing: false,
      quality: 0.7,
    });

    if (!result.canceled && result.assets[0]) {
      console.log('📹 Video selected from library:', result.assets[0].uri);
      onSendVideo(result.assets[0].uri);
    }
  };

  return (
    <View style={styles.safeContainer}>
      {/* Emoji Picker */}
      {showEmojiPicker && (
        <View style={[styles.emojiPicker, {
          backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
          shadowOffset: { width: 0, height: -4 },
          shadowOpacity: 1,
          shadowRadius: 20,
          elevation: 10,
        }]}>
          <View style={styles.emojiHeader}>
            <Text style={[styles.emojiTitle, {
              color: isDark ? '#ffffff' : '#000000',
            }]}>
              Choose an emoji
            </Text>
            <TouchableOpacity 
              onPress={() => setShowEmojiPicker(false)}
              style={[styles.emojiCloseButton, {
                backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
              }]}
            >
              <IconSymbol name="xmark" size={16} color="#8e8e93" />
            </TouchableOpacity>
          </View>
          <ScrollView 
            style={styles.emojiScrollView}
            contentContainerStyle={styles.emojiGrid}
            showsVerticalScrollIndicator={false}
          >
            {emojis.map((emoji, index) => (
              <TouchableOpacity
                key={index}
                style={styles.emojiButton}
                onPress={() => addEmoji(emoji)}
                activeOpacity={0.7}
              >
                <Text style={styles.emojiText}>{emoji}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      <View style={[styles.container, { 
        backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
        shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
        shadowOffset: { width: 0, height: -4 },
        shadowOpacity: 1,
        shadowRadius: 20,
        elevation: 10,
      }]}>
        <View style={styles.inputRow}>
          <TouchableOpacity 
            onPress={() => setShowEmojiPicker(!showEmojiPicker)} 
            style={[styles.emojiToggleButton, {
              backgroundColor: showEmojiPicker ? '#FF6B35' : (isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)'),
            }]}
            activeOpacity={0.7}
          >
            <Text style={[styles.emojiToggleText, {
              color: showEmojiPicker ? '#ffffff' : '#FF6B35',
            }]}>
              😊
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            onPress={showAttachmentOptions} 
            style={[styles.attachButton, {
              backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
            }]}
            activeOpacity={0.7}
          >
            <IconSymbol name="plus" size={18} color="#FF6B35" />
          </TouchableOpacity>
          
          <View style={[styles.inputWrapper, {
            backgroundColor: isDark ? 'rgba(42, 42, 42, 0.6)' : 'rgba(240, 240, 240, 0.6)',
            shadowColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 1,
            shadowRadius: 8,
            elevation: 2,
          }]}>
            <TextInput
              style={[styles.textInput, { 
                color: isDark ? '#ffffff' : '#000000',
              }]}
              placeholder={placeholder}
              placeholderTextColor={isDark ? 'rgba(142, 142, 147, 0.7)' : 'rgba(142, 142, 147, 0.8)'}
              value={messageText}
              onChangeText={setMessageText}
              multiline
              maxLength={1000}
              onSubmitEditing={handleSend}
              blurOnSubmit={false}
            />
          </View>
          
          <TouchableOpacity 
            onPress={handleSend} 
            style={[styles.sendButton, { 
              opacity: messageText.trim() ? 1 : 0.4,
              backgroundColor: messageText.trim() ? '#FF6B35' : 'rgba(142, 142, 147, 0.3)',
              shadowColor: messageText.trim() ? '#FF6B35' : 'transparent',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: messageText.trim() ? 0.4 : 0,
              shadowRadius: 12,
              elevation: messageText.trim() ? 6 : 0,
              transform: [{ scale: messageText.trim() ? 1 : 0.9 }],
            }]}
            disabled={!messageText.trim()}
            activeOpacity={0.8}
          >
            <IconSymbol name="arrow.up" size={16} color="#ffffff" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  safeContainer: {
    // Container without extra spacing
  },
  container: {
    paddingHorizontal: 24,
    paddingTop: 8,
    paddingBottom: Platform.select({ 
      ios: 20, 
      android: getAndroidVersionAwarePadding(16, 24) 
    }),
    borderTopWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center', // Changed from 'flex-end' to 'center' for better vertical alignment
  },
  emojiToggleButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    marginBottom: 0, // Removed bottom margin
  },
  emojiToggleText: {
    fontSize: 20,
  },
  attachButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 0, // Removed bottom margin
  },
  inputWrapper: {
    flex: 1,
    borderRadius: 24,
    marginRight: 16,
    backdropFilter: 'blur(10px)',
  },
  textInput: {
    paddingHorizontal: 20,
    paddingVertical: 10, // Reduced from 14 to 10
    fontSize: 17,
    maxHeight: 120,
    lineHeight: 22,
    fontWeight: '400',
    letterSpacing: -0.2,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 0, // Removed bottom margin
  },
  emojiPicker: {
    height: 280,
    paddingTop: 20,
    paddingHorizontal: 24,
    backdropFilter: 'blur(20px)',
  },
  emojiHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  emojiTitle: {
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: -0.2,
  },
  emojiCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emojiScrollView: {
    flex: 1,
  },
  emojiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingBottom: 20,
  },
  emojiButton: {
    width: '12%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    borderRadius: 12,
  },
  emojiText: {
    fontSize: 24,
  },
});