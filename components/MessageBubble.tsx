import { useColorScheme } from '@/hooks/useColorScheme';
import { Message, User } from '@/types/messenger';
import { format, isToday, isYesterday } from 'date-fns';
import { Image } from 'expo-image';
import React, { useState } from 'react';
import {
    ActionSheetIOS,
    Alert,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import EmojiPicker from './EmojiPicker';

interface MessageBubbleProps {
  message: Message;
  sender: User;
  isCurrentUser: boolean;
  isGroup: boolean;
  onAddReaction: (messageId: string, emoji: string) => void;
  onReply?: (message: Message) => void;
  onImagePress?: (imageUrl: string) => void;
  onDelete?: (messageId: string) => void;
}

export default function MessageBubble({ 
  message, 
  sender, 
  isCurrentUser, 
  isGroup,
  onAddReaction,
  onReply,
  onImagePress,
  onDelete
}: MessageBubbleProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  const formatMessageTime = (timestamp: Date) => {
    if (isToday(timestamp)) {
      return format(timestamp, 'HH:mm');
    } else if (isYesterday(timestamp)) {
      return `Yesterday ${format(timestamp, 'HH:mm')}`;
    } else {
      return format(timestamp, 'MMM d, HH:mm');
    }
  };

  const showMessageOptions = () => {
    const options = ['Add Reaction', 'Reply', 'Copy', 'Delete', 'Cancel'];
    const destructiveButtonIndex = 3;
    const cancelButtonIndex = 4;

    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options,
          destructiveButtonIndex,
          cancelButtonIndex,
        },
        (buttonIndex) => {
          switch (buttonIndex) {
            case 0:
              showReactionOptions();
              break;
            case 1:
              onReply?.(message);
              break;
            case 2:
              // Copy message
              Alert.alert('Copied', 'Message copied to clipboard');
              break;
            case 3:
              // Delete message
              Alert.alert('Delete Message', 'Are you sure?', [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Delete', style: 'destructive', onPress: () => onDelete?.(message.id) },
              ]);
              break;
          }
        }
      );
    } else {
      Alert.alert(
        'Message Options',
        '',
        [
          { text: 'Add Reaction', onPress: showReactionOptions },
          { text: 'Reply', onPress: () => onReply?.(message) },
          { text: 'Copy', onPress: () => Alert.alert('Copied', 'Message copied to clipboard') },
          { text: 'Delete', style: 'destructive', onPress: () => onDelete?.(message.id) },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    }
  };

  const showReactionOptions = () => {
    setShowEmojiPicker(true);
  };

  const handleReactionPress = (emoji: string) => {
    onAddReaction(message.id, emoji);
  };

  // Don't show message options for deleted messages
  const handleLongPress = message.deleted ? undefined : showMessageOptions;

  return (
    <>
      <TouchableOpacity
        style={[
          styles.container,
          isCurrentUser ? styles.currentUserContainer : styles.otherUserContainer,
          message.deleted && styles.deletedMessageContainer,
        ]}
        onLongPress={handleLongPress}
        activeOpacity={message.deleted ? 1 : 0.9}
      >
      <View style={[
        styles.bubble,
        isCurrentUser ? [styles.currentUserBubble, {
          shadowColor: '#FF6B35',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.2,
          shadowRadius: 8,
          // elevation: 3,
        }] : [styles.otherUserBubble, { 
          backgroundColor: isDark ? '#2a2a2a' : '#f0f0f0',
          shadowColor: isDark ? '#ffffff10' : '#00000010',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 8,
          // elevation: 2,
        }],
      ]}>
        {message.deleted ? (
          // Deleted message UI
          <View style={styles.deletedMessageContent}>
            <Text style={[
              styles.deletedMessageText,
              { 
                color: isCurrentUser ? 'rgba(255,255,255,0.7)' : (isDark ? '#8e8e93' : '#6c6c70'),
                fontStyle: 'italic',
              }
            ]}>
              {isCurrentUser ? 'You deleted this message' : 'This message was deleted'}
            </Text>
            <Text style={[
              styles.deletedTimestamp,
              { 
                color: isCurrentUser ? 'rgba(255,255,255,0.6)' : (isDark ? '#6c6c70' : '#8e8e93'),
                fontSize: 10,
              }
            ]}>
              {formatMessageTime(message.timestamp)}
            </Text>
          </View>
        ) : (
          // Regular message UI
          <>
            {!isCurrentUser && isGroup && (
              <Text style={[styles.senderName, { 
                color: '#FF6B35',
                fontWeight: '600',
                fontSize: 13,
              }]}>
                {sender.name}
              </Text>
            )}
            
            {message.replyTo && (
              <View style={[styles.replyContainer, { 
                backgroundColor: isDark ? '#3a3a3c' : '#e8e8e8',
                borderRadius: 8,
              }]}>
                <Text style={[styles.replyText, { 
                  color: isDark ? '#8e8e93' : '#6c6c70',
                  fontStyle: 'italic',
                }]}>
                  Replying to message...
                </Text>
              </View>
            )}
            
            {message.type === 'image' && (message.imageUrl || message.imageUri) ? (
              <View>
                <TouchableOpacity onPress={() => onImagePress?.(message.imageUrl || message.imageUri || '')}>
                  <View style={styles.imageContainer}>
                    <Image 
                      source={{ uri: message.imageUrl || message.imageUri }} 
                      style={[
                        styles.messageImage,
                        {
                          aspectRatio: message.imageWidth && message.imageHeight 
                            ? message.imageWidth / message.imageHeight 
                            : 4 / 3, // Default aspect ratio
                          shadowColor: isDark ? '#ffffff20' : '#00000020',
                          shadowOffset: { width: 0, height: 2 },
                          shadowOpacity: 1,
                          shadowRadius: 4,
                          // elevation: 2,
                        }
                      ]} 
                      contentFit="cover"
                    />
                  </View>
                </TouchableOpacity>
                {message.text && (
                  <Text style={[
                    styles.messageText,
                    { 
                      color: isCurrentUser ? '#fff' : (isDark ? '#ffffff' : '#1a1a1a'),
                      fontWeight: '400',
                    }
                  ]}>
                    {message.text}
                  </Text>
                )}
              </View>
            ) : (
              <Text style={[
                styles.messageText,
                { 
                  color: isCurrentUser ? '#fff' : (isDark ? '#ffffff' : '#1a1a1a'),
                  fontWeight: '400',
                  lineHeight: 22,
                }
              ]}>
                {message.text}
              </Text>
            )}
            
            <Text style={[
              styles.timestamp,
              { 
                color: isCurrentUser ? 'rgba(255,255,255,0.8)' : (isDark ? '#8e8e93' : '#6c6c70'),
                fontSize: 11,
                fontWeight: '500',
              }
            ]}>
              {formatMessageTime(message.timestamp)}
            </Text>
          </>
        )}
      </View>

      {!message.deleted && message.reactions && Object.keys(message.reactions).length > 0 && (
        <View style={[
          styles.reactionsContainer,
          isCurrentUser ? styles.currentUserReactions : styles.otherUserReactions
        ]}>
          {Object.entries(message.reactions).map(([emoji, users], index) => (
            <TouchableOpacity
              key={`${emoji}-${index}`}
              style={[styles.reactionBubble, { 
                backgroundColor: isDark ? '#2a2a2a' : '#ffffff',
                shadowColor: isDark ? '#ffffff10' : '#00000015',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 1,
                shadowRadius: 4,
                // elevation: 2,
              }]}
              onPress={() => handleReactionPress(emoji)}
            >
              <Text style={styles.reactionEmoji}>{emoji}</Text>
              {users.length > 1 && (
                <Text style={styles.reactionCount}>{users.length}</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>
      )}
    </TouchableOpacity>

    <EmojiPicker
      visible={showEmojiPicker}
      onClose={() => setShowEmojiPicker(false)}
      onEmojiSelect={(emoji) => onAddReaction(message.id, emoji)}
      title="Add Reaction"
    />
  </>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 3,
    marginHorizontal: 20,
  },
  currentUserContainer: {
    alignItems: 'flex-end',
  },
  otherUserContainer: {
    alignItems: 'flex-start',
  },
  bubble: {
    maxWidth: '85%',
    padding: 14,
    borderRadius: 20,
  },
  currentUserBubble: {
    backgroundColor: '#FF6B35',
  },
  otherUserBubble: {
    // backgroundColor set dynamically
  },
  senderName: {
    marginBottom: 6,
  },
  replyContainer: {
    padding: 10,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#FF6B35',
  },
  replyText: {
    fontSize: 13,
  },
  messageText: {
    fontSize: 16,
  },
  imageContainer: {
    borderRadius: 16,
    marginBottom: 6,
    overflow: 'hidden',
    maxWidth: 280,
    width: 280, // Fixed width to prevent infinite expansion
  },
  messageImage: {
    width: '100%',
    borderRadius: 16,
  },
  timestamp: {
    marginTop: 6,
    alignSelf: 'flex-end',
  },
  reactionsContainer: {
    flexDirection: 'row',
    marginTop: 6,
    flexWrap: 'wrap',
  },
  currentUserReactions: {
    justifyContent: 'flex-end',
  },
  otherUserReactions: {
    justifyContent: 'flex-start',
  },
  reactionBubble: {
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 6,
    marginTop: 4,
    borderWidth: 0,
  },
  reactionEmoji: {
    fontSize: 16,
  },
  reactionCount: {
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '600',
    color: '#8e8e93',
  },
  deletedMessageContainer: {
    opacity: 0.6,
  },
  deletedMessageContent: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  deletedMessageText: {
    fontSize: 14,
    textAlign: 'center',
  },
  deletedTimestamp: {
    marginTop: 4,
    textAlign: 'center',
  },
});