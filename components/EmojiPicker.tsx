import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import {
    Modal,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

interface EmojiPickerProps {
  visible: boolean;
  onClose: () => void;
  onEmojiSelect: (emoji: string) => void;
  title?: string;
}

export default function EmojiPicker({ 
  visible, 
  onClose, 
  onEmojiSelect, 
  title = "Choose an emoji" 
}: EmojiPickerProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const emojiCategories = {
    'Smileys': [
      '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
      '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
      '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
      '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
    ],
    'Hearts': [
      '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
      '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '💌',
    ],
    'Gestures': [
      '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉',
      '👆', '👇', '☝️', '👋', '🤚', '🖐', '✋', '🖖', '👏', '🙌',
      '🤲', '🤝', '🙏', '✍️', '💅', '🤳', '💪', '🦾', '🦿', '🦵',
    ],
    'Objects': [
      '⭐', '🌟', '✨', '⚡', '☄️', '💥', '🔥', '🌪', '🌈', '☀️',
      '🌙', '⭐', '🌟', '✨', '⚡', '☄️', '💥', '🔥', '🌪', '🌈',
    ],
  };

  const quickReactions = ['❤️', '😂', '😮', '😢', '😡', '👍', '👎', '🔥'];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity 
          style={styles.backdrop} 
          activeOpacity={1} 
          onPress={onClose}
        />
        <View style={[styles.container, {
          backgroundColor: isDark ? 'rgba(26, 26, 26, 0.98)' : 'rgba(255, 255, 255, 0.98)',
          shadowColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
          shadowOffset: { width: 0, height: -8 },
          shadowOpacity: 1,
          shadowRadius: 24,
          elevation: 16,
        }]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.title, {
              color: isDark ? '#ffffff' : '#000000',
            }]}>
              {title}
            </Text>
            <TouchableOpacity 
              onPress={onClose}
              style={[styles.closeButton, {
                backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
              }]}
            >
              <IconSymbol name="xmark" size={16} color="#8e8e93" />
            </TouchableOpacity>
          </View>

          {/* Quick Reactions */}
          <View style={styles.quickReactionsContainer}>
            <Text style={[styles.sectionTitle, {
              color: isDark ? 'rgba(142, 142, 147, 0.8)' : 'rgba(108, 108, 112, 0.8)',
            }]}>
              QUICK REACTIONS
            </Text>
            <View style={styles.quickReactions}>
              {quickReactions.map((emoji, index) => (
                <TouchableOpacity
                  key={index}
                  style={[styles.quickReactionButton, {
                    backgroundColor: isDark ? 'rgba(42, 42, 42, 0.6)' : 'rgba(240, 240, 240, 0.6)',
                  }]}
                  onPress={() => {
                    onEmojiSelect(emoji);
                    onClose();
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={styles.quickReactionEmoji}>{emoji}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* All Emojis */}
          <ScrollView 
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
          >
            {Object.entries(emojiCategories).map(([category, emojis]) => (
              <View key={category} style={styles.categoryContainer}>
                <Text style={[styles.categoryTitle, {
                  color: isDark ? 'rgba(142, 142, 147, 0.8)' : 'rgba(108, 108, 112, 0.8)',
                }]}>
                  {category.toUpperCase()}
                </Text>
                <View style={styles.emojiGrid}>
                  {emojis.map((emoji, index) => (
                    <TouchableOpacity
                      key={index}
                      style={styles.emojiButton}
                      onPress={() => {
                        onEmojiSelect(emoji);
                        onClose();
                      }}
                      activeOpacity={0.7}
                    >
                      <Text style={styles.emojiText}>{emoji}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  container: {
    height: '70%',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 24,
    paddingHorizontal: 24,
    backdropFilter: 'blur(20px)',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickReactionsContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: '600',
    letterSpacing: 0.5,
    marginBottom: 12,
  },
  quickReactions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickReactionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickReactionEmoji: {
    fontSize: 24,
  },
  scrollView: {
    flex: 1,
  },
  categoryContainer: {
    marginBottom: 24,
  },
  categoryTitle: {
    fontSize: 13,
    fontWeight: '600',
    letterSpacing: 0.5,
    marginBottom: 12,
  },
  emojiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  emojiButton: {
    width: '11%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    borderRadius: 12,
  },
  emojiText: {
    fontSize: 28,
  },
});