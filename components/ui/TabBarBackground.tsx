import React from 'react';
import { StyleSheet, Platform, View } from 'react-native';
import { BlurView } from 'expo-blur';

export function TabBarBackground() {
  if (Platform.OS === 'ios') {
    return (
      <>
        <BlurView
          tint="dark"
          intensity={95}
          style={StyleSheet.absoluteFill}
        />
        <View style={styles.borderTop} />
      </>
    );
  }
  return null;
}

export function useBottomTabOverflow() {
  return Platform.OS === 'ios' ? 20 : 0;
}

const styles = StyleSheet.create({
  borderTop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
});
