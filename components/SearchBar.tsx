import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import * as Haptics from 'expo-haptics';
import React from 'react';
import {
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  onClear?: () => void;
}

export default function SearchBar({ 
  value, 
  onChangeText, 
  placeholder = "Search", 
  onClear 
}: SearchBarProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const triggerButtonHaptic = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  return (
    <View style={styles.container}>
      <View style={[styles.searchContainer, { 
        backgroundColor: 'transparent',
      }]}>
        <View style={[styles.iconContainer, {
          backgroundColor: isDark ? 'rgba(142, 142, 147, 0.2)' : 'rgba(142, 142, 147, 0.15)',
        }]}>
          <IconSymbol name="magnifyingglass" size={16} color="#8e8e93" />
        </View>
        <TextInput
          style={[styles.searchInput, { 
            color: isDark ? '#ffffff' : '#000000',
            fontSize: 17,
            fontWeight: '400',
            letterSpacing: -0.2,
          }]}
          placeholder={placeholder}
          placeholderTextColor={isDark ? 'rgba(142, 142, 147, 0.8)' : 'rgba(142, 142, 147, 0.9)'}
          value={value}
          onChangeText={onChangeText}
          returnKeyType="search"
        />
        {value.length > 0 && (
          <TouchableOpacity 
            onPress={() => {
              triggerButtonHaptic();
              onClear && onClear();
            }} 
            style={[styles.clearButton, {
              backgroundColor: isDark ? 'rgba(142, 142, 147, 0.3)' : 'rgba(142, 142, 147, 0.2)',
            }]}
            activeOpacity={0.7}
          >
            <IconSymbol name="xmark" size={12} color="#8e8e93" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 0,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 0,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  searchInput: {
    flex: 1,
  },
  clearButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
});