import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { userService } from '@/services/userService';
import { getAndroidVersionAwarePadding } from '@/utils/platform';
import { Chat, User } from '@/types/messenger';
import { router } from 'expo-router';
import { Image } from 'expo-image';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

interface ChatHeaderProps {
  chat: Chat;
  onBack: () => void;
  onViewInfo: () => void;
  isDark: boolean;
}

export default function ChatHeader({ chat, onBack, onViewInfo, isDark }: ChatHeaderProps) {
  const { user } = useAuth();
  const [otherUserDetails, setOtherUserDetails] = useState<User | null>(null);
  const colorScheme = useColorScheme();
  const isDarkMode = isDark || colorScheme === 'dark';
  
  const currentUserId = user?.uid || '';

  // Get other participant info for direct chats
  const otherParticipantId = chat.participants.find(p => p !== currentUserId);
  const otherParticipantDetails = otherParticipantId ? chat.participantDetails[otherParticipantId] : null;
  
  // Load other user details for direct chats
  useEffect(() => {
    const loadOtherUserDetails = async () => {
      if (otherParticipantId && !chat.isGroup) {
        try {
          // First try to get from participantDetails
          if (otherParticipantDetails?.name && otherParticipantDetails.name !== 'Unknown User') {
            setOtherUserDetails({
              id: otherParticipantId,
              name: otherParticipantDetails.name,
              email: otherParticipantDetails.email,
              displayName: otherParticipantDetails.name,
              photoURL: otherParticipantDetails.photoURL || undefined,
              avatar: otherParticipantDetails.photoURL || undefined,
              isOnline: false,
              lastSeen: new Date(),
              createdAt: new Date(),
            });
          } else {
            // If not available or is "Unknown User", fetch from userService
            const userDetails = await userService.getUserById(otherParticipantId);
            if (userDetails) {
              setOtherUserDetails({
                id: otherParticipantId,
                name: userDetails.displayName || userDetails.name || 'Unknown User',
                email: userDetails.email || '',
                displayName: userDetails.displayName || userDetails.name || 'Unknown User',
                photoURL: userDetails.photoURL || userDetails.avatar || undefined,
                avatar: userDetails.photoURL || userDetails.avatar || undefined,
                isOnline: userDetails.isOnline || false,
                lastSeen: userDetails.lastSeen?.toDate ? userDetails.lastSeen.toDate() : new Date(),
                createdAt: userDetails.createdAt?.toDate ? userDetails.createdAt.toDate() : new Date(),
              });
            }
          }
        } catch (error) {
          console.error('Error loading other user details:', error);
          setOtherUserDetails(null);
        }
      }
    };

    loadOtherUserDetails();
  }, [otherParticipantId, chat.isGroup, otherParticipantDetails]);

  // Listen to contact updates to refresh user details
  useEffect(() => {
    if (!otherParticipantId || chat.isGroup) return;

    const unsubscribe = userService.listenToUserContacts(user?.uid || '', (contacts) => {
      const contact = contacts.find(c => c.id === otherParticipantId);
      if (contact && (contact.name !== otherUserDetails?.name || contact.photoURL !== otherUserDetails?.photoURL)) {
        console.log('🔄 Updating user details from contact update for:', otherParticipantId);
        setOtherUserDetails(prev => ({
          id: otherParticipantId,
          name: contact.name,
          email: contact.email,
          displayName: contact.name,
          photoURL: contact.photoURL,
          avatar: contact.photoURL,
          isOnline: contact.isOnline,
          lastSeen: contact.lastSeen,
          createdAt: contact.createdAt,
        }));
      }
    });

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [otherParticipantId, chat.isGroup, user?.uid]);
  
  const chatName = chat.isGroup 
    ? (chat.name || 'Unnamed Group') 
    : (otherParticipantDetails?.name || otherUserDetails?.name || 'Unknown User');
    
  const chatAvatar = chat.isGroup 
    ? (chat.photoURL || undefined) 
    : (otherParticipantDetails?.photoURL || otherUserDetails?.photoURL || otherUserDetails?.avatar);

  const handleInfoPress = () => {
    if (chat.isGroup) {
      // Direct navigation to group settings for group chats
      router.push({
        pathname: '/group-settings',
        params: {
          chatId: chat.id,
          createdBy: chat.createdBy,
        }
      });
    } else {
      // For individual chats, navigate to user profile
      if (otherParticipantId && (otherParticipantDetails || otherUserDetails)) {
        const userData = otherUserDetails || {
          name: otherParticipantDetails?.name || 'Unknown User',
          photoURL: otherParticipantDetails?.photoURL,
          email: otherParticipantDetails?.email,
          isOnline: false,
          avatar: otherParticipantDetails?.photoURL,
        };
        
        router.push({
          pathname: '/user-profile',
          params: { 
            userId: otherParticipantId,
            userName: userData.name || 'Unknown User',
            userAvatar: userData.photoURL || userData.avatar || 'https://via.placeholder.com/150',
            userIsOnline: userData.isOnline ? 'true' : 'false',
            userIdTag: userData.email || '@unknown'
          }
        });
      }
    }
  };

  

  return (
    <View style={[styles.container, { 
      backgroundColor: isDarkMode ? '#1a1a1a' : '#ffffff',
    }]}>
      <TouchableOpacity onPress={onBack} style={[styles.backButton, {
        backgroundColor: isDarkMode ? '#2a2a2a' : '#f0f0f0',
      }]}>
        <IconSymbol name="chevron.left" size={20} color="#FF6B35" />
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.headerContent} onPress={handleInfoPress} activeOpacity={0.8}>
        {chatAvatar ? (
          <Image
            source={{ uri: chatAvatar }}
            style={[styles.avatar, { 
              backgroundColor: isDarkMode ? '#2a2a2a' : '#e0e0e0',
            }]}
          />
        ) : (
          <View style={[styles.avatar, styles.avatarPlaceholder, { 
            backgroundColor: isDarkMode ? '#2a2a2a' : '#e0e0e0',
          }]}>
            {chatName !== 'Unknown User' && (
              <Text style={styles.avatarText}>
                {chatName.charAt(0).toUpperCase()}
              </Text>
            )}
          </View>
        )}
        <View style={styles.textContent}>
          <Text style={[styles.title, { 
            color: isDarkMode ? '#ffffff' : '#1a1a1a',
            fontWeight: '600',
            letterSpacing: -0.2,
          }]} numberOfLines={1}>
            {chatName}
          </Text>
          {chat.isGroup ? (
            <Text style={[styles.subtitle, { 
              color: isDarkMode ? '#8e8e93' : '#6c6c70',
              fontWeight: '500',
            }]}>
              {chat.participants.length} members
            </Text>
          ) : (
            <Text style={[styles.subtitle, { 
              color: otherUserDetails?.isOnline ? '#30d158' : (isDarkMode ? '#8e8e93' : '#6c6c70'),
              fontWeight: '500',
            }]}>
              {otherUserDetails?.isOnline ? 'Active now' : 'Last seen recently'}
            </Text>
          )}
        </View>
      </TouchableOpacity>

      <View style={styles.actions}>
        <TouchableOpacity onPress={handleInfoPress} style={[styles.actionButton, {
          backgroundColor: isDarkMode ? '#2a2a2a' : '#f0f0f0',
        }]}>
          <IconSymbol name="info.circle" size={18} color="#FF6B35" />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.select({ 
      ios: 12, 
      android: getAndroidVersionAwarePadding(20, 24)
    }),
    paddingBottom: 12,
    borderBottomWidth: 0,
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    marginRight: 16,
  },
  avatarPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FF6B35',
    fontSize: 20,
    fontWeight: 'bold',
  },
  textContent: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
  },
  subtitle: {
    fontSize: 13,
    marginTop: 2,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
});