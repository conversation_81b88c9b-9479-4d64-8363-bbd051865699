import React from 'react';
import { Animated, ViewStyle } from 'react-native';

interface AnimatedTabScreenProps {
  children: React.ReactNode;
  style?: ViewStyle;
  isActive?: boolean;
}

export const AnimatedTabScreen: React.FC<AnimatedTabScreenProps> = ({ 
  children, 
  style, 
  isActive = true 
}) => {
  const opacity = React.useRef(new Animated.Value(isActive ? 1 : 0)).current;
  const translateY = React.useRef(new Animated.Value(isActive ? 0 : 10)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: isActive ? 1 : 0,
        duration: 180,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: isActive ? 0 : 10,
        duration: 180,
        useNativeDriver: true,
      }),
    ]).start();
  }, [isActive, opacity, translateY]);

  return (
    <Animated.View
      style={[
        style,
        {
          opacity,
          transform: [{ translateY }],
          flex: 1,
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};