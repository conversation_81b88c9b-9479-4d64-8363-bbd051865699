import React from 'react';
import { Animated, ViewStyle } from 'react-native';

interface TabTransitionProps {
  children: React.ReactNode;
  style?: ViewStyle;
  isActive?: boolean;
}

export const TabTransition: React.FC<TabTransitionProps> = ({ children, style, isActive = true }) => {
  const opacity = React.useRef(new Animated.Value(isActive ? 1 : 0)).current;
  const scale = React.useRef(new Animated.Value(isActive ? 1 : 0.95)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: isActive ? 1 : 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(scale, {
        toValue: isActive ? 1 : 0.95,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  }, [isActive, opacity, scale]);

  return (
    <Animated.View
      style={[
        style,
        {
          opacity,
          transform: [{ scale }],
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};

export const FadeInSlideUp: React.FC<TabTransitionProps> = ({ children, style, isActive = true }) => {
  const opacity = React.useRef(new Animated.Value(isActive ? 1 : 0)).current;
  const translateY = React.useRef(new Animated.Value(isActive ? 0 : 20)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: isActive ? 1 : 0,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: isActive ? 0 : 20,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start();
  }, [isActive, opacity, translateY]);

  return (
    <Animated.View
      style={[
        style,
        {
          opacity,
          transform: [{ translateY }],
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};