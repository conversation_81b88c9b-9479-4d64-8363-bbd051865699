import React, { useState, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, View, Modal, Text, ActionSheetIOS, Alert, Platform } from 'react-native';
import { VideoView, useVideoPlayer } from 'expo-video';
import { Image } from 'expo-image';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Message } from '@/types/messenger';
import EmojiPicker from './EmojiPicker';
import { format, isToday, isYesterday } from 'date-fns';
import * as MediaLibrary from 'expo-media-library';
import { default as FileSystem } from 'expo-file-system';

interface VideoMessageBubbleProps {
  message: Message;
  isDark?: boolean;
  onVideoPress?: () => void;
  onAddReaction?: (messageId: string, emoji: string) => void;
  onDelete?: (messageId: string) => void;
}

export default function VideoMessageBubble({ 
  message, 
  isDark: propIsDark,
  onVideoPress,
  onAddReaction,
  onDelete
}: VideoMessageBubbleProps) {
  const colorScheme = useColorScheme();
  const isDark = propIsDark !== undefined ? propIsDark : colorScheme === 'dark';
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [shouldPlayVideo, setShouldPlayVideo] = useState(false);
  const player = useVideoPlayer(playbackUrl, player => {
    if (shouldPlayVideo) {
      player.play();
    }
  });

  // Effects
  useEffect(() => {
    console.log('🎬 Video player useEffect - isModalVisible:', isModalVisible, 'shouldPlayVideo:', shouldPlayVideo);
    if (!isModalVisible) {
      setShouldPlayVideo(false);
    }
  }, [isModalVisible, shouldPlayVideo]);

  useEffect(() => {
    if (player && isModalVisible && shouldPlayVideo) {
      player.play();
    } else if (player && !shouldPlayVideo) {
      player.pause();
    }
  }, [player, isModalVisible, shouldPlayVideo]);

  const isOwnMessage = message.senderId === message.currentUserId;
  const videoUrl = message.videoUrl;
  const thumbnailUrl = message.videoThumbnail;
  
  // Debug logging
  console.log('🎬 VideoMessageBubble - videoUrl:', videoUrl);
  console.log('🎬 VideoMessageBubble - thumbnailUrl:', thumbnailUrl);

  if (!videoUrl || !thumbnailUrl) return null;

  // Ensure Firebase Storage URL is properly formatted for playback
  const getPlaybackUrl = (url: string) => {
    if (url.includes('firebasestorage.googleapis.com')) {
      // Firebase Storage URLs should work directly, but let's ensure they're properly formatted
      return url;
    }
    return url;
  };

  const playbackUrl = getPlaybackUrl(videoUrl);
  console.log('🎬 VideoMessageBubble - playbackUrl:', playbackUrl);

  const handleVideoPress = () => {
    if (message.deleted) return; // Don't open video if deleted
    setIsModalVisible(true);
    setShouldPlayVideo(true);
    onVideoPress?.();
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };

  
  const formatMessageTime = (timestamp: Date) => {
    if (isToday(timestamp)) {
      return format(timestamp, 'HH:mm');
    } else if (isYesterday(timestamp)) {
      return `Yesterday ${format(timestamp, 'HH:mm')}`;
    } else {
      return format(timestamp, 'MMM d, HH:mm');
    }
  };

  const showMessageOptions = () => {
    // Don't show message options for deleted messages
    if (message.deleted) return;
    
    const options = ['Add Reaction', 'Copy', 'Delete', 'Cancel'];
    const destructiveButtonIndex = 2;
    const cancelButtonIndex = 3;

    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options,
          destructiveButtonIndex,
          cancelButtonIndex,
        },
        (buttonIndex) => {
          switch (buttonIndex) {
            case 0:
              showReactionOptions();
              break;
            case 1:
              Alert.alert('Copied', 'Video message copied to clipboard');
              break;
            case 2:
              Alert.alert('Delete Message', 'Are you sure?', [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Delete', style: 'destructive', onPress: () => onDelete?.(message.id) },
              ]);
              break;
          }
        }
      );
    } else {
      Alert.alert(
        'Message Options',
        '',
        [
          { text: 'Add Reaction', onPress: showReactionOptions },
          { text: 'Copy', onPress: () => Alert.alert('Copied', 'Video message copied to clipboard') },
          { text: 'Delete', style: 'destructive', onPress: () => onDelete?.(message.id) },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    }
  };

  const showReactionOptions = () => {
    setShowEmojiPicker(true);
  };

  const handleReactionPress = (emoji: string) => {
    onAddReaction?.(message.id, emoji);
  };

  const saveVideoToGallery = async () => {
    if (!videoUrl) return;
    
    try {
      // Request media library permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please grant permission to save videos to your gallery.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'OK', onPress: () => MediaLibrary.requestPermissionsAsync() }
          ]
        );
        return;
      }

      // Download the video file directly to temporary location
      const fileUri = FileSystem.documentDirectory ? `${FileSystem.documentDirectory}video_${Date.now()}.mp4` : `${FileSystem.cacheDirectory || ''}video_${Date.now()}.mp4`;
      console.log('🎬 Downloading video to:', fileUri);
      const downloadResumable = FileSystem.createDownloadResumable(
        videoUrl,
        fileUri
      );
      
      const { uri } = await downloadResumable.downloadAsync();
      
      // Save to gallery using the file URI
      await MediaLibrary.createAssetAsync(uri);
      
      // Clean up temporary file
      await FileSystem.deleteAsync(uri);
      
      Alert.alert(
        'Success',
        'Video saved to your gallery!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error saving video to gallery:', error);
      Alert.alert(
        'Error',
        'Failed to save video to gallery. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <>
      <TouchableOpacity 
        onPress={handleVideoPress}
        onLongPress={showMessageOptions}
        activeOpacity={message.deleted ? 1 : 0.8}
        style={[
          styles.container,
          isOwnMessage ? styles.ownMessage : styles.otherMessage,
          message.deleted && styles.deletedMessageContainer,
        ]}
      >
        <View style={styles.videoContainer}>
          {message.deleted ? (
            // Deleted message UI
            <View style={styles.deletedVideoContent}>
              <Text style={[
                styles.deletedMessageText,
                { 
                  color: isOwnMessage ? 'rgba(255,255,255,0.7)' : (isDark ? '#8e8e93' : '#6c6c70'),
                  fontStyle: 'italic',
                }
              ]}>
                {isOwnMessage ? 'You deleted this video' : 'This video was deleted'}
              </Text>
              <Text style={[
                styles.deletedTimestamp,
                { 
                  color: isOwnMessage ? 'rgba(255,255,255,0.6)' : (isDark ? '#6c6c70' : '#8e8e93'),
                  fontSize: 10,
                }
              ]}>
                {formatMessageTime(message.timestamp)}
              </Text>
            </View>
          ) : (
            // Regular video UI
            <>
              <Image 
                source={{ uri: thumbnailUrl }}
                style={styles.thumbnail}
                contentFit="cover"
              />
              <View style={[
                styles.playButtonOverlay,
                { backgroundColor: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.5)' }
              ]}>
                <View style={styles.playButton}>
                  <IconSymbol 
                    name="play.fill" 
                    size={24} 
                    color="#ffffff" 
                    style={{ marginLeft: 2 }}
                  />
                </View>
              </View>
            </>
          )}
        </View>
        
        {!message.deleted && message.reactions && Object.keys(message.reactions).length > 0 && (
          <View style={[
            styles.reactionsContainer,
            isOwnMessage ? styles.currentUserReactions : styles.otherUserReactions
          ]}>
            {Object.entries(message.reactions).map(([emoji, users], index) => (
              <TouchableOpacity
                key={`${emoji}-${index}`}
                style={[styles.reactionBubble, { 
                  backgroundColor: isDark ? '#2a2a2a' : '#ffffff',
                  shadowColor: isDark ? '#ffffff10' : '#00000015',
                  shadowOffset: { width: 0, height: 1 },
                  shadowOpacity: 1,
                  shadowRadius: 4,
                }]}
                onPress={() => handleReactionPress(emoji)}
              >
                <Text style={styles.reactionEmoji}>{emoji}</Text>
                {users.length > 1 && (
                  <Text style={styles.reactionCount}>{users.length}</Text>
                )}
              </TouchableOpacity>
            ))}
          </View>
        )}
        
        {!message.deleted && (
          <Text style={[
            styles.timestamp,
            { 
              color: isOwnMessage ? 'rgba(255,255,255,0.8)' : (isDark ? '#8e8e93' : '#6c6c70'),
              fontSize: 11,
              fontWeight: '500',
              marginTop: 6,
              alignSelf: isOwnMessage ? 'flex-end' : 'flex-start',
            }
          ]}>
            {formatMessageTime(message.timestamp)}
          </Text>
        )}
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        onRequestClose={handleCloseModal}
        presentationStyle="fullScreen"
        animationType="fade"
      >
        <View style={[
          styles.modalContainer,
          { backgroundColor: isDark ? '#000000' : '#000000' }
        ]}>
          {isModalVisible && playbackUrl && (
            <VideoView
              player={player}
              style={styles.fullScreenVideo}
              contentFit="contain"
              allowsFullscreen={false}
              showsControls={true}
              nativeControls={true}
              onError={(error) => {
                console.error('🎬 Video playback error:', error);
              }}
            />
          )}
          
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={handleCloseModal}
            activeOpacity={0.7}
          >
            <View style={[
              styles.closeButtonBackground,
              { backgroundColor: isDark ? 'rgba(42, 42, 42, 0.9)' : 'rgba(240, 240, 240, 0.9)' }
            ]}>
              <IconSymbol name="xmark" size={20} color="#ffffff" />
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.saveButton}
            onPress={saveVideoToGallery}
            activeOpacity={0.7}
          >
            <View style={[
              styles.saveButtonBackground,
              { backgroundColor: isDark ? 'rgba(42, 42, 42, 0.9)' : 'rgba(240, 240, 240, 0.9)' }
            ]}>
              <IconSymbol name="arrow.down.circle" size={20} color="#ffffff" />
            </View>
          </TouchableOpacity>
        </View>
      </Modal>
      
      <EmojiPicker
        visible={showEmojiPicker}
        onClose={() => setShowEmojiPicker(false)}
        onEmojiSelect={(emoji) => onAddReaction?.(message.id, emoji)}
        title="Add Reaction"
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    overflow: 'hidden',
    maxWidth: 280,
    width: 280, // Fixed width to prevent infinite expansion
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    marginVertical: 3,
    marginHorizontal: 20,
  },
  ownMessage: {
    borderBottomRightRadius: 4,
  },
  otherMessage: {
    borderBottomLeftRadius: 4,
  },
  videoContainer: {
    position: 'relative',
    aspectRatio: 16 / 9,
    borderRadius: 16,
    overflow: 'hidden',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  playButtonOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 107, 53, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenVideo: {
    width: '100%',
    height: '100%',
  },
  closeButton: {
    position: 'absolute',
    top: 60,
    right: 20,
    zIndex: 10,
  },
  closeButtonBackground: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  saveButton: {
    position: 'absolute',
    top: 60,
    right: 70,
    zIndex: 10,
  },
  saveButtonBackground: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  reactionsContainer: {
    flexDirection: 'row',
    marginTop: 6,
    flexWrap: 'wrap',
  },
  currentUserReactions: {
    justifyContent: 'flex-end',
  },
  otherUserReactions: {
    justifyContent: 'flex-start',
  },
  reactionBubble: {
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 6,
    marginTop: 4,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  reactionEmoji: {
    fontSize: 16,
  },
  reactionCount: {
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '600',
    color: '#8e8e93',
  },
  timestamp: {
    marginTop: 4,
  },
  deletedMessageContainer: {
    opacity: 0.6,
  },
  deletedVideoContent: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  deletedMessageText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 4,
  },
  deletedTimestamp: {
    fontSize: 10,
    textAlign: 'center',
  },
});