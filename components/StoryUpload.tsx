import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  Modal,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { Image } from 'expo-image';
import { IconSymbol } from './ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { StoryUploadData } from '@/types/story';
import * as ImagePicker from 'expo-image-picker';

interface StoryUploadProps {
  visible: boolean;
  onClose: () => void;
  onUpload: (storyData: StoryUploadData) => void;
  visibleTo: string[]; // Array of user IDs who can view the story
}

export default function StoryUpload({ visible, onClose, onUpload, visibleTo }: StoryUploadProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [mediaUri, setMediaUri] = useState<string | null>(null);
  const [mediaType, setMediaType] = useState<'image' | 'video'>('image');
  const [caption, setCaption] = useState('');
  const [uploading, setUploading] = useState(false);

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        quality: 0.7,
      });

      if (!result.canceled && result.assets[0]) {
        setMediaUri(result.assets[0].uri);
        setMediaType('image');
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const takePhoto = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        quality: 0.7,
      });

      if (!result.canceled && result.assets[0]) {
        setMediaUri(result.assets[0].uri);
        setMediaType('image');
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const handleUpload = async () => {
    if (!mediaUri) {
      Alert.alert('Error', 'Please select a photo');
      return;
    }

    if (visibleTo.length === 0) {
      Alert.alert('Error', 'No contacts available to share with');
      return;
    }

    setUploading(true);
    try {
      const storyData: StoryUploadData = {
        mediaUri,
        mediaType,
        caption: caption.trim(),
        visibleTo,
      };

      await onUpload(storyData);
      setCaption('');
      setMediaUri(null);
      onClose();
    } catch (error) {
      console.error('Error uploading story:', error);
      Alert.alert('Error', 'Failed to upload story');
    } finally {
      setUploading(false);
    }
  };

  const resetForm = () => {
    setMediaUri(null);
    setCaption('');
    setUploading(false);
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: isDark ? '#000' : '#fff' }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={[styles.header, { backgroundColor: isDark ? '#1a1a1a' : '#f8f9fa' }]}>
          <TouchableOpacity onPress={() => {
            resetForm();
            onClose();
          }} style={styles.closeButton}>
            <IconSymbol name="xmark" size={24} color={isDark ? '#fff' : '#000'} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: isDark ? '#fff' : '#000' }]}>Create Story</Text>
          <TouchableOpacity
            onPress={handleUpload}
            disabled={!mediaUri || uploading}
            style={[styles.uploadButton, !mediaUri || uploading ? styles.uploadButtonDisabled : {}]}
          >
            {uploading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.uploadButtonText}>Share</Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {mediaUri ? (
            <View style={styles.mediaContainer}>
              <Image
                source={{ uri: mediaUri }}
                style={styles.mediaPreview}
                contentFit="contain"
              />
              <TouchableOpacity
                onPress={() => setMediaUri(null)}
                style={styles.removeMediaButton}
              >
                <IconSymbol name="trash.fill" size={20} color="#fff" />
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.mediaSelectionContainer}>
              <TouchableOpacity
                onPress={pickImage}
                style={[styles.mediaButton, { backgroundColor: isDark ? '#2a2a2a' : '#f0f0f0' }]}
              >
                <IconSymbol name="photo.fill" size={32} color="#FF6B35" />
                <Text style={[styles.mediaButtonText, { color: isDark ? '#fff' : '#000' }]}>
                  Choose from Library
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={takePhoto}
                style={[styles.mediaButton, { backgroundColor: isDark ? '#2a2a2a' : '#f0f0f0' }]}
              >
                <IconSymbol name="camera.fill" size={32} color="#FF6B35" />
                <Text style={[styles.mediaButtonText, { color: isDark ? '#fff' : '#000' }]}>
                  Take Photo
                </Text>
              </TouchableOpacity>
            </View>
          )}

          <View style={styles.captionContainer}>
            <Text style={[styles.captionLabel, { color: isDark ? '#fff' : '#000' }]}>
              Caption (Optional)
            </Text>
            <TextInput
              style={[styles.captionInput, {
                backgroundColor: isDark ? '#2a2a2a' : '#f0f0f0',
                color: isDark ? '#fff' : '#000',
                borderColor: isDark ? '#3a3a3a' : '#e0e0e0',
              }]}
              placeholder="Write a caption..."
              placeholderTextColor={isDark ? '#8e8e93' : '#8e8e93'}
              value={caption}
              onChangeText={setCaption}
              multiline
              maxLength={500}
            />
            <Text style={[styles.captionCount, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              {caption.length}/500
            </Text>
          </View>

          <View style={styles.infoContainer}>
            <View style={[styles.infoItem, { backgroundColor: isDark ? '#2a2a2a' : '#f0f0f0' }]}>
              <IconSymbol name="person.2.fill" size={20} color="#FF6B35" />
              <Text style={[styles.infoText, { color: isDark ? '#fff' : '#000' }]}>
                Visible to {visibleTo.length} contacts
              </Text>
            </View>
            <View style={[styles.infoItem, { backgroundColor: isDark ? '#2a2a2a' : '#f0f0f0' }]}>
              <IconSymbol name="clock.fill" size={20} color="#FF6B35" />
              <Text style={[styles.infoText, { color: isDark ? '#fff' : '#000' }]}>
                Expires in 24 hours
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  uploadButton: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  uploadButtonDisabled: {
    backgroundColor: '#ccc',
  },
  uploadButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  mediaContainer: {
    position: 'relative',
    width: '100%',
    height: 400, // Fixed height for consistency, but image will maintain aspect ratio inside
  },
  mediaPreview: {
    width: '100%',
    height: '100%',
  },
  removeMediaButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaSelectionContainer: {
    padding: 20,
    gap: 16,
  },
  mediaButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    borderRadius: 16,
    gap: 12,
  },
  mediaButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  captionContainer: {
    padding: 20,
  },
  captionLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  captionInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  captionCount: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'right',
  },
  infoContainer: {
    padding: 20,
    gap: 12,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 12,
  },
  infoText: {
    fontSize: 14,
    fontWeight: '500',
  },
});