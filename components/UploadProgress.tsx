import React from 'react';
import { StyleSheet, View, Text, Modal, TouchableOpacity } from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';

interface UploadProgressProps {
  visible: boolean;
  fileName?: string;
  fileType: 'image' | 'video' | 'file';
  onCancel?: () => void;
  isDark?: boolean;
}

export default function UploadProgress({
  visible,
  fileName,
  fileType,
  onCancel,
  isDark: propIsDark
}: UploadProgressProps) {
  const colorScheme = useColorScheme();
  const isDark = propIsDark !== undefined ? propIsDark : colorScheme === 'dark';

  const getFileIcon = () => {
    switch (fileType) {
      case 'image':
        return 'photo';
      case 'video':
        return 'video';
      default:
        return 'doc';
    }
  };

  const getFileTypeText = () => {
    switch (fileType) {
      case 'image':
        return 'Image';
      case 'video':
        return 'Video';
      default:
        return 'File';
    }
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={styles.modalOverlay}>
        <View style={[
          styles.container,
          { 
            backgroundColor: isDark ? '#1a1a1a' : '#ffffff',
            shadowColor: isDark ? '#ffffff20' : '#00000030'
          }
        ]}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.fileInfo}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: isDark ? '#2a2a2a' : '#f0f0f0' }
              ]}>
                <IconSymbol 
                  name={getFileIcon()} 
                  size={24} 
                  color={isDark ? '#ffffff' : '#000000'} 
                />
              </View>
              <View style={styles.fileDetails}>
                <Text style={[
                  styles.fileType,
                  { color: isDark ? '#ffffff' : '#000000' }
                ]}>
                  {getFileTypeText()}
                </Text>
                {fileName && (
                  <Text style={[
                    styles.fileName,
                    { color: isDark ? '#8e8e93' : '#6c6c70' }
                  ]}>
                    {fileName}
                  </Text>
                )}
              </View>
            </View>
            {onCancel && (
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={onCancel}
                activeOpacity={0.7}
              >
                <IconSymbol name="xmark" size={20} color="#FF6B35" />
              </TouchableOpacity>
            )}
          </View>

          {/* Progress Section */}
          <View style={styles.progressSection}>
            <View style={styles.progressInfo}>
              <Text style={[
                styles.progressText,
                { color: isDark ? '#ffffff' : '#000000' }
              ]}>
                Uploading...
              </Text>
            </View>
            
            {/* Progress Bar */}
            <View style={[
              styles.progressBarContainer,
              { backgroundColor: isDark ? '#2a2a2a' : '#f0f0f0' }
            ]}>
              <View style={[
                styles.progressBarIndeterminate,
                { backgroundColor: '#FF6B35' }
              ]} />
            </View>
          </View>

          {/* Status */}
          <View style={styles.statusSection}>
            <IconSymbol 
              name="arrow.up.circle" 
              size={16} 
              color={isDark ? '#8e8e93' : '#6c6c70'} 
            />
            <Text style={[
              styles.statusText,
              { color: isDark ? '#8e8e93' : '#6c6c70' }
            ]}>
              Upload in progress
            </Text>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    width: '100%',
    maxWidth: 320,
    borderRadius: 20,
    padding: 20,
    elevation: 8,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  fileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  fileDetails: {
    flex: 1,
  },
  fileType: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  fileName: {
    fontSize: 13,
    fontWeight: '400',
  },
  cancelButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  progressSection: {
    marginBottom: 20,
  },
  progressInfo: {
    marginBottom: 12,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  progressBarContainer: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarIndeterminate: {
    height: '100%',
    borderRadius: 4,
    width: '30%',
    animation: 'indeterminateProgress 1.5s ease-in-out infinite',
  },
  statusSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
});