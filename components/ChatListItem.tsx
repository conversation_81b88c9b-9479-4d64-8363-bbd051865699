import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { ChatPreview } from '@/types/messenger';
import { format, isToday, isYesterday } from 'date-fns';
import { Image } from 'expo-image';
import React, { useEffect, useState } from 'react';
import {
    ActionSheetIOS,
    Alert,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { userService } from '@/services/userService';

interface ChatListItemProps {
  chat: ChatPreview;
  onPress: (chatId: string) => void;
  onDelete?: (chatId: string) => void;
  onMute?: (chatId: string) => void;
}

export default function ChatListItem({ chat, onPress, onDelete, onMute }: ChatListItemProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { user: currentUser } = useAuth();
  const [userData, setUserData] = useState<{name: string, avatar?: string} | null>(null);

  // For direct chats, fetch user data if it's not properly populated
  useEffect(() => {
    const fetchUserData = async () => {
      // Only fetch for direct chats that don't have proper name/avatar
      if (!chat.isGroup && (chat.name === 'Unknown User' || !chat.name)) {
        // Find the other participant (not the current user)
        const otherParticipant = chat.participants.find(p => p !== currentUser?.uid);
        if (otherParticipant) {
          try {
            const user = await userService.getUserById(otherParticipant);
            if (user) {
              setUserData({
                name: user.displayName || user.name || 'Unknown User',
                avatar: user.photoURL || user.avatar
              });
            }
          } catch (error) {
            console.error('Error fetching user data:', error);
          }
        }
      }
    };

    // Only fetch if we need to
    if (!chat.isGroup && (chat.name === 'Unknown User' || !chat.name || chat.name === '')) {
      fetchUserData();
    }
  }, [chat, currentUser]);

  const displayName = userData?.name || chat.name || 'Unknown User';
  const displayAvatar = userData?.avatar || chat.avatar || chat.photoURL;

  const formatTimestamp = (timestamp: Date) => {
    if (isToday(timestamp)) {
      return format(timestamp, 'HH:mm');
    } else if (isYesterday(timestamp)) {
      return 'Yesterday';
    } else {
      return format(timestamp, 'MMM d');
    }
  };

  const handleLongPress = () => {
    const options = ['Mute', 'Delete', 'Cancel'];
    const destructiveButtonIndex = 1;
    const cancelButtonIndex = 2;

    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options,
          destructiveButtonIndex,
          cancelButtonIndex,
        },
        (buttonIndex) => {
          if (buttonIndex === 0) {
            onMute?.(chat.id);
          } else if (buttonIndex === 1) {
            Alert.alert(
              'Delete Chat',
              `Are you sure you want to delete this chat with ${displayName}?`,
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Delete', style: 'destructive', onPress: () => onDelete?.(chat.id) },
              ]
            );
          }
        }
      );
    } else {
      Alert.alert(
        'Chat Options',
        '',
        [
          { text: 'Mute', onPress: () => onMute?.(chat.id) },
          { text: 'Delete', style: 'destructive', onPress: () => onDelete?.(chat.id) },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, { 
        backgroundColor: isDark ? 'rgba(26, 26, 26, 0.8)' : 'rgba(255, 255, 255, 0.9)',
        shadowColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 1,
        shadowRadius: 20,
        elevation: 1,
      }]}
      onPress={() => onPress(chat.id)}
      onLongPress={handleLongPress}
      activeOpacity={0.9}
    >
      <View style={styles.avatarContainer}>
        <View style={[styles.avatarWrapper, {
          shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.15)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 8,
          elevation: 4,
        }]}>
          {displayAvatar ? (
            <Image
              source={{ uri: displayAvatar }}
              style={styles.avatar}
            />
          ) : (
            <View style={[styles.avatar, styles.avatarPlaceholder]}>
              {displayName !== 'Unknown User' && (
                <Text style={styles.avatarText}>
                  {displayName.charAt(0).toUpperCase()}
                </Text>
              )}
            </View>
          )}
        </View>
        {!chat.isGroup && chat.isOnline && (
          <View style={[styles.onlineIndicator, {
            shadowColor: '#30d158',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.5,
            shadowRadius: 2,
            elevation: 2,
          }]} />
        )}
        {chat.isGroup && (
          <View style={[styles.groupIcon, {
            backgroundColor: '#FF6B35',
            shadowColor: '#FF6B35',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.3,
            shadowRadius: 2,
            elevation: 2,
          }]}>
            <IconSymbol name="person.2.fill" size={10} color="#fff" />
          </View>
        )}
      </View>
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={[styles.name, { 
            color: isDark ? '#ffffff' : '#000000',
            fontWeight: '700',
            letterSpacing: -0.3,
          }]} numberOfLines={1}>
            {displayName}
          </Text>
          <View style={styles.timestampContainer}>
            <Text style={[styles.timestamp, { 
              color: isDark ? 'rgba(142, 142, 147, 0.8)' : 'rgba(108, 108, 112, 0.8)',
              fontSize: 12,
              fontWeight: '600',
              letterSpacing: 0.2,
            }]}>
              {formatTimestamp(chat.timestamp)}
            </Text>
            {chat.unreadCount > 0 && (
              <View style={[styles.unreadBadge, {
                backgroundColor: '#FF6B35',
                shadowColor: '#FF6B35',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.3,
                shadowRadius: 4,
                elevation: 3,
              }]}>
                <Text style={styles.unreadText}>
                  {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                </Text>
              </View>
            )}
          </View>
        </View>
        
        <View style={styles.messageRow}>
          <Text
            style={[
              styles.lastMessage,
              { 
                color: isDark ? '#8e8e93' : '#6c6c70',
                fontWeight: chat.unreadCount > 0 ? '500' : '400',
                fontSize: 15,
              }
            ]}
            numberOfLines={1}
          >
            {chat.lastMessage}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
    marginVertical: 2,
    borderRadius: 20,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatarWrapper: {
    borderRadius: 28,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  avatarPlaceholder: {
    backgroundColor: '#FF6B35',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#30d158',
    borderWidth: 3,
    borderColor: '#fff',
  },
  groupIcon: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    borderRadius: 10,
    padding: 4,
    borderWidth: 2,
    borderColor: '#fff',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  name: {
    fontSize: 17,
    flex: 1,
  },
  timestampContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timestamp: {
    marginRight: 8,
  },
  unreadBadge: {
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  unreadText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '700',
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  lastMessage: {
    flex: 1,
    lineHeight: 20,
    fontSize: 15,
  },
});