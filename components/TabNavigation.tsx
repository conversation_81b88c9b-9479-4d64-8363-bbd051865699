import React from 'react';
import { Tabs, usePathname } from 'expo-router';
import { Platform } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { TabBarBackground } from '@/components/ui/TabBarBackground';
import { TabTransition } from '@/components/TabTransition';

interface TabNavigationProps {
  children: React.ReactNode;
}

const TabNavigation: React.FC<TabNavigationProps> = ({ children }) => {
  const pathname = usePathname();

  const getActiveTab = (path: string) => {
    if (path.includes('/chats') || path === '/(tabs)') return 'index';
    if (path.includes('/contacts')) return 'contacts';
    if (path.includes('/groups') || path.includes('/explore')) return 'explore';
    if (path.includes('/settings')) return 'settings';
    return 'index';
  };

  const activeTab = getActiveTab(pathname);

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#FF6B35',
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            position: 'absolute',
          },
          default: {},
        }),
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
        tabBarItemStyle: {
          paddingVertical: 4,
        },
        tabBarIconStyle: {
          marginBottom: 2,
        },
        sceneStyle: {
          backgroundColor: 'transparent',
        },
        animation: 'fade',
        animationTypeForReplace: 'push',
      }}
    >
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child)) {
          const tabName = child.props.name;
          const isActive = activeTab === tabName;
          
          return React.cloneElement(child as React.ReactElement<any>, {
            ...child.props,
            options: {
              ...child.props.options,
              animation: 'fade',
              animationTypeForReplace: 'push',
            },
          });
        }
        return child;
      })}
    </Tabs>
  );
};

export default TabNavigation;