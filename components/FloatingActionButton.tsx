import { IconSymbol } from '@/components/ui/IconSymbol';
import React from 'react';
import {
    Platform,
    StyleSheet,
    TouchableOpacity,
    ViewStyle,
} from 'react-native';

interface FloatingActionButtonProps {
  onPress: () => void;
  icon?: any;
  size?: number;
  style?: ViewStyle;
}

export default function FloatingActionButton({ 
  onPress, 
  icon = "plus.circle.fill", 
  size = 24,
  style 
}: FloatingActionButtonProps) {
  return (
    <TouchableOpacity
      style={[styles.fab, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <IconSymbol name={icon} size={size} color="#fff" />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    bottom: Platform.select({ ios: 90, android: 110 }),
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#0084FF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});