import React from 'react';
import { Modal, View, Image, StyleSheet, TouchableOpacity, Alert, Platform } from 'react-native';
import { IconSymbol } from './ui/IconSymbol';
import * as MediaLibrary from 'expo-media-library';
import { default as FileSystem } from 'expo-file-system';

interface ImageViewerProps {
  visible: boolean;
  imageUrl: string | null;
  onClose: () => void;
}

export default function ImageViewer({ visible, imageUrl, onClose }: ImageViewerProps) {
  const saveToGallery = async () => {
    if (!imageUrl) return;
    
    try {
      // Request media library permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please grant permission to save photos to your gallery.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'OK', onPress: () => MediaLibrary.requestPermissionsAsync() }
          ]
        );
        return;
      }

      // Download the image file directly to temporary location
      const fileUri = FileSystem.documentDirectory ? `${FileSystem.documentDirectory}image_${Date.now()}.jpg` : `${FileSystem.cacheDirectory || ''}image_${Date.now()}.jpg`;
      const downloadResumable = FileSystem.createDownloadResumable(
        imageUrl,
        fileUri
      );
      
      const { uri } = await downloadResumable.downloadAsync();
      
      // Save to gallery using the file URI
      const asset = await MediaLibrary.createAssetAsync(fileUri);
      
      // Clean up temporary file
      await FileSystem.deleteAsync(fileUri);
      
      Alert.alert(
        'Success',
        'Image saved to your gallery!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error saving image to gallery:', error);
      Alert.alert(
        'Error',
        'Failed to save image to gallery. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <Modal visible={visible} transparent={true} onRequestClose={onClose}>
      <View style={styles.container}>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <IconSymbol name="xmark" size={24} color="#fff" />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.saveButton} onPress={saveToGallery}>
          <IconSymbol name="arrow.down.circle" size={24} color="#fff" />
        </TouchableOpacity>
        
        {imageUrl && (
          <Image source={{ uri: imageUrl }} style={styles.image} resizeMode="contain" />
        )}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 1,
  },
  saveButton: {
    position: 'absolute',
    top: 40,
    right: 60,
    zIndex: 1,
  },
});
