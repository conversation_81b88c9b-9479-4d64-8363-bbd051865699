import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Notification, notificationApiService } from '@/services/notificationApiService';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Modal,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

interface NotificationPopupProps {
  visible: boolean;
  onClose: () => void;
}

export default function NotificationPopup({ 
  visible, 
  onClose
}: NotificationPopupProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      loadNotifications();
    }
  }, [visible]);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      // Updated to use the new Firestore-based service
      const notificationsData = await notificationApiService.getUserNotifications(5);
      setNotifications(notificationsData);
    } catch (error) {
      console.error('Error loading notifications:', error);
      // Set empty notifications array to handle the error gracefully
      setNotifications([]);
      
      // Show an error message in a non-disruptive way with a small delay
      setTimeout(() => {
        Alert.alert(
          'Network Error',
          'Unable to load notifications. Please check your connection and try again.',
          [{ text: 'OK' }]
        );
      }, 500);
    } finally {
      setLoading(false);
    }
  };

  const handleShowAllNotifications = () => {
    onClose();
    router.push('/notifications');
  };

  const handleNotificationPress = async (notification: Notification) => {
    // Mark as read using the new Firestore-based service
    try {
      await notificationApiService.markNotificationsAsRead([notification.id]);
      setNotifications(prev => prev.map(n => 
        n.id === notification.id ? {...n, read: true} : n
      ));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
    
    // Handle notification press based on type
    if (notification.data) {
      switch (notification.data.type) {
        case 'message':
          // Navigate to chat
          if (notification.chatId) {
            router.push(`/chat/${notification.chatId}`);
            onClose();
          }
          break;
        case 'contact_request':
          // Navigate to user profile with contact request context
          if (notification.senderId) {
            router.push(`/user-profile?userId=${notification.senderId}`);
            onClose();
          }
          break;
        case 'group_invite':
          // Navigate to group chat
          if (notification.chatId) {
            router.push(`/chat/${notification.chatId}`);
            onClose();
          }
          break;
        case 'contact_accepted':
          // Navigate to user profile
          if (notification.senderId) {
            router.push(`/user-profile?userId=${notification.senderId}`);
            onClose();
          }
          break;
        default:
          // For other notification types, just close the popup
          onClose();
          break;
      }
    } else {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity 
        style={styles.overlay} 
        onPress={onClose}
        activeOpacity={1}
      >
        <View style={[styles.container, {
          backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 1,
          shadowRadius: 20,
          elevation: 10,
        }]}>
          <View style={styles.header}>
            <Text style={[styles.headerTitle, {
              color: isDark ? '#ffffff' : '#000000',
            }]}>
              Notifications
            </Text>
            <TouchableOpacity onPress={onClose}>
              <IconSymbol name="xmark" size={20} color={isDark ? '#8e8e93' : '#8e8e93'} />
            </TouchableOpacity>
          </View>
          
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#FF6B35" />
            </View>
          ) : notifications.length > 0 ? (
            <ScrollView style={styles.notificationsContainer}>
              {notifications.map((notification) => (
                <TouchableOpacity
                  key={notification.id}
                  style={[styles.notificationItem, {
                    backgroundColor: isDark ? 'rgba(42, 42, 42, 0.6)' : 'rgba(240, 240, 240, 0.6)',
                  }]}
                  onPress={() => handleNotificationPress(notification)}
                >
                  <View style={styles.notificationContent}>
                    <Text style={[styles.notificationTitle, {
                      color: isDark ? '#ffffff' : '#000000',
                    }]}>
                      {notification.title}
                    </Text>
                    <Text style={[styles.notificationMessage, {
                      color: isDark ? 'rgba(142, 142, 147, 0.8)' : 'rgba(108, 108, 112, 0.8)',
                    }]} numberOfLines={2}>
                      {notification.body}
                    </Text>
                    <Text style={[styles.notificationTime, {
                      color: isDark ? 'rgba(142, 142, 147, 0.6)' : 'rgba(108, 108, 112, 0.6)',
                    }]}>
                      {new Date(notification.sentAt).toLocaleString()}
                    </Text>
                  </View>
                  {!notification.read && (
                    <View style={[styles.unreadIndicator, {
                      backgroundColor: '#FF6B35',
                    }]} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          ) : (
            <View style={styles.emptyContainer}>
              <IconSymbol name="bell.slash.fill" size={48} color={isDark ? '#8e8e93' : '#8e8e93'} />
              <Text style={[styles.emptyText, {
                color: isDark ? '#8e8e93' : '#8e8e93',
              }]}>
                No notifications
              </Text>
            </View>
          )}
          
          <TouchableOpacity
            style={[styles.showAllButton, {
              backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
            }]}
            onPress={handleShowAllNotifications}
          >
            <Text style={[styles.showAllText, {
              color: '#FF6B35',
            }]}>
              Show All Notifications
            </Text>
            <IconSymbol name="chevron.right" size={16} color="#FF6B35" />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingTop: 80,
    paddingRight: 24,
  },
  container: {
    width: 320,
    borderRadius: 20,
    maxHeight: 400,
    backdropFilter: 'blur(20px)',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(142, 142, 147, 0.2)',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: -0.2,
  },
  notificationsContainer: {
    maxHeight: 250,
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 12,
    marginVertical: 4,
    borderRadius: 16,
    position: 'relative',
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: 14,
    fontWeight: '400',
    marginBottom: 4,
    lineHeight: 20,
  },
  notificationTime: {
    fontSize: 12,
    fontWeight: '400',
  },
  unreadIndicator: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  showAllButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    margin: 12,
    borderRadius: 16,
  },
  showAllText: {
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
});