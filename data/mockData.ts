import { Chat, ChatPreview, Message, User } from '@/types/messenger';

export const currentUser: User = {
    id: 'current-user',
    name: 'You',
    email: '<EMAIL>',
    displayName: 'You',
    userId: '@you',
    isOnline: true,
    lastSeen: new Date(),
};

export const mockUsers: User[] = [
    {
        id: '1',
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        displayName: '<PERSON><PERSON>',
        userId: '@priya_sharma',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        isOnline: true,
        lastSeen: new Date(),
    },
    {
        id: '2',
        name: '<PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        displayName: '<PERSON><PERSON><PERSON>',
        userId: '@arjun_patel',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        isOnline: false,
        lastSeen: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    },
    {
        id: '3',
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        displayName: 'Ananya <PERSON>',
        userId: '@ananya_gupta',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        isOnline: true,
        lastSeen: new Date(),
    },
    {
        id: '4',
        name: 'Vikram Singh',
        email: '<EMAIL>',
        displayName: 'Vikram Singh',
        userId: '@vikram_singh',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        isOnline: false,
        lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    },
    {
        id: '5',
        name: 'Kavya Reddy',
        email: '<EMAIL>',
        displayName: 'Kavya Reddy',
        userId: '@kavya_reddy',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
        isOnline: true,
        lastSeen: new Date(),
    },
    {
        id: '6',
        name: 'Rohit Agarwal',
        email: '<EMAIL>',
        displayName: 'Rohit Agarwal',
        userId: '@rohit_agarwal',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
        isOnline: true,
        lastSeen: new Date(),
    },
    {
        id: '7',
        name: 'Meera Joshi',
        email: '<EMAIL>',
        displayName: 'Meera Joshi',
        userId: '@meera_joshi',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
        isOnline: false,
        lastSeen: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
    },
    {
        id: '8',
        name: 'Karan Malhotra',
        email: '<EMAIL>',
        displayName: 'Karan Malhotra',
        userId: '@karan_malhotra',
        avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
        isOnline: true,
        lastSeen: new Date(),
    },
];

export const mockMessages: Message[] = [
    {
        id: '1',
        chatId: '1',
        text: 'Namaste! How are you doing?',
        senderId: '1',
        senderName: 'Priya Sharma',
        senderEmail: '<EMAIL>',
        timestamp: new Date(Date.now() - 1000 * 60 * 5),
        type: 'text',
        reactions: {},
    },
    {
        id: '2',
        chatId: '1',
        text: 'I\'m doing great! Just finished the puja preparations 🙏',
        senderId: 'current-user',
        senderName: 'You',
        senderEmail: '<EMAIL>',
        timestamp: new Date(Date.now() - 1000 * 60 * 3),
        type: 'text',
        reactions: {},
    },
    {
        id: '3',
        chatId: '1',
        text: 'That\'s wonderful! May Lord Ganesha bless you!',
        senderId: '1',
        senderName: 'Priya Sharma',
        senderEmail: '<EMAIL>',
        timestamp: new Date(Date.now() - 1000 * 60 * 2),
        type: 'text',
        reactions: { '🙏': ['current-user'] },
    },
    {
        id: '4',
        chatId: '1',
        text: 'Thanks! Want to visit the temple later?',
        senderId: 'current-user',
        senderName: 'You',
        senderEmail: '<EMAIL>',
        timestamp: new Date(Date.now() - 1000 * 60 * 1),
        type: 'text',
        reactions: {},
    },
];

export const mockChats: Chat[] = [
    {
        id: '1',
        participants: ['current-user', '1'],
        participantDetails: {
            'current-user': { name: 'You', email: '<EMAIL>', photoURL: null },
            '1': { name: 'Priya Sharma', email: '<EMAIL>', photoURL: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face' }
        },
        type: 'direct',
        messages: mockMessages,
        lastMessage: {
            text: mockMessages[mockMessages.length - 1].text,
            senderId: mockMessages[mockMessages.length - 1].senderId,
            timestamp: mockMessages[mockMessages.length - 1].timestamp,
            type: mockMessages[mockMessages.length - 1].type
        },
        isGroup: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24),
        lastActivity: new Date(),
        unreadCount: { 'current-user': 0, '1': 0 },
    },
    {
        id: '2',
        name: 'Temple Committee',
        participants: ['current-user', '2', '3', '4'],
        participantDetails: {
            'current-user': { name: 'You', email: '<EMAIL>' },
            '2': { name: 'Arjun Patel', email: '<EMAIL>', photoURL: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face' },
            '3': { name: 'Ananya Gupta', email: '<EMAIL>', photoURL: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face' },
            '4': { name: 'Vikram Singh', email: '<EMAIL>', photoURL: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face' }
        },
        type: 'group',
        messages: [
            {
                id: 'group-1',
                chatId: '2',
                text: 'Aarti at 6 PM today at the temple',
                senderId: '2',
                senderName: 'Arjun Patel',
                senderEmail: '<EMAIL>',
                timestamp: new Date(Date.now() - 1000 * 60 * 30),
                type: 'text',
                reactions: {},
            },
            {
                id: 'group-2',
                chatId: '2',
                text: 'Om Namah Shivaya! I\'ll be there',
                senderId: '3',
                senderName: 'Ananya Gupta',
                senderEmail: '<EMAIL>',
                timestamp: new Date(Date.now() - 1000 * 60 * 25),
                type: 'text',
                reactions: {},
            },
            {
                id: 'group-3',
                chatId: '2',
                text: 'Should we bring prasadam?',
                senderId: 'current-user',
                senderName: 'You',
                senderEmail: '<EMAIL>',
                timestamp: new Date(Date.now() - 1000 * 60 * 20),
                type: 'text',
                reactions: {},
            },
        ],
        isGroup: true,
        photoURL: 'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=150&h=150&fit=crop',
        groupAvatar: 'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=150&h=150&fit=crop',
        createdBy: '2',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7),
        lastActivity: new Date(Date.now() - 1000 * 60 * 20),
        unreadCount: { 'current-user': 2, '2': 0, '3': 1, '4': 1 },
        lastMessage: {
            text: 'Should we bring prasadam?',
            senderId: 'current-user',
            timestamp: new Date(Date.now() - 1000 * 60 * 20),
            type: 'text',
        },
    },
    {
        id: '3',
        participants: ['current-user', '5'],
        participantDetails: {
            'current-user': { name: 'You', email: '<EMAIL>' },
            '5': { name: 'Kavya Reddy', email: '<EMAIL>', photoURL: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face' }
        },
        type: 'direct',
        messages: [
            {
                id: 'kavya-1',
                chatId: '3',
                text: 'Check out this beautiful temple!',
                senderId: '5',
                senderName: 'Kavya Reddy',
                senderEmail: '<EMAIL>',
                timestamp: new Date(Date.now() - 1000 * 60 * 60),
                type: 'image',
                imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
                imageUri: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
                reactions: {},
            },
            {
                id: 'kavya-2',
                chatId: '3',
                text: 'Har Har Mahadev! Beautiful darshan today!',
                senderId: '5',
                senderName: 'Kavya Reddy',
                senderEmail: '<EMAIL>',
                timestamp: new Date(Date.now() - 1000 * 60 * 58),
                type: 'text',
                reactions: {},
            },
        ],
        isGroup: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2),
        lastActivity: new Date(Date.now() - 1000 * 60 * 58),
        unreadCount: { 'current-user': 1, '5': 0 },
        lastMessage: {
            text: 'Har Har Mahadev! Beautiful darshan today!',
            senderId: '5',
            timestamp: new Date(Date.now() - 1000 * 60 * 58),
            type: 'text',
        },
    },
    {
        id: '4',
        participants: ['current-user', '2'],
        participantDetails: {
            'current-user': { name: 'You', email: '<EMAIL>' },
            '2': { name: 'Arjun Patel', email: '<EMAIL>', photoURL: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face' }
        },
        type: 'direct',
        messages: [
            {
                id: 'arjun-1',
                chatId: '4',
                text: 'Namaste! Are you free for satsang tomorrow?',
                senderId: '2',
                senderName: 'Arjun Patel',
                senderEmail: '<EMAIL>',
                timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
                type: 'text',
                reactions: {},
            },
        ],
        isGroup: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3),
        lastActivity: new Date(Date.now() - 1000 * 60 * 60 * 2),
        unreadCount: { 'current-user': 1, '2': 0 },
        lastMessage: {
            text: 'Namaste! Are you free for satsang tomorrow?',
            senderId: '2',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
            type: 'text',
        },
    },
    {
        id: '5',
        name: 'Sharma Family',
        participants: ['current-user', '1', '3'],
        participantDetails: {
            'current-user': { name: 'You', email: '<EMAIL>' },
            '1': { name: 'Priya Sharma', email: '<EMAIL>', photoURL: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face' },
            '3': { name: 'Ananya Gupta', email: '<EMAIL>', photoURL: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face' }
        },
        type: 'group',
        messages: [
            {
                id: 'family-1',
                chatId: '5',
                text: 'Don\'t forget about Diwali celebration on Sunday!',
                senderId: '1',
                senderName: 'Priya Sharma',
                senderEmail: '<EMAIL>',
                timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4),
                type: 'text',
                reactions: {},
            },
            {
                id: 'family-2',
                chatId: '5',
                text: '👍',
                senderId: '3',
                senderName: 'Ananya Gupta',
                senderEmail: '<EMAIL>',
                timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3),
                type: 'text',
                reactions: {},
            },
        ],
        isGroup: true,
        photoURL: 'https://images.unsplash.com/photo-1511895426328-dc8714191300?w=150&h=150&fit=crop',
        groupAvatar: 'https://images.unsplash.com/photo-1511895426328-dc8714191300?w=150&h=150&fit=crop',
        createdBy: '1',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30),
        lastActivity: new Date(Date.now() - 1000 * 60 * 60 * 3),
        unreadCount: { 'current-user': 0, '1': 0, '3': 0 },
        lastMessage: {
            text: '👍',
            senderId: '3',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3),
            type: 'text',
        },
    },
];

export const getChatPreviews = (): ChatPreview[] => {
    return mockChats.map(chat => {
        const otherParticipantId = chat.participants.find(p => p !== 'current-user');
        const otherParticipant = otherParticipantId ? chat.participantDetails[otherParticipantId] : null;
        const otherUser = otherParticipantId ? mockUsers.find(u => u.id === otherParticipantId) : null;
        
        return {
            id: chat.id,
            name: chat.isGroup ? chat.name! : (otherParticipant?.name || 'Unknown'),
            lastMessage: chat.lastMessage?.type === 'image' ? '📷 Photo' : chat.lastMessage?.text || '',
            lastMessageTime: chat.lastMessage?.timestamp || chat.createdAt,
            timestamp: chat.lastMessage?.timestamp || chat.createdAt,
            unreadCount: chat.unreadCount?.['current-user'] || 0,
            isGroup: chat.isGroup || false,
            participants: chat.participants,
            photoURL: chat.isGroup ? chat.groupAvatar : otherParticipant?.photoURL,
            avatar: chat.isGroup ? (chat.groupAvatar || undefined) : (otherParticipant?.photoURL || undefined), // Ensure undefined instead of null
            isOnline: chat.isGroup ? undefined : (otherUser?.isOnline || false),
        };
    });
};

export const getGroupPreviews = (): ChatPreview[] => {
    return mockChats
        .filter(chat => chat.isGroup)
        .map(chat => ({
            id: chat.id,
            name: chat.name!,
            lastMessage: chat.lastMessage?.type === 'image' ? '📷 Photo' : chat.lastMessage?.text || '',
            lastMessageTime: chat.lastMessage?.timestamp || chat.createdAt,
            timestamp: chat.lastMessage?.timestamp || chat.createdAt,
            unreadCount: chat.unreadCount?.['current-user'] || 0,
            isGroup: true,
            participants: chat.participants,
            photoURL: chat.groupAvatar,
            avatar: chat.groupAvatar || undefined, // Ensure undefined instead of null
            isOnline: undefined,
        }));
};