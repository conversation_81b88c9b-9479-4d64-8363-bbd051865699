# <PERSON><PERSON><PERSON> Messenger

A sacred messaging platform for the Hindu community built with React Native and Expo. Connect with fellow devotees, share spiritual moments, and celebrate dharmic traditions together.

## 🚀 Features

### Core Messaging
- **Real-time Chat Interface** - Smooth messaging experience with message bubbles
- **Group Chats** - Create and manage group conversations
- **Message Reactions** - Add emoji reactions to messages
- **Reply to Messages** - Quote and reply to specific messages
- **Image Sharing** - Send photos from camera or gallery
- **Typing Indicators** - Visual feedback when users are typing

### User Interface
- **Dark/Light Mode** - Automatic theme switching
- **Messenger Design** - Authentic Facebook Messenger look and feel
- **Smooth Animations** - Polished transitions and micro-interactions
- **Responsive Layout** - Works on all screen sizes

### Contact Management
- **People Directory** - Browse and search contacts
- **Online Status** - Real-time online/offline indicators
- **User Profiles** - Avatar images and status information

### Group Features
- **Group Creation** - Create groups with multiple participants
- **Group Management** - Add/remove members, set names and avatars
- **Member Lists** - View all group participants

### Advanced Features
- **Message Actions** - Long press for options (react, reply, copy, delete)
- **Chat Management** - Mute, delete, and organize conversations
- **Search Functionality** - Find chats and contacts quickly
- **Unread Badges** - Visual indicators for new messages
- **Smart Timestamps** - Contextual time display

## 📱 Screenshots

The app includes:
- Sacred chat interface with dharmic greetings
- Sangha (group) creation for spiritual communities
- Devotee directory with online presence
- Message reactions with spiritual emojis
- Temple committee and family group chats
- Hindu festival and celebration coordination

## 🛠 Tech Stack

- **React Native** - Cross-platform mobile development
- **Expo** - Development platform and tools
- **TypeScript** - Type-safe development
- **Expo Router** - File-based navigation
- **Expo Image** - Optimized image handling
- **date-fns** - Date formatting utilities
- **Expo Image Picker** - Camera and gallery access
- **Firebase** - Real-time database and authentication

## 🏗 Architecture

### Components
- `ChatHeader` - Reusable chat header with user info
- `MessageBubble` - Advanced message display with reactions
- `MessageInput` - Enhanced input with attachments
- `ChatListItem` - Rich chat preview with actions
- `SearchBar` - Reusable search component
- `TypingIndicator` - Animated typing feedback

### Data Structure
- Type-safe interfaces for Users, Messages, and Chats
- Real-time data with Firebase integration
- Frontend-backend architecture with Firebase services

## 🚀 Getting Started

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start the development server**
   ```bash
   npx expo start
   ```

3. **Run on device**
   - Scan QR code with Expo Go app
   - Or press `i` for iOS simulator
   - Or press `a` for Android emulator

## 📂 Project Structure

```
├── app/
│   ├── (tabs)/
│   │   ├── index.tsx          # Chat list screen
│   │   └── explore.tsx        # People/contacts screen
│   ├── chat/[id].tsx          # Individual chat screen
│   ├── create-group.tsx       # Group creation screen
│   └── people.tsx             # Standalone people screen
├── components/
│   ├── ChatHeader.tsx         # Chat header component
│   ├── MessageBubble.tsx      # Message display component
│   ├── MessageInput.tsx       # Message input component
│   ├── ChatListItem.tsx       # Chat list item component
│   └── SearchBar.tsx          # Search component
├── data/
│   └── mockData.ts            # Sample data (for reference only)
├── types/
│   └── messenger.ts           # TypeScript interfaces
└── README.md
```

## 🔮 Future Enhancements

- **WebRTC Integration** - Voice and video calling
- **Push Notifications** - Real-time message alerts
- **Message Encryption** - End-to-end encryption
- **File Sharing** - Documents and media sharing
- **Message Search** - Full-text search across conversations
- **Stories Feature** - Temporary photo/video sharing
- **Chat Themes** - Customizable chat colors and themes

## 🤝 Contributing

This is a community-focused messaging platform designed to bring Hindu devotees together. Built with devotion and respect for our sacred traditions. 🙏

**Features for the Hindu Community:**
- Dharmic greetings and spiritual conversations
- Temple committee coordination
- Festival celebration planning
- Satsang and spiritual gathering organization
- Family devotion sharing
- Sacred text and mantra sharing

## 📄 License

MIT License - feel free to use this code for learning and development purposes.