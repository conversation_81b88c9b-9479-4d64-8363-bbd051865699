# Firestore Index Fix

## Problem
The application was throwing a Firebase error when trying to query contact requests:
```
Error getting contact requests: FirebaseError: The query requires an index.
```

## Root Cause
Firestore requires composite indexes when combining equality filters (`where`) with ordering (`orderBy`). The [getContactRequests](file:///Users/<USER>/Documents/CodeBase/namoshkar-messenger-firebase/services/userService.ts#L151-L201) function was using two such queries:

1. Query incoming requests: `where('recipientId', '==', userId)` + `orderBy('createdAt', 'desc')`
2. Query outgoing requests: `where('senderId', '==', userId)` + `orderBy('createdAt', 'desc')`

## Solution
Added the required composite indexes to [firestore.indexes.json](file:///Users/<USER>/Documents/CodeBase/namoshkar-messenger-firebase/firestore.indexes.json):

```json
{
  "collectionGroup": "contactRequests",
  "queryScope": "COLLECTION",
  "fields": [
    {
      "fieldPath": "recipientId",
      "order": "ASCENDING"
    },
    {
      "fieldPath": "createdAt",
      "order": "DESCENDING"
    }
  ]
},
{
  "collectionGroup": "contactRequests",
  "queryScope": "COLLECTION",
  "fields": [
    {
      "fieldPath": "senderId",
      "order": "ASCENDING"
    },
    {
      "fieldPath": "createdAt",
      "order": "DESCENDING"
    }
  ]
}
```

## Deployment
When you deploy these changes, Firebase will automatically create the required indexes. You can also manually create them through the Firebase Console if needed.

## Error Handling
Added improved error handling in the code to provide better feedback when index errors occur.

## Verification
After deploying these changes:
1. The Firebase indexes will be created automatically
2. The contact request functionality should work without errors
3. Users should be able to send, receive, and manage contact requests properly