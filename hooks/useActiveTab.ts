import { usePathname } from 'expo-router';
import { useState, useEffect } from 'react';

export const useActiveTab = () => {
  const pathname = usePathname();
  const [activeTab, setActiveTab] = useState('index');

  useEffect(() => {
    const determineActiveTab = (path: string) => {
      if (path.includes('/chats') || path === '/(tabs)') return 'index';
      if (path.includes('/stories')) return 'stories';
      if (path.includes('/contacts')) return 'contacts';
      if (path.includes('/groups') || path.includes('/explore')) return 'explore';
      if (path.includes('/settings')) return 'settings';
      return 'index';
    };

    setActiveTab(determineActiveTab(pathname));
  }, [pathname]);

  return activeTab;
};