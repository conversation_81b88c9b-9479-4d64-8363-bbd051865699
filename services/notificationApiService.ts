import { addDoc, collection, deleteDoc, doc, getDoc, getDocs, query, updateDoc, where, writeBatch } from 'firebase/firestore';
import { authService } from './authService';
import { db } from '../config/firebase';

export interface Notification {
  id: string;
  recipientId: string;
  senderId: string;
  senderName?: string;
  title: string;
  body: string;
  data?: any;
  imageUrl?: string;
  chatId?: string;
  sentAt: Date;
  delivered: boolean;
  read: boolean;
}

class NotificationApiService {
  // Create a notification
  async createNotification(
    recipientId: string,
    senderId: string,
    title: string,
    body: string,
    data?: any,
    imageUrl?: string,
    chatId?: string
  ): Promise<string> {
    try {
      const notification = {
        recipientId,
        senderId,
        title,
        body,
        data: data || {},
        imageUrl: imageUrl || null,
        chatId: chatId || null,
        sentAt: new Date(),
        delivered: false,
        read: false,
      };

      const docRef = await addDoc(collection(db, 'notifications'), notification);
      return docRef.id;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Get user's notifications
  async getUserNotifications(limit: number = 50): Promise<Notification[]> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const notificationsQuery = query(
        collection(db, 'notifications'),
        where('recipientId', '==', currentUser.uid),
        // orderBy('sentAt', 'desc'), // Requires index
      );

      const snapshot = await getDocs(notificationsQuery);
      
      const notifications: Notification[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        notifications.push({
          id: doc.id,
          recipientId: data.recipientId,
          senderId: data.senderId,
          senderName: data.senderName,
          title: data.title,
          body: data.body,
          data: data.data,
          imageUrl: data.imageUrl,
          chatId: data.chatId,
          sentAt: data.sentAt.toDate(),
          delivered: data.delivered,
          read: data.read,
        });
      });

      // Sort by sentAt descending (newest first)
      notifications.sort((a, b) => (b.sentAt?.getTime() || 0) - (a.sentAt?.getTime() || 0));
      
      return notifications.slice(0, limit);
    } catch (error) {
      console.error('Error getting user notifications:', error);
      return [];
    }
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    try {
      await updateDoc(doc(db, 'notifications', notificationId), {
        read: true,
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // Get unread notifications count (alias for getUnreadCount)
  async getUnreadNotificationsCount(): Promise<number> {
    return await this.getUnreadCount();
  }

  // Mark notifications as read
  async markNotificationsAsRead(notificationIds: string[]): Promise<void> {
    try {
      const batch = writeBatch(db);
      
      for (const id of notificationIds) {
        const notificationRef = doc(db, 'notifications', id);
        batch.update(notificationRef, { read: true });
      }
      
      await batch.commit();
    } catch (error) {
      console.error('Error marking notifications as read:', error);
      throw error;
    }
  }

  // Mark all notifications as read
  async markAllNotificationsAsRead(): Promise<void> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get all unread notifications for the current user
      const unreadQuery = query(
        collection(db, 'notifications'),
        where('recipientId', '==', currentUser.uid),
        where('read', '==', false)
      );

      const snapshot = await getDocs(unreadQuery);
      
      // Use batch update for better performance
      const batch = writeBatch(db);
      snapshot.forEach((doc) => {
        batch.update(doc.ref, { read: true });
      });
      
      await batch.commit();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  // Delete notification
  async deleteNotification(notificationId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'notifications', notificationId));
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  // Delete all notifications for the current user
  async deleteAllNotifications(): Promise<void> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get all notifications for the current user
      const userNotificationsQuery = query(
        collection(db, 'notifications'),
        where('recipientId', '==', currentUser.uid)
      );

      const snapshot = await getDocs(userNotificationsQuery);
      
      // Use batch delete for better performance
      const batch = writeBatch(db);
      snapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });
      
      await batch.commit();
    } catch (error) {
      console.error('Error deleting all notifications:', error);
      throw error;
    }
  }

  // Get unread notifications count
  async getUnreadCount(): Promise<number> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        return 0;
      }

      // First try a simple query without orderBy to avoid index requirement
      const unreadQuery = query(
        collection(db, 'notifications'),
        where('recipientId', '==', currentUser.uid),
        where('read', '==', false)
      );

      try {
        const snapshot = await getDocs(unreadQuery);
        return snapshot.size;
      } catch (innerError) {
        console.error('Permission error getting unread notifications count:', innerError);
        // Return 0 as a fallback to prevent UI errors
        return 0;
      }
    } catch (error) {
      console.error('Error getting unread notifications count from Firestore:', error);
      return 0;
    }
  }

  // Create a test notification (for testing purposes)
  async createTestNotification(): Promise<boolean> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        console.error('No authenticated user');
        return false;
      }

      // Create a test notification for the current user
      const notification = {
        recipientId: currentUser.uid,
        senderId: currentUser.uid, // Self-notification for testing
        title: 'Test Notification',
        body: 'This is a test notification to verify the notification system is working correctly.',
        data: {
          type: 'test',
          timestamp: new Date().toISOString(),
        },
        imageUrl: null,
        chatId: null,
        sentAt: new Date(),
        delivered: true,
        read: false,
      };

      await addDoc(collection(db, 'notifications'), notification);
      console.log('Test notification created successfully');
      return true;
    } catch (error) {
      console.error('Error creating test notification:', error);
      return false;
    }
  }
}

// Export singleton instance
export const notificationApiService = new NotificationApiService();
export default notificationApiService;