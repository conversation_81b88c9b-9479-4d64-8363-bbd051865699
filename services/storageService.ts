import { getDownloadURL, ref, uploadBytes } from 'firebase/storage';
import { storage, getAuth } from '../config/firebase';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import * as VideoThumbnails from 'expo-video-thumbnails';
import { Image } from 'expo-image';

class StorageService {
  /**
   * Uploads an avatar image to Firebase Storage and returns the download URL
   * @param uri - The local URI of the image file
   * @param fileName - The name to save the file as (optional)
   * @returns Promise<string> - The download URL of the uploaded image
   */
  async uploadAvatar(uri: string, fileName?: string): Promise<string> {
    try {
      // Get the current user
      const auth = await getAuth();
      const user = auth ? auth.currentUser : null;
      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Generate a file name if not provided
      const finalFileName = fileName || this.generateFileName(uri);
      
      // Optimize the image for avatar
      const optimizedImage = await this.optimizeImageForAvatar(uri);
      
      // Create a reference to the file location in Firebase Storage
      const storageRef = ref(storage, `avatars/${user.uid}/${finalFileName}`);
      
      // Upload the optimized file to Firebase Storage
      const snapshot = await uploadBytes(storageRef, optimizedImage);
      
      // Get the download URL
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      return downloadURL;
    } catch (error) {
      console.error('Error uploading avatar:', error);
      throw new Error('Failed to upload avatar');
    }
  }

  /**
   * Uploads a chat image to Firebase Storage and returns the download URL with dimensions
   * @param uri - The local URI of the image file
   * @param fileName - The name to save the file as (optional)
   * @param onProgress - Progress callback function (optional)
   * @returns Promise<{url: string, width: number, height: number}> - The download URL and dimensions of the uploaded image
   */
  async uploadChatImage(uri: string, fileName?: string, onProgress?: (progress: number) => void): Promise<{url: string, width: number, height: number}> {
    try {
      // Get the current user
      const auth = await getAuth();
      const user = auth ? auth.currentUser : null;
      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Generate a file name if not provided
      const finalFileName = fileName || this.generateFileName(uri);
      
      // Optimize the image for chat and get dimensions
      const optimizedImage = await this.optimizeImageForChat(uri);
      const imageDimensions = await this.getImageDimensions(uri);
      
      // Create a reference to the file location in Firebase Storage
      const storageRef = ref(storage, `chat-images/${user.uid}/${finalFileName}`);
      
      // Upload image using regular uploadBytes
      const snapshot = await uploadBytes(storageRef, optimizedImage);
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      // Call progress callback to indicate completion
      onProgress?.();
      
      return {
        url: downloadURL,
        width: imageDimensions.width,
        height: imageDimensions.height
      };
    } catch (error) {
      console.error('Error uploading chat image:', error);
      throw new Error('Failed to upload chat image');
    }
  }

  /**
   * Uploads an optimized story image to Firebase Storage and returns the download URL
   * @param uri - The local URI of the image file
   * @param fileName - The name to save the file as (optional)
   * @returns Promise<string> - The download URL of the uploaded image
   */
  async uploadStoryImage(uri: string, fileName?: string): Promise<string> {
    try {
      // Get the current user
      const auth = await getAuth();
      const user = auth ? auth.currentUser : null;
      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Generate a file name if not provided
      const finalFileName = fileName || this.generateFileName(uri);
      
      // Optimize the image for stories
      const optimizedImage = await this.optimizeImageForStory(uri);
      
      // Create a reference to the file location in Firebase Storage
      const storageRef = ref(storage, `stories/${user.uid}/${finalFileName}`);
      
      // Upload the optimized file to Firebase Storage
      const snapshot = await uploadBytes(storageRef, optimizedImage);
      
      // Get the download URL
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      return downloadURL;
    } catch (error) {
      console.error('Error uploading story image:', error);
      throw new Error('Failed to upload story image');
    }
  }

  /**
   * Optimizes an image for story upload by resizing and compressing
   * @param uri - The local URI of the image file
   * @returns Promise<Blob> - The optimized image blob
   */
  private async optimizeImageForStory(uri: string): Promise<Blob> {
    try {
      // Get original file size for comparison
      const originalResponse = await fetch(uri);
      const originalBlob = await originalResponse.blob();
      const originalSize = originalBlob.size;
      
      // For stories, we want to optimize for file size while maintaining aspect ratio
      // Set maximum dimensions - images larger than this will be resized
      const MAX_WIDTH = 1080;
      const MAX_HEIGHT = 1920;
      
      // Use expo-image-manipulator to resize and compress
      // The resize operation automatically maintains aspect ratio
      const optimizedResult = await manipulateAsync(
        uri,
        [
          {
            resize: {
              width: MAX_WIDTH,
              height: MAX_HEIGHT,
            },
          },
        ],
        {
          compress: 0.7, // 70% quality for good file size reduction
          format: SaveFormat.JPEG,
        }
      );
      
      // Convert back to blob and get optimized size
      const optimizedResponse = await fetch(optimizedResult.uri);
      const optimizedBlob = await optimizedResponse.blob();
      const optimizedSize = optimizedBlob.size;
      
      // Calculate size reduction percentage
      const sizeReduction = ((originalSize - optimizedSize) / originalSize * 100).toFixed(1);
      
      console.log(`📸 Story image optimized: ${(originalSize / 1024 / 1024).toFixed(2)}MB -> ${(optimizedSize / 1024 / 1024).toFixed(2)}MB (${sizeReduction}% reduction)`);
      
      return optimizedBlob;
    } catch (error) {
      console.error('Error optimizing story image:', error);
      // Fallback to original image if optimization fails
      const response = await fetch(uri);
      return await response.blob();
    }
  }

  /**
   * Optimizes an image for avatar upload by resizing and compressing
   * @param uri - The local URI of the image file
   * @returns Promise<Blob> - The optimized image blob
   */
  private async optimizeImageForAvatar(uri: string): Promise<Blob> {
    try {
      // Get original file size for comparison
      const originalResponse = await fetch(uri);
      const originalBlob = await originalResponse.blob();
      const originalSize = originalBlob.size;
      
      // For avatars, we want smaller dimensions and higher compression
      // Set maximum dimensions - avatars should be small and square
      const MAX_SIZE = 400;
      
      // Use expo-image-manipulator to resize and compress
      // The resize operation automatically maintains aspect ratio
      const optimizedResult = await manipulateAsync(
        uri,
        [
          {
            resize: {
              width: MAX_SIZE,
              height: MAX_SIZE,
            },
          },
        ],
        {
          compress: 0.6, // 60% quality for avatars (smaller file size)
          format: SaveFormat.JPEG,
        }
      );
      
      // Convert back to blob and get optimized size
      const optimizedResponse = await fetch(optimizedResult.uri);
      const optimizedBlob = await optimizedResponse.blob();
      const optimizedSize = optimizedBlob.size;
      
      // Calculate size reduction percentage
      const sizeReduction = ((originalSize - optimizedSize) / originalSize * 100).toFixed(1);
      
      console.log(`👤 Avatar optimized: ${(originalSize / 1024 / 1024).toFixed(2)}MB -> ${(optimizedSize / 1024 / 1024).toFixed(2)}MB (${sizeReduction}% reduction)`);
      
      return optimizedBlob;
    } catch (error) {
      console.error('Error optimizing avatar:', error);
      // Fallback to original image if optimization fails
      const response = await fetch(uri);
      return await response.blob();
    }
  }

  /**
   * Optimizes an image for chat upload by resizing and compressing
   * @param uri - The local URI of the image file
   * @returns Promise<Blob> - The optimized image blob
   */
  private async optimizeImageForChat(uri: string): Promise<Blob> {
    try {
      // Get original file size for comparison
      const originalResponse = await fetch(uri);
      const originalBlob = await originalResponse.blob();
      const originalSize = originalBlob.size;
      
      // For chat images, balance between quality and file size
      // Set maximum dimensions - larger than avatars but smaller than stories
      const MAX_WIDTH = 1200;
      const MAX_HEIGHT = 1200;
      
      // Use expo-image-manipulator to resize and compress
      // The resize operation automatically maintains aspect ratio
      const optimizedResult = await manipulateAsync(
        uri,
        [
          {
            resize: {
              width: MAX_WIDTH,
              height: MAX_HEIGHT,
            },
          },
        ],
        {
          compress: 0.75, // 75% quality for chat images (good balance)
          format: SaveFormat.JPEG,
        }
      );
      
      // Convert back to blob and get optimized size
      const optimizedResponse = await fetch(optimizedResult.uri);
      const optimizedBlob = await optimizedResponse.blob();
      const optimizedSize = optimizedBlob.size;
      
      // Calculate size reduction percentage
      const sizeReduction = ((originalSize - optimizedSize) / originalSize * 100).toFixed(1);
      
      console.log(`💬 Chat image optimized: ${(originalSize / 1024 / 1024).toFixed(2)}MB -> ${(optimizedSize / 1024 / 1024).toFixed(2)}MB (${sizeReduction}% reduction)`);
      
      return optimizedBlob;
    } catch (error) {
      console.error('Error optimizing chat image:', error);
      // Fallback to original image if optimization fails
      const response = await fetch(uri);
      return await response.blob();
    }
  }

  /**
   * Uploads a video to Firebase Storage and returns the download URL with optimization
   * @param uri - The local URI of the video file
   * @param fileName - The name to save the file as (optional)
   * @returns Promise<{videoUrl: string, thumbnailUrl: string, duration: number, size: number}> - Video info with URLs
   */
  async uploadChatVideo(uri: string, fileName?: string, onProgress?: (progress: number) => void): Promise<{
    videoUrl: string;
    thumbnailUrl: string;
    duration: number;
    size: number;
    width: number;
    height: number;
  }> {
    try {
      console.log('🎥 uploadChatVideo called with URI:', uri);
      
      // Get the current user
      const auth = await getAuth();
      const user = auth ? auth.currentUser : null;
      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Validate video file
      const videoValidation = await this.validateVideoFile(uri);
      if (!videoValidation.isValid) {
        throw new Error(videoValidation.error || 'Video validation failed');
      }

      console.log(`🎥 Video validated: ${videoValidation.duration}s, ${(videoValidation.size / 1024 / 1024).toFixed(2)}MB`);

      // Generate file names
      const videoFileName = fileName || this.generateVideoFileName();
      const thumbnailFileName = `thumb_${videoFileName}`;
      
      // Generate thumbnail and get video dimensions
      const thumbnailBlob = await this.generateVideoThumbnail(uri);
      const videoDimensions = await this.getVideoDimensions(uri);
      
      // Upload thumbnail
      const thumbnailRef = ref(storage, `chat-videos/${user.uid}/${thumbnailFileName}`);
      const thumbnailSnapshot = await uploadBytes(thumbnailRef, thumbnailBlob);
      const thumbnailUrl = await getDownloadURL(thumbnailSnapshot.ref);
      
      // Upload video (original file since iOS doesn't allow video manipulation)
      const videoResponse = await fetch(uri);
      const videoBlob = await videoResponse.blob();
      
      const videoRef = ref(storage, `chat-videos/${user.uid}/${videoFileName}`);
      
      // Upload video using regular uploadBytes
      const videoSnapshot = await uploadBytes(videoRef, videoBlob);
      const videoUrl = await getDownloadURL(videoSnapshot.ref);
      
      // Call progress callback to indicate completion
      onProgress?.();
      
      console.log(`📹 Video uploaded: ${(videoBlob.size / 1024 / 1024).toFixed(2)}MB`);
      
      return {
        videoUrl,
        thumbnailUrl,
        duration: videoValidation.duration,
        size: videoBlob.size,
        width: videoDimensions.width,
        height: videoDimensions.height
      };
    } catch (error) {
      console.error('Error uploading chat video:', error);
      throw new Error('Failed to upload chat video');
    }
  }

  /**
   * Validates a video file for size and duration limits
   * @param uri - The local URI of the video file
   * @returns Promise<{isValid: boolean, duration: number, size: number, error?: string}> - Validation result
   */
  private async validateVideoFile(uri: string): Promise<{
    isValid: boolean;
    duration: number;
    size: number;
    error?: string;
  }> {
    try {
      // Get video file size
      const response = await fetch(uri);
      const blob = await response.blob();
      const size = blob.size;
      
      // Size limits: 100MB max
      const MAX_SIZE = 100 * 1024 * 1024; // 100MB
      if (size > MAX_SIZE) {
        return {
          isValid: false,
          duration: 0,
          size,
          error: 'Video file too large. Maximum size is 100MB.'
        };
      }
      
      // Get video duration (iOS limitation: can't get duration without AVAsset)
      // For now, we'll set a default duration and let the frontend handle actual duration
      const duration = 30; // Default 30 seconds (will be updated by frontend)
      
      // Duration limits: 60 seconds max
      const MAX_DURATION = 60; // 60 seconds
      if (duration > MAX_DURATION) {
        return {
          isValid: false,
          duration,
          size,
          error: 'Video too long. Maximum duration is 60 seconds.'
        };
      }
      
      return {
        isValid: true,
        duration,
        size
      };
    } catch (error) {
      console.error('Error validating video:', error);
      return {
        isValid: false,
        duration: 0,
        size: 0,
        error: 'Failed to validate video file'
      };
    }
  }

  /**
   * Generates a thumbnail from a video file
   * @param uri - The local URI of the video file
   * @returns Promise<Blob> - The thumbnail image blob
   */
  private async generateVideoThumbnail(uri: string): Promise<Blob> {
    try {
      // Generate thumbnail using expo-video-thumbnails
      const thumbnail = await VideoThumbnails.getThumbnailAsync(uri, {
        time: 1000, // 1 second into the video
        quality: 0.6
      });
      
      // Optimize the thumbnail
      const optimizedThumbnail = await this.optimizeImageForChat(thumbnail.uri);
      
      return optimizedThumbnail;
    } catch (error) {
      console.error('Error generating video thumbnail:', error);
      throw new Error('Failed to generate video thumbnail');
    }
  }

  /**
   * Uploads an image to Firebase Storage and returns the download URL (legacy method)
   * @param uri - The local URI of the image file
   * @param fileName - The name to save the file as (optional)
   * @returns Promise<string> - The download URL of the uploaded image
   */
  async uploadImage(uri: string, fileName?: string): Promise<string> {
    // Default to avatar upload for backward compatibility
    return this.uploadAvatar(uri, fileName);
  }

  /**
   * Generates a unique file name based on timestamp
   * @param uri - The URI of the file
   * @returns string - A unique file name
   */
  private generateFileName(uri: string): string {
    const timestamp = new Date().getTime();
    const fileExtension = this.getFileExtension(uri);
    return `avatar_${timestamp}.${fileExtension}`;
  }

  /**
   * Generates a unique video file name based on timestamp
   * @returns string - A unique video file name
   */
  private generateVideoFileName(): string {
    const timestamp = new Date().getTime();
    return `video_${timestamp}.mp4`;
  }

  /**
   * Extracts the file extension from a URI
   * @param uri - The URI of the file
   * @returns string - The file extension
   */
  private getFileExtension(uri: string): string {
    // Try to get extension from URI
    const match = uri.match(/\.([^.]+)$/);
    return match ? match[1] : 'jpg';
  }

  /**
   * Extracts image dimensions from a URI
   * @param uri - The URI of the image file
   * @returns Promise<{width: number, height: number}> - The image dimensions
   */
  private async getImageDimensions(uri: string): Promise<{width: number, height: number}> {
    try {
      // Use Image component to get dimensions
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          resolve({
            width: img.naturalWidth || img.width || 1200,
            height: img.naturalHeight || img.height || 1200
          });
        };
        img.onerror = () => {
          // Fallback to default dimensions
          resolve({ width: 1200, height: 1200 });
        };
        img.src = uri;
      });
    } catch (error) {
      console.error('Error getting image dimensions:', error);
      // Fallback to default dimensions
      return { width: 1200, height: 1200 };
    }
  }

  /**
   * Extracts video dimensions from a URI
   * @param uri - The URI of the video file
   * @returns Promise<{width: number, height: number}> - The video dimensions
   */
  private async getVideoDimensions(uri: string): Promise<{width: number, height: number}> {
    try {
      // For mobile apps, we can use expo-video to get video info
      // For now, return default 16:9 dimensions
      // This can be enhanced later with actual video metadata extraction
      return { width: 1280, height: 720 }; // Default 16:9
    } catch (error) {
      console.error('Error getting video dimensions:', error);
      // Fallback to default 16:9 dimensions
      return { width: 1280, height: 720 };
    }
  }
}

// Export singleton instance
export const storageService = new StorageService();
export default storageService;