import { io, Socket } from 'socket.io-client';
import { authService } from './authService';

export interface SocketMessage {
  id: string;
  chatId: string;
  senderId: string;
  senderEmail: string;
  text: string;
  type: 'text' | 'image' | 'file';
  timestamp: Date;
  replyTo?: string;
  reactions: { [emoji: string]: string[] };
  edited?: boolean;
  editedAt?: Date | null;
}

export interface TypingUser {
  userId: string;
  userEmail: string;
  chatId: string;
}

export interface UserPresence {
  userId: string;
  isOnline: boolean;
  lastSeen: Date;
}

class WebSocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private eventListeners: Map<string, Function[]> = new Map();

  // Initialize WebSocket connection
  async connect(): Promise<boolean> {
    try {
      const token = await authService.getAuthToken();
      if (!token) {
        console.error('No auth token available for WebSocket connection');
        return false;
      }

      const socketUrl = process.env.EXPO_PUBLIC_WEBSOCKET_URL || 'http://localhost:3002';
      
      this.socket = io(socketUrl, {
        auth: { token },
        transports: ['websocket', 'polling'],
        timeout: 20000,
      });

      this.setupEventListeners();
      
      return new Promise((resolve) => {
        this.socket!.on('connect', () => {
          console.log('✅ WebSocket connected');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.emit('connected');
          resolve(true);
        });

        this.socket!.on('connect_error', (error) => {
          console.error('❌ WebSocket connection error:', error);
          this.isConnected = false;
          this.emit('connection_error', error);
          resolve(false);
        });
      });

    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
      return false;
    }
  }

  // Disconnect WebSocket
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      console.log('🔌 WebSocket disconnected');
    }
  }

  // Setup event listeners
  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('disconnect', (reason) => {
      console.log('🔌 WebSocket disconnected:', reason);
      this.isConnected = false;
      this.emit('disconnected', reason);
      
      // Auto-reconnect if not a manual disconnect
      if (reason !== 'io client disconnect') {
        this.handleReconnection();
      }
    });

    this.socket.on('reconnect', () => {
      console.log('🔄 WebSocket reconnected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.emit('reconnected');
    });

    // Message events
    this.socket.on('new-message', (message: SocketMessage) => {
      this.emit('new-message', {
        ...message,
        timestamp: new Date(message.timestamp)
      });
    });

    this.socket.on('message-error', (error) => {
      this.emit('message-error', error);
    });

    // Typing events
    this.socket.on('user-typing', (data: TypingUser) => {
      this.emit('user-typing', data);
    });

    this.socket.on('user-stopped-typing', (data: TypingUser) => {
      this.emit('user-stopped-typing', data);
    });

    // Reaction events
    this.socket.on('reaction-updated', (data) => {
      this.emit('reaction-updated', data);
    });

    this.socket.on('reaction-error', (error) => {
      this.emit('reaction-error', error);
    });

    // Presence events
    this.socket.on('user-presence-changed', (data: UserPresence) => {
      this.emit('user-presence-changed', {
        ...data,
        lastSeen: new Date(data.lastSeen)
      });
    });

    // Notification events
    this.socket.on('notification', (notification) => {
      this.emit('notification', notification);
    });
  }

  // Handle reconnection logic
  private handleReconnection(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff
      
      console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`);
      
      setTimeout(() => {
        this.connect();
      }, delay);
    } else {
      console.error('❌ Max reconnection attempts reached');
      this.emit('max-reconnect-attempts-reached');
    }
  }

  // Join a chat room
  joinChat(chatId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('join-chat', chatId);
    }
  }

  // Leave a chat room
  leaveChat(chatId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave-chat', chatId);
    }
  }

  // Send a message
  sendMessage(data: {
    chatId: string;
    text: string;
    type: 'text' | 'image' | 'file';
    replyTo?: string;
  }): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('send-message', data);
    } else {
      console.error('Cannot send message: WebSocket not connected');
      this.emit('message-error', { error: 'Not connected to server' });
    }
  }

  // Start typing indicator
  startTyping(chatId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('typing-start', chatId);
    }
  }

  // Stop typing indicator
  stopTyping(chatId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('typing-stop', chatId);
    }
  }

  // Add reaction to message
  addReaction(messageId: string, reaction: string, chatId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('add-reaction', { messageId, reaction, chatId });
    }
  }

  // Update user presence
  updatePresence(isOnline: boolean): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('update-presence', isOnline);
    }
  }

  // Event listener management
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback?: Function): void {
    if (!this.eventListeners.has(event)) return;
    
    if (callback) {
      const listeners = this.eventListeners.get(event)!;
      const index = listeners.indexOf(callback);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    } else {
      this.eventListeners.set(event, []);
    }
  }

  private emit(event: string, data?: any): void {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event)!.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event}:`, error);
        }
      });
    }
  }

  // Get connection status
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  // Get socket instance (for advanced usage)
  getSocket(): Socket | null {
    return this.socket;
  }
}

// Export singleton instance
export const webSocketService = new WebSocketService();
export default webSocketService;