import {
  addDoc,
  arrayUnion,
  collection,
  deleteDoc,
  doc,
  getDocs,
  onSnapshot,
  query,
  Timestamp,
  updateDoc,
  where,
  orderBy,
  limit,
  getDoc
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { authService } from './authService';
import { userService } from './userService';
import { storageService } from './storageService';
import { Story, StoryUploadData, StoryViewer, StoryStats } from '../types/story';

class StoryService {
  private storiesCollection = collection(db, 'stories');

  // Create a new story
  async createStory(storyData: StoryUploadData): Promise<string> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Upload media to Firebase Storage
      const mediaUrl = storyData.mediaType === 'image' 
        ? await storageService.uploadStoryImage(storyData.mediaUri, `story_${Date.now()}`)
        : await this.uploadVideo(storyData.mediaUri, `story_${Date.now()}`);

      // Get user profile for avatar and name
      const userProfile = await userService.getUserById(currentUser.uid);
      
      // Calculate expiration time (24 hours from now)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      // Create story document
      const storyDataToSave = {
        userId: currentUser.uid,
        userName: userProfile?.displayName || currentUser.displayName || 'Unknown User',
        userAvatar: userProfile?.photoURL || currentUser.photoURL || '',
        mediaUrl,
        mediaType: storyData.mediaType,
        caption: storyData.caption || '',
        timestamp: Timestamp.now(),
        expiresAt: Timestamp.fromDate(expiresAt),
        viewedBy: [],
        isVisibleTo: storyData.visibleTo,
        createdAt: Timestamp.now(),
      };

      const docRef = await addDoc(this.storiesCollection, storyDataToSave);
      return docRef.id;
    } catch (error) {
      console.error('Error creating story:', error);
      throw error;
    }
  }

  // Get stories for current user (from contacts)
  async getUserStories(userId: string): Promise<Story[]> {
    try {
      const q = query(
        this.storiesCollection,
        where('isVisibleTo', 'array-contains', userId),
        where('expiresAt', '>', Timestamp.now()),
        orderBy('timestamp', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const stories: Story[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        stories.push({
          id: doc.id,
          userId: data.userId,
          userName: data.userName,
          userAvatar: data.userAvatar,
          mediaUrl: data.mediaUrl,
          mediaType: data.mediaType,
          caption: data.caption,
          timestamp: data.timestamp ? data.timestamp.toDate() : new Date(),
          expiresAt: data.expiresAt ? data.expiresAt.toDate() : new Date(Date.now() + 24 * 60 * 60 * 1000),
          viewedBy: data.viewedBy || [],
          isVisibleTo: data.isVisibleTo,
          createdAt: data.createdAt ? data.createdAt.toDate() : new Date(),
        });
      });

      return stories;
    } catch (error) {
      console.error('Error getting user stories:', error);
      // Check if the error is due to missing index or collection
      if (error.code === 'failed-precondition') {
        console.log('Stories collection may not exist or indexes may not be ready');
      }
      return [];
    }
  }

  // Get stories from user's contacts
  async getContactStories(currentUserId: string): Promise<Story[]> {
    try {
      // Get user's contacts
      const contacts = await userService.getUserContacts(currentUserId);
      const contactIds = contacts.map(contact => contact.contactId);

      // If no contacts, return empty array
      if (contactIds.length === 0) {
        return [];
      }

      // Include current user's ID so they can see their own stories
      const allUserIds = [currentUserId, ...contactIds];
      
      // Get stories from contacts and current user that are visible to current user
      const q = query(
        this.storiesCollection,
        where('userId', 'in', allUserIds),
        where('isVisibleTo', 'array-contains', currentUserId),
        where('expiresAt', '>', Timestamp.now()),
        orderBy('timestamp', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const stories: Story[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        stories.push({
          id: doc.id,
          userId: data.userId,
          userName: data.userName,
          userAvatar: data.userAvatar,
          mediaUrl: data.mediaUrl,
          mediaType: data.mediaType,
          caption: data.caption,
          timestamp: data.timestamp ? data.timestamp.toDate() : new Date(),
          expiresAt: data.expiresAt ? data.expiresAt.toDate() : new Date(Date.now() + 24 * 60 * 60 * 1000),
          viewedBy: data.viewedBy || [],
          isVisibleTo: data.isVisibleTo,
          createdAt: data.createdAt ? data.createdAt.toDate() : new Date(),
        });
      });

      return stories;
    } catch (error) {
      console.error('Error getting contact stories:', error);
      // Check if the error is due to missing index or collection
      if (error.code === 'failed-precondition') {
        console.log('Stories collection may not exist or indexes may not be ready');
        // Return empty array instead of throwing error
        return [];
      }
      return [];
    }
  }

  // Mark story as viewed
  async markStoryAsViewed(storyId: string, userId: string): Promise<void> {
    try {
      const storyRef = doc(this.storiesCollection, storyId);
      await updateDoc(storyRef, {
        viewedBy: arrayUnion(userId),
      });
    } catch (error) {
      console.error('Error marking story as viewed:', error);
      throw error;
    }
  }

  // Get story statistics
  async getStoryStats(storyId: string): Promise<StoryStats> {
    try {
      const storyRef = doc(this.storiesCollection, storyId);
      const storyDoc = await getDoc(storyRef);
      
      if (!storyDoc.exists()) {
        throw new Error('Story not found');
      }

      const data = storyDoc.data();
      const viewers: StoryViewer[] = [];

      // Get viewer details
      for (const viewerId of data.viewedBy || []) {
        try {
          const viewerProfile = await userService.getUserById(viewerId);
          if (viewerProfile) {
            viewers.push({
              userId: viewerId,
              userName: viewerProfile.displayName || 'Unknown User',
              viewedAt: new Date(), // We don't track individual view times yet
            });
          }
        } catch (error) {
          console.error('Error getting viewer profile:', error);
        }
      }

      return {
        totalViews: data.viewedBy?.length || 0,
        viewerList: viewers,
      };
    } catch (error) {
      console.error('Error getting story stats:', error);
      return { totalViews: 0, viewerList: [] };
    }
  }

  // Delete a story
  async deleteStory(storyId: string): Promise<void> {
    try {
      const storyRef = doc(this.storiesCollection, storyId);
      await deleteDoc(storyRef);
    } catch (error) {
      console.error('Error deleting story:', error);
      throw error;
    }
  }

  // Listen to real-time story updates
  listenToContactStories(currentUserId: string, callback: (stories: Story[]) => void): () => void {
    let unsubscribe: (() => void) | null = null;

    // Get user's contacts and set up listener
    userService.getUserContacts(currentUserId).then(contacts => {
      const contactIds = contacts.map(contact => contact.contactId);
      
      // Include current user's ID so they can see their own stories
      const allUserIds = [currentUserId, ...contactIds];

      // If no contacts and no current user, return empty stories immediately
      if (allUserIds.length === 0) {
        callback([]);
        return;
      }

      try {
        const q = query(
          this.storiesCollection,
          where('userId', 'in', allUserIds),
          where('isVisibleTo', 'array-contains', currentUserId),
          where('expiresAt', '>', Timestamp.now()),
          orderBy('timestamp', 'desc')
        );

        unsubscribe = onSnapshot(q, (querySnapshot) => {
          const stories: Story[] = [];

          querySnapshot.forEach((doc) => {
            const data = doc.data();
            stories.push({
              id: doc.id,
              userId: data.userId,
              userName: data.userName,
              userAvatar: data.userAvatar,
              mediaUrl: data.mediaUrl,
              mediaType: data.mediaType,
              caption: data.caption,
              timestamp: data.timestamp ? data.timestamp.toDate() : new Date(),
              expiresAt: data.expiresAt ? data.expiresAt.toDate() : new Date(Date.now() + 24 * 60 * 60 * 1000),
              viewedBy: data.viewedBy || [],
              isVisibleTo: data.isVisibleTo,
              createdAt: data.createdAt ? data.createdAt.toDate() : new Date(),
            });
          });

          callback(stories);
        }, (error) => {
          console.error('Error in story listener:', error);
          
          // Check if it's an index error
          if (error.code === 'failed-precondition' && error.message.includes('requires an index')) {
            console.log('Index is still building, trying ultra-simplified query...');
            // Try a much simpler query - just get all stories and filter manually
            try {
              const simpleQuery = query(
                this.storiesCollection,
                orderBy('timestamp', 'desc'),
                limit(50) // Limit to prevent too many results
              );
              
              const unsubscribeSimple = onSnapshot(simpleQuery, (simpleSnapshot) => {
                const simpleStories: Story[] = [];
                
                simpleSnapshot.forEach((doc) => {
                  const data = doc.data();
                  
                  // Manual filtering for all conditions
                  const isFromContactOrSelf = contactIds.includes(data.userId) || data.userId === currentUserId;
                  const isVisibleToUser = data.isVisibleTo && data.isVisibleTo.includes(currentUserId);
                  const isNotExpired = data.expiresAt && data.expiresAt.toDate() > new Date();
                  
                  if (isFromContactOrSelf && isVisibleToUser && isNotExpired) {
                    simpleStories.push({
                      id: doc.id,
                      userId: data.userId,
                      userName: data.userName,
                      userAvatar: data.userAvatar,
                      mediaUrl: data.mediaUrl,
                      mediaType: data.mediaType,
                      caption: data.caption,
                      timestamp: data.timestamp ? data.timestamp.toDate() : new Date(),
                      expiresAt: data.expiresAt ? data.expiresAt.toDate() : new Date(Date.now() + 24 * 60 * 60 * 1000),
                      viewedBy: data.viewedBy || [],
                      isVisibleTo: data.isVisibleTo,
                      createdAt: data.createdAt ? data.createdAt.toDate() : new Date(),
                    });
                  }
                });
                
                callback(simpleStories);
              });
              
              return unsubscribeSimple;
            } catch (simpleError) {
              console.error('Simple query also failed:', simpleError);
              // As a last resort, just return empty stories
              callback([]);
            }
          } else {
            // If it's not an index error, return empty stories
            callback([]);
          }
        });
      } catch (error) {
        console.error('Error setting up story listener:', error);
        callback([]);
      }
    }).catch(error => {
      console.error('Error getting user contacts for story listener:', error);
      callback([]);
    });

    // Return unsubscribe function
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }

  // Upload video (placeholder for now)
  private async uploadVideo(uri: string, fileName: string): Promise<string> {
    // For now, we'll use the same method as images
    // In a real app, you'd want to handle video compression and specific video storage
    return storageService.uploadStoryImage(uri, fileName);
  }

  // Clean up expired stories (could be called by a cloud function)
  async cleanupExpiredStories(): Promise<void> {
    try {
      const q = query(
        this.storiesCollection,
        where('expiresAt', '<=', Timestamp.now())
      );

      const querySnapshot = await getDocs(q);
      const batch = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
      
      await Promise.all(batch);
      console.log(`Cleaned up ${batch.length} expired stories`);
    } catch (error) {
      console.error('Error cleaning up expired stories:', error);
    }
  }
}

export const storyService = new StoryService();
export default storyService;