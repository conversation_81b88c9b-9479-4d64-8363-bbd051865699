import { addDoc, collection, deleteDoc, doc, getDoc, getDocs, onSnapshot, query, updateDoc, where, writeBatch } from 'firebase/firestore';
import { authService } from './authService';
import { db } from '../config/firebase';
import { notificationService } from './notificationService';
import { chatService } from './chatService';

export interface Contact {
  id: string;
  userId: string;
  contactId: string;
  contactName: string;
  contactEmail: string;
  contactPhotoURL?: string;
  isOnline: boolean;
  lastSeen: Date; // Made non-optional to match types/messenger.ts
  addedAt: Date;
  // Add missing properties for compatibility
  name: string; // Display name alias
  email: string; // Email alias
  photoURL?: string; // Photo URL alias
  createdAt: Date; // Creation date alias (made non-optional to match types/messenger.ts)
}

export interface ContactRequest {
  id: string;
  senderId: string;
  senderName: string;
  senderEmail: string;
  senderPhotoURL?: string;
  recipientId: string;
  message?: string;
  status: 'pending' | 'accepted' | 'declined';
  sentAt: Date;
  respondedAt?: Date;
}

class UserService {
  // Search users by name or email
  async searchUsers(searchTerm: string): Promise<any[]> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const lowerCaseSearchTerm = searchTerm.toLowerCase();

      const usersQuery = query(
        collection(db, 'users'),
        where('searchTokens', 'array-contains', lowerCaseSearchTerm)
      );

      const snapshot = await getDocs(usersQuery);
      const users: any[] = [];

      snapshot.forEach((doc) => {
        const userData = doc.data();
        if (doc.id !== currentUser.uid) {
          users.push({ id: doc.id, ...userData });
        }
      });

      return users;
    } catch (error) {
      console.error('Error searching users:', error);
      return [];
    }
  }

  // Send contact request
  async sendContactRequest(recipientId: string, message?: string): Promise<void> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get recipient user data
      const recipientDoc = await getDoc(doc(db, 'users', recipientId));
      if (!recipientDoc.exists()) {
        throw new Error('Recipient user not found');
      }

      const recipientData = recipientDoc.data();

      // Create contact request
      const contactRequest = {
        senderId: currentUser.uid,
        senderEmail: currentUser.email || '',
        senderName: currentUser.displayName || 'Unknown User',
        recipientId,
        message: message || '',
        status: 'pending',
        sentAt: new Date(),
      };

      await addDoc(collection(db, 'contactRequests'), contactRequest);

      // Send notification to recipient
      await notificationService.sendContactRequestNotification(
        recipientId,
        currentUser.displayName || 'Someone',
        currentUser.uid
      );

    } catch (error) {
      console.error('Error sending contact request:', error);
      throw error;
    }
  }

  // Get user's contact requests
  async getContactRequests(): Promise<ContactRequest[]> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get incoming contact requests
      const requestsQuery = query(
        collection(db, 'contactRequests'),
        where('recipientId', '==', currentUser.uid),
        where('status', '==', 'pending')
      );

      const snapshot = await getDocs(requestsQuery);
      
      const requests: ContactRequest[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        requests.push({
          id: doc.id,
          senderId: data.senderId,
          senderName: data.senderName,
          senderEmail: data.senderEmail,
          senderPhotoURL: data.senderPhotoURL,
          recipientId: data.recipientId,
          message: data.message,
          status: data.status,
          sentAt: data.sentAt.toDate(),
          respondedAt: data.respondedAt ? data.respondedAt.toDate() : undefined,
        });
      });

      return requests;
    } catch (error) {
      console.error('Error getting contact requests:', error);
      return [];
    }
  }

  // Respond to contact request
  async respondToContactRequest(requestId: string, action: 'accept' | 'decline'): Promise<void> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get the contact request
      const requestDoc = await getDoc(doc(db, 'contactRequests', requestId));
      if (!requestDoc.exists()) {
        throw new Error('Contact request not found');
      }

      const requestData = requestDoc.data();
      
      // Verify this request is for the current user
      if (requestData.recipientId !== currentUser.uid) {
        throw new Error('Not authorized to respond to this request');
      }

      // Update the request status
      await updateDoc(doc(db, 'contactRequests', requestId), {
        status: action,
        respondedAt: new Date(),
      });

      // If accepted, create contacts for both users
      if (action === 'accept') {
        // Create contact for current user
        const senderDoc = await getDoc(doc(db, 'users', requestData.senderId));
        if (senderDoc.exists()) {
          const senderData = senderDoc.data();
          const contactForCurrentUser = {
            userId: currentUser.uid,
            contactId: requestData.senderId,
            contactEmail: senderData.email || '',
            contactName: senderData.displayName || 'Unknown User',
            contactPhotoURL: senderData.photoURL || null,
            isOnline: senderData.isOnline || false,
            lastSeen: senderData.lastSeen ? senderData.lastSeen.toDate() : new Date(), // Provide default date
            addedAt: new Date(),
            // Add missing properties for compatibility
            name: senderData.displayName || 'Unknown User',
            email: senderData.email || '',
            photoURL: senderData.photoURL || null,
            createdAt: new Date(),
          };

          await addDoc(collection(db, 'contacts'), contactForCurrentUser);
        }

        // Create contact for sender
        const contactForSender = {
          userId: requestData.senderId,
          contactId: currentUser.uid,
          contactEmail: currentUser.email || '',
          contactName: currentUser.displayName || 'Unknown User',
          contactPhotoURL: currentUser.photoURL || null,
          isOnline: false, // Default value since we don't have this info
          lastSeen: new Date(), // Provide default date
          addedAt: new Date(),
          // Add missing properties for compatibility
          name: currentUser.displayName || 'Unknown User',
          email: currentUser.email || '',
          photoURL: currentUser.photoURL || null,
          createdAt: new Date(),
        };

        await addDoc(collection(db, 'contacts'), contactForSender);

        // Send notification to sender
        await notificationService.sendContactAcceptedNotification(
          requestData.senderId,
          currentUser.displayName || 'Someone',
          currentUser.uid
        );

      }
    } catch (error) {
      console.error('Error responding to contact request:', error);
      throw error;
    }
  }

  // Get user's contacts
  async getUserContacts(userId?: string): Promise<Contact[]> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const targetUserId = userId || currentUser.uid;

      // Get user's contacts
      const contactsQuery = query(
        collection(db, 'contacts'),
        where('userId', '==', targetUserId)
      );

      const snapshot = await getDocs(contactsQuery);
      
      const contacts: Contact[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        contacts.push({
          id: data.contactId, // Use contactId as the main id
          userId: data.userId,
          contactId: data.contactId,
          contactName: data.contactName,
          contactEmail: data.contactEmail,
          contactPhotoURL: data.contactPhotoURL,
          isOnline: data.isOnline,
          lastSeen: data.lastSeen ? data.lastSeen.toDate() : new Date(), // Provide default date
          addedAt: data.addedAt ? data.addedAt.toDate() : new Date(),
          // Add missing properties for compatibility
          name: data.contactName,
          email: data.contactEmail,
          photoURL: data.contactPhotoURL,
          createdAt: data.addedAt ? data.addedAt.toDate() : new Date(),
        });
      });

      return contacts;
    } catch (error) {
      console.error('Error getting user contacts:', error);
      return [];
    }
  }

  // Get user's contacts (alias for getUserContacts)
  async getContacts(): Promise<Contact[]> {
    return await this.getUserContacts();
  }

  // Check if a user is in the contact list
  async isContact(contactId: string): Promise<boolean> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        return false;
      }

      const contactsQuery = query(
        collection(db, 'contacts'),
        where('userId', '==', currentUser.uid),
        where('contactId', '==', contactId)
      );

      const snapshot = await getDocs(contactsQuery);
      return !snapshot.empty;
    } catch (error) {
      console.error('Error checking if user is a contact:', error);
      return false;
    }
  }

  // Listen to user contacts updates
  listenToUserContacts(userId: string, callback: (contacts: Contact[]) => void): () => void {
    try {
      const contactsQuery = query(
        collection(db, 'contacts'),
        where('userId', '==', userId)
      );

      const unsubscribe = onSnapshot(contactsQuery, (snapshot) => {
        console.log('🔥 Firestore snapshot received for user contacts:', snapshot.docs.length, 'documents');
        console.log('🔥 Contacts snapshot metadata:', {
          fromCache: snapshot.metadata.fromCache,
          hasPendingWrites: snapshot.metadata.hasPendingWrites,
          docChanges: snapshot.docChanges().length
        });

        const contacts: Contact[] = [];
        
        // Check for document changes to update chat participant details
        const docChanges = snapshot.docChanges();
        for (const change of docChanges) {
          if (change.type === 'modified') {
            const data = change.doc.data();
            console.log('🔄 Contact document modified:', change.doc.id, 'Name:', data.contactName, 'PhotoURL:', data.contactPhotoURL);
            
            // Update chat participant details when contact info changes
            if (data.contactName || data.contactPhotoURL) {
              chatService.updateParticipantDetailsForUser(data.contactId, {
                displayName: data.contactName,
                photoURL: data.contactPhotoURL
              }).catch(error => {
                console.error('❌ Failed to update chat participant details for contact:', data.contactId, error);
              });
            }
          }
        }
        
        snapshot.forEach((doc) => {
          const data = doc.data();
          console.log('🔥 Processing contact document:', doc.id, 'Name:', data.contactName, 'Email:', data.contactEmail, 'PhotoURL:', data.contactPhotoURL);
          console.log('🔥 Contact data structure:', {
            contactId: data.contactId,
            userId: data.userId,
            contactName: data.contactName,
            contactPhotoURL: data.contactPhotoURL,
            isOnline: data.isOnline,
            lastSeen: data.lastSeen,
            addedAt: data.addedAt
          });
          contacts.push({
            id: data.contactId, // Use contactId as the main id
            userId: data.userId,
            contactId: data.contactId,
            contactName: data.contactName,
            contactEmail: data.contactEmail,
            contactPhotoURL: data.contactPhotoURL,
            isOnline: data.isOnline,
            lastSeen: data.lastSeen ? data.lastSeen.toDate() : new Date(), // Provide default date
            addedAt: data.addedAt ? data.addedAt.toDate() : new Date(),
            // Add missing properties for compatibility
            name: data.contactName,
            email: data.contactEmail,
            photoURL: data.contactPhotoURL,
            createdAt: data.addedAt ? data.addedAt.toDate() : new Date(),
          });
        });

        console.log('🔄 Real-time contacts update received:', contacts.length, 'contacts');
        callback(contacts);
      }, (error: any) => {
        console.error('❌ Error listening to user contacts:', error);
        console.error('❌ Error code:', error.code);
        console.error('❌ Error message:', error.message);
        
        // Handle missing index error gracefully
        if (error.code === 'failed-precondition' && error.message.includes('query requires an index')) {
          console.warn('⚠️ Firestore index missing. Please create the required index using the Firebase Console.');
          console.warn('⚠️ Index URL should be in the error message above.');
          callback([]); // Call with empty array
        } else {
          // For other errors, still call callback with empty array to avoid UI hanging
          callback([]);
        }
      });

      return unsubscribe;
    } catch (error) {
      console.error('Error setting up contacts listener:', error);
      return () => {};
    }
  }

  // Remove contact
  async removeContact(contactId: string): Promise<void> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Find and delete the contact
      const contactsQuery = query(
        collection(db, 'contacts'),
        where('userId', '==', currentUser.uid),
        where('contactId', '==', contactId)
      );

      const snapshot = await getDocs(contactsQuery);
      
      const batch = writeBatch(db);
      snapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });
      
      await batch.commit();

      // Also remove the reverse contact relationship
      const reverseContactsQuery = query(
        collection(db, 'contacts'),
        where('userId', '==', contactId),
        where('contactId', '==', currentUser.uid)
      );

      const reverseSnapshot = await getDocs(reverseContactsQuery);
      
      const reverseBatch = writeBatch(db);
      reverseSnapshot.forEach((doc) => {
        reverseBatch.delete(doc.ref);
      });
      
      await reverseBatch.commit();
    } catch (error) {
      console.error('Error removing contact:', error);
      throw error;
    }
  }

  // Block user
  async blockUser(userId: string): Promise<void> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Create a block record
      const blockRecord = {
        blockerId: currentUser.uid,
        blockedId: userId,
        blockedAt: new Date(),
      };

      await addDoc(collection(db, 'blockedUsers'), blockRecord);

      // Remove from contacts if they're contacts
      await this.removeContact(userId);
    } catch (error) {
      console.error('Error blocking user:', error);
      throw error;
    }
  }

  // Unblock user
  async unblockUser(userId: string): Promise<void> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Find and delete the block record
      const blockQuery = query(
        collection(db, 'blockedUsers'),
        where('blockerId', '==', currentUser.uid),
        where('blockedId', '==', userId)
      );

      const snapshot = await getDocs(blockQuery);
      
      const batch = writeBatch(db);
      snapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });
      
      await batch.commit();
    } catch (error) {
      console.error('Error unblocking user:', error);
      throw error;
    }
  }

  // Check if user is blocked
  async isUserBlocked(userId: string): Promise<boolean> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        return false;
      }

      const blockQuery = query(
        collection(db, 'blockedUsers'),
        where('blockerId', '==', currentUser.uid),
        where('blockedId', '==', userId)
      );

      const snapshot = await getDocs(blockQuery);
      return !snapshot.empty;
    } catch (error) {
      console.error('Error checking if user is blocked:', error);
      return false;
    }
  }

  // Get blocked users
  async getBlockedUsers(): Promise<string[]> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        return [];
      }

      const blockQuery = query(
        collection(db, 'blockedUsers'),
        where('blockerId', '==', currentUser.uid)
      );

      const snapshot = await getDocs(blockQuery);
      
      const blockedUserIds: string[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        blockedUserIds.push(data.blockedId);
      });

      return blockedUserIds;
    } catch (error) {
      console.error('Error getting blocked users:', error);
      return [];
    }
  }

  // Accept contact request
  async acceptContactRequest(requestId: string): Promise<void> {
    return await this.respondToContactRequest(requestId, 'accept');
  }

  // Reject contact request
  async rejectContactRequest(requestId: string): Promise<void> {
    return await this.respondToContactRequest(requestId, 'decline');
  }

  // Get user by ID
  async getUserById(userId: string): Promise<any> {
    try {
      const userDoc = await getDoc(doc(db, 'users', userId));
      if (userDoc.exists()) {
        console.log('User data from Firestore:', userDoc.data());
        const userData = userDoc.data();
        // Ensure we have proper default values
        return {
          id: userDoc.id,
          ...userData,
          name: userData.displayName || userData.name || 'Unknown User',
          email: userData.email || '',
          photoURL: userData.photoURL || userData.avatar || null,
          isOnline: userData.isOnline || false,
          lastSeen: userData.lastSeen ? (userData.lastSeen.toDate ? userData.lastSeen.toDate() : userData.lastSeen) : new Date(),
          createdAt: userData.createdAt ? (userData.createdAt.toDate ? userData.createdAt.toDate() : userData.createdAt) : new Date(),
        };
      }
      return null;
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  // Debug function: Manually update contact records for a user
  async debugUpdateContactRecords(userId: string, updates: { displayName?: string; photoURL?: string }): Promise<void> {
    try {
      console.log('🔧 DEBUG: Manually updating contact records for user:', userId);
      console.log('🔧 DEBUG: Updates to apply:', updates);
      
      const contactsQuery = query(
        collection(db, 'contacts'),
        where('contactId', '==', userId)
      );
      
      const contactsSnapshot = await getDocs(contactsQuery);
      console.log('🔧 DEBUG: Found', contactsSnapshot.docs.length, 'contact records');
      
      const batchUpdates: Promise<void>[] = [];
      contactsSnapshot.forEach((contactDoc) => {
        const data = contactDoc.data();
        console.log('🔧 DEBUG: Contact record details:', {
          id: contactDoc.id,
          userId: data.userId,
          contactId: data.contactId,
          currentName: data.contactName,
          currentPhotoURL: data.contactPhotoURL
        });
        
        const contactUpdate: any = {};
        if (updates.displayName) {
          contactUpdate.contactName = updates.displayName;
        }
        if (updates.photoURL) {
          contactUpdate.contactPhotoURL = updates.photoURL;
        }
        
        console.log('🔧 DEBUG: Updating contact:', contactDoc.id, 'with:', contactUpdate);
        batchUpdates.push(updateDoc(contactDoc.ref, contactUpdate));
      });
      
      await Promise.all(batchUpdates);
      console.log('🔧 DEBUG: Updated', batchUpdates.length, 'contact records');
    } catch (error) {
      console.error('🔧 DEBUG: Error updating contact records:', error);
      throw error;
    }
  }

  // Debug function: Show all contact records for current user
  async debugShowMyContacts(): Promise<void> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        console.log('🔧 DEBUG: No current user found');
        return;
      }

      console.log('🔧 DEBUG: Showing contacts for current user:', currentUser.uid);
      
      const contactsQuery = query(
        collection(db, 'contacts'),
        where('userId', '==', currentUser.uid)
      );
      
      const contactsSnapshot = await getDocs(contactsQuery);
      console.log('🔧 DEBUG: Found', contactsSnapshot.docs.length, 'contact records for current user');
      
      contactsSnapshot.forEach((contactDoc) => {
        const data = contactDoc.data();
        console.log('🔧 DEBUG: My contact record:', {
          id: contactDoc.id,
          userId: data.userId,
          contactId: data.contactId,
          contactName: data.contactName,
          contactPhotoURL: data.contactPhotoURL,
          isOnline: data.isOnline,
          lastSeen: data.lastSeen
        });
      });
    } catch (error) {
      console.error('🔧 DEBUG: Error showing contacts:', error);
    }
  }

  // Debug function: Show all contact records where specific user is the contact
  async debugShowContactRecordsForUser(contactId: string): Promise<void> {
    try {
      console.log('🔧 DEBUG: Showing contact records where user is contact:', contactId);
      
      const contactsQuery = query(
        collection(db, 'contacts'),
        where('contactId', '==', contactId)
      );
      
      const contactsSnapshot = await getDocs(contactsQuery);
      console.log('🔧 DEBUG: Found', contactsSnapshot.docs.length, 'contact records where user is contact');
      
      contactsSnapshot.forEach((contactDoc) => {
        const data = contactDoc.data();
        console.log('🔧 DEBUG: Contact record where user is contact:', {
          id: contactDoc.id,
          userId: data.userId,
          contactId: data.contactId,
          contactName: data.contactName,
          contactPhotoURL: data.contactPhotoURL,
          isOnline: data.isOnline,
          lastSeen: data.lastSeen
        });
      });
    } catch (error) {
      console.error('🔧 DEBUG: Error showing contact records for user:', error);
    }
  }

  // Debug function: Simulate Bapon updating his profile
  async debugSimulateBaponProfileUpdate(): Promise<void> {
    try {
      const baponUserId = 'MzXam2r12ydOqN79D4JML5rSYrp2';
      const newPhotoURL = 'https://via.placeholder.com/150/FF0000/FFFFFF?text=BAPON_NEW';
      
      console.log('🔧 DEBUG: Simulating Bapon profile update with new photo:', newPhotoURL);
      
      // First, show Bapon's current contact records
      await this.debugShowContactRecordsForUser(baponUserId);
      
      // Then simulate the profile update process
      const contactsQuery = query(
        collection(db, 'contacts'),
        where('contactId', '==', baponUserId)
      );
      
      const contactsSnapshot = await getDocs(contactsQuery);
      console.log('🔧 DEBUG: Found', contactsSnapshot.docs.length, 'contact records to update for Bapon');
      
      const batchUpdates: Promise<void>[] = [];
      contactsSnapshot.forEach((contactDoc) => {
        const data = contactDoc.data();
        console.log('🔧 DEBUG: Updating contact record for Bapon:', {
          id: contactDoc.id,
          ownerUserId: data.userId,
          currentPhotoURL: data.contactPhotoURL,
          newPhotoURL: newPhotoURL
        });
        
        const contactUpdate: any = {
          contactPhotoURL: newPhotoURL
        };
        
        batchUpdates.push(updateDoc(contactDoc.ref, contactUpdate));
      });
      
      if (batchUpdates.length > 0) {
        await Promise.all(batchUpdates);
        console.log('🔧 DEBUG: Updated', batchUpdates.length, 'contact records for Bapon');
        
        // Show the updated records
        await this.debugShowContactRecordsForUser(baponUserId);
      } else {
        console.log('🔧 DEBUG: No contact records found to update for Bapon');
      }
    } catch (error) {
      console.error('🔧 DEBUG: Error simulating Bapon profile update:', error);
      throw error;
    }
  }

  // Utility function to manually trigger avatar sync for a user
  async syncAvatarForUser(userId: string, newPhotoURL?: string, newDisplayName?: string): Promise<void> {
    try {
      console.log('🔄 Manually syncing avatar for user:', userId);
      
      // If new avatar or name provided, get the user's current data first
      let updates: { displayName?: string; photoURL?: string } = {};
      
      if (newPhotoURL || newDisplayName) {
        updates = {
          displayName: newDisplayName,
          photoURL: newPhotoURL
        };
      } else {
        // Fetch current user data
        const user = await this.getUserById(userId);
        if (user) {
          updates = {
            displayName: user.displayName || user.name,
            photoURL: user.photoURL || user.avatar
          };
        }
      }
      
      if (updates.displayName || updates.photoURL) {
        // Update all contact records for this user
        await this.debugUpdateContactRecords(userId, updates);
        
        // Update all chat participant details for this user
        await chatService.updateParticipantDetailsForUser(userId, updates);
        
        console.log('✅ Successfully synced avatar for user:', userId);
      } else {
        console.warn('⚠️ No avatar or name data to sync for user:', userId);
      }
    } catch (error) {
      console.error('❌ Error syncing avatar for user:', userId, error);
      throw error;
    }
  }
}

// Export singleton instance
export const userService = new UserService();
export default userService;