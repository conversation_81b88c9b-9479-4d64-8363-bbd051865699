import {
    addDoc,
    arrayRemove,
    arrayUnion,
    collection,
    deleteDoc,
    doc,
    getDoc,
    getDocs,
    limit,
    onSnapshot,
    orderBy,
    query,
    QueryDocumentSnapshot,
    startAfter,
    Timestamp,
    updateDoc,
    where
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { authService } from './authService';
import { userService } from './userService';
import { webSocketService } from './websocketService';
import { storageService } from './storageService';
import { notificationService } from './notificationService';

export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  senderEmail: string;
  text: string;
  type: 'text' | 'image' | 'file' | 'video';
  timestamp: Date;
  replyTo?: string;
  reactions: { [emoji: string]: string[] };
  edited?: boolean;
  editedAt?: Date | null;
  imageUrl?: string;
  imageWidth?: number;
  imageHeight?: number;
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  videoUrl?: string;
  videoThumbnail?: string;
  videoDuration?: number;
  videoSize?: number;
  videoWidth?: number;
  videoHeight?: number;
  currentUserId?: string;
}

export interface Chat {
  id: string;
  participants: string[];
  participantDetails: { [userId: string]: { name: string; email: string; photoURL?: string } };
  type: 'direct' | 'group';
  name?: string;
  description?: string;
  photoURL?: string;
  createdBy?: string;
  createdAt: Date;
  lastMessage?: {
    text: string;
    senderId: string;
    timestamp: Date;
    type: string;
  };
  lastActivity: Date;
  unreadCount?: { [userId: string]: number };
}

export interface ChatListItem {
  id: string;
  name: string;
  lastMessage: string;
  lastMessageTime: Date;
  unreadCount: number;
  isGroup: boolean;
  participants: string[];
  photoURL?: string;
}

class ChatService {
  private messageListeners: Map<string, () => void> = new Map();
  private chatListListeners: (() => void)[] = [];

  // Get user's chat list
  async getUserChats(userId: string): Promise<ChatListItem[]> {
    try {
      const chatsQuery = query(
        collection(db, 'chats'),
        where('participants', 'array-contains', userId),
        orderBy('lastActivity', 'desc')
      );

      const snapshot = await getDocs(chatsQuery);
      const chats: ChatListItem[] = [];

      for (const doc of snapshot.docs) {
        const chatData = doc.data();
        const chat = await this.formatChatListItem(doc.id, chatData, userId);
        chats.push(chat);
      }

      return chats;
    } catch (error: any) {
      console.error('Error getting user chats:', error);
      // Handle missing index error gracefully
      if (error.code === 'failed-precondition' && error.message.includes('query requires an index')) {
        console.warn('Firestore index missing. Please create the required index using the Firebase Console.');
        // Return empty array instead of throwing error
        return [];
      }
      throw error;
    }
  }

  // Update participant details for all chats involving a user
  async updateParticipantDetailsForUser(userId: string, updates: { displayName?: string; photoURL?: string }): Promise<void> {
    try {
      console.log('🔄 Updating participant details for user:', userId, 'with updates:', updates);
      
      // Find all chats where this user is a participant
      const chatsQuery = query(
        collection(db, 'chats'),
        where('participants', 'array-contains', userId)
      );
      
      const snapshot = await getDocs(chatsQuery);
      console.log('🔄 Found', snapshot.docs.length, 'chats to update for user:', userId);
      
      const batchUpdates: Promise<void>[] = [];
      
      for (const doc of snapshot.docs) {
        const chatData = doc.data();
        const chatId = doc.id;
        
        // Check if this user's details need updating in the participantDetails
        const currentDetails = chatData.participantDetails?.[userId];
        const needsUpdate = updates.displayName && currentDetails?.name !== updates.displayName ||
                          updates.photoURL && currentDetails?.photoURL !== updates.photoURL;
        
        if (needsUpdate) {
          console.log('🔄 Updating participant details in chat:', chatId, 'for user:', userId);
          
          const newParticipantDetails = {
            ...chatData.participantDetails,
            [userId]: {
              name: updates.displayName || currentDetails?.name || 'Unknown User',
              email: currentDetails?.email || '',
              photoURL: updates.photoURL || currentDetails?.photoURL
            }
          };
          
          batchUpdates.push(updateDoc(doc.ref, { participantDetails: newParticipantDetails }));
        }
      }
      
      if (batchUpdates.length > 0) {
        await Promise.all(batchUpdates);
        console.log('✅ Updated participant details in', batchUpdates.length, 'chats for user:', userId);
      } else {
        console.log('ℹ️ No participant details needed updating for user:', userId);
      }
    } catch (error) {
      console.error('❌ Error updating participant details for user:', userId, error);
    }
  }

  // Listen to user's chat list changes
  listenToUserChats(userId: string, callback: (chats: ChatListItem[]) => void): () => void {
    try {
      const chatsQuery = query(
        collection(db, 'chats'),
        where('participants', 'array-contains', userId),
        orderBy('lastActivity', 'desc')
      );

      const unsubscribe = onSnapshot(chatsQuery, async (snapshot) => {
        console.log('🔥 Firestore snapshot received for user chats:', snapshot.docs.length, 'documents');
        console.log('🔥 Snapshot metadata:', {
          fromCache: snapshot.metadata.fromCache,
          hasPendingWrites: snapshot.metadata.hasPendingWrites,
          docChanges: snapshot.docChanges().length
        });

        const chats: ChatListItem[] = [];

        for (const doc of snapshot.docs) {
          const chatData = doc.data();
          console.log('🔥 Processing chat document:', doc.id, 'Type:', chatData.type, 'Participants:', chatData.participants);
          const chat = await this.formatChatListItem(doc.id, chatData, userId);
          chats.push(chat);
        }

        console.log('🔥 Calling callback with', chats.length, 'chats');
        callback(chats);
      }, (error: any) => {
        console.error('❌ Error listening to user chats:', error);
        console.error('❌ Error code:', error.code);
        console.error('❌ Error message:', error.message);
        
        // Handle missing index error gracefully
        if (error.code === 'failed-precondition' && error.message.includes('query requires an index')) {
          console.warn('⚠️ Firestore index missing. Please create the required index using the Firebase Console.');
          console.warn('⚠️ Index URL should be in the error message above.');
          callback([]); // Call with empty array
        } else {
          // For other errors, still call callback with empty array to avoid UI hanging
          callback([]);
        }
      });

      this.chatListListeners.push(unsubscribe);
      return unsubscribe;
    } catch (error) {
      console.error('Error listening to user chats:', error);
      throw error;
    }
  }

  // Get chat messages with pagination
  async getChatMessages(
    chatId: string,
    pageSize: number = 20,
    lastDoc?: QueryDocumentSnapshot
  ): Promise<{ messages: Message[]; lastDoc: QueryDocumentSnapshot | null }> {
    try {
      let messagesQuery = query(
        collection(db, 'messages'),
        where('chatId', '==', chatId),
        orderBy('timestamp', 'desc'),
        limit(pageSize)
      );

      if (lastDoc) {
        messagesQuery = query(messagesQuery, startAfter(lastDoc));
      }

      const snapshot = await getDocs(messagesQuery);
      const messages: Message[] = [];

      snapshot.forEach((doc) => {
        const data = doc.data();
        messages.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp.toDate(),
          editedAt: data.editedAt?.toDate() || null,
        } as Message);
      });

      const lastDocument = snapshot.docs[snapshot.docs.length - 1] || null;

      return { messages: messages.reverse(), lastDoc: lastDocument };
    } catch (error) {
      console.error('Error getting chat messages:', error);
      throw error;
    }
  }

  // Listen to chat messages in real-time
  listenToChatMessages(chatId: string, callback: (messages: Message[]) => void): () => void {
    try {
      const messagesQuery = query(
        collection(db, 'messages'),
        where('chatId', '==', chatId),
        orderBy('timestamp', 'desc'),
        limit(50) // Limit for real-time listening
      );

      const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
        const messages: Message[] = [];

        snapshot.forEach((doc) => {
          const data = doc.data();
          messages.push({
            id: doc.id,
            ...data,
            timestamp: data.timestamp.toDate(),
            editedAt: data.editedAt?.toDate() || null,
          } as Message);
        });

        callback(messages.reverse());
      });

      this.messageListeners.set(chatId, unsubscribe);
      return unsubscribe;
    } catch (error) {
      console.error('Error listening to chat messages:', error);
      throw error;
    }
  }

  // Send message (integrates with WebSocket)
  async sendMessage(
    chatId: string,
    text: string,
    type: 'text' | 'image' | 'file' | 'video' = 'text',
    replyTo?: string,
    imageUrl?: string,
    fileUrl?: string,
    fileName?: string,
    fileSize?: number,
    videoUrl?: string,
    videoThumbnail?: string,
    videoDuration?: number,
    videoSize?: number,
    imageWidth?: number,
    imageHeight?: number,
    videoWidth?: number,
    videoHeight?: number,
    onProgress?: () => void
  ): Promise<void> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      let finalImageUrl = imageUrl || null;
      let finalImageWidth = imageWidth || null;
      let finalImageHeight = imageHeight || null;
      let finalVideoUrl = videoUrl || null;
      let finalVideoThumbnail = videoThumbnail || null;
      let finalVideoDuration = videoDuration || null;
      let finalVideoSize = videoSize || null;
      let finalVideoWidth = videoWidth || null;
      let finalVideoHeight = videoHeight || null;

      // If image is provided as local URI, upload it to Firebase Storage
      if (type === 'image' && imageUrl && imageUrl.startsWith('file://')) {
        try {
          const imageResult = await storageService.uploadChatImage(imageUrl, `message_${Date.now()}`, onProgress);
          finalImageUrl = imageResult.url;
          finalImageWidth = imageResult.width;
          finalImageHeight = imageResult.height;
        } catch (uploadError) {
          console.error('Error uploading image to storage:', uploadError);
          // Continue with local URI as fallback
        }
      }

      // If video is provided as local URI, upload it to Firebase Storage
      if (type === 'video' && videoUrl && (videoUrl.startsWith('file://') || videoUrl.startsWith('ph://') || videoUrl.includes('/'))) {
        console.log('🎥 Uploading video to storage:', videoUrl);
        try {
          const videoResult = await storageService.uploadChatVideo(videoUrl, `video_${Date.now()}`, onProgress);
          finalVideoUrl = videoResult.videoUrl;
          finalVideoThumbnail = videoResult.thumbnailUrl;
          finalVideoDuration = videoResult.duration;
          finalVideoSize = videoResult.size;
          finalVideoWidth = videoResult.width;
          finalVideoHeight = videoResult.height;
        } catch (uploadError) {
          console.error('❌ Error uploading video to storage:', uploadError);
          // Continue with local URI as fallback
        }
      } else if (type === 'video') {
        console.log('🎥 Video URI check - type:', type, 'videoUrl:', videoUrl, 'startsWith file://:', videoUrl?.startsWith('file://'));
      }

      // The WebSocket connection is not used in the Vercel deployment.
      // The message is saved directly to Firestore, and real-time updates
      // are handled by the Firestore onSnapshot listener.
      // webSocketService.sendMessage({
      //   chatId,
      //   text,
      //   type,
      //   replyTo,
      // });

      // Also save directly to Firestore as backup
      const messageData = {
        chatId,
        senderId: currentUser.uid,
        senderName: currentUser.displayName || 'Unknown User',
        senderEmail: currentUser.email || '',
        text,
        type,
        timestamp: Timestamp.now(),
        replyTo: replyTo || null,
        reactions: {},
        edited: false,
        editedAt: null,
        imageUrl: finalImageUrl || null,
        imageUri: imageUrl || null, // Keep local URI for mobile app (but don't send undefined)
        fileUrl: fileUrl || null,
        fileName: fileName || null,
        fileSize: fileSize || null,
        videoUrl: finalVideoUrl || null,
        videoThumbnail: finalVideoThumbnail || null,
        videoDuration: finalVideoDuration || null,
        videoSize: finalVideoSize || null,
        imageWidth: finalImageWidth || null,
        imageHeight: finalImageHeight || null,
        videoWidth: finalVideoWidth || null,
        videoHeight: finalVideoHeight || null,
      };

      const messageRef = await addDoc(collection(db, 'messages'), messageData);

      // Get chat details to send notifications to other participants
      const chatDetails = await this.getChatDetails(chatId);
      if (chatDetails) {
        const senderName = currentUser.displayName || 'Unknown User';
        const isGroup = chatDetails.type === 'group';
        
        // Send notifications to all other participants
        for (const participantId of chatDetails.participants) {
          if (participantId !== currentUser.uid) {
            try {
              // Use appropriate message text for notifications
              let notificationText = text;
              if (type === 'image') {
                notificationText = '📷 Sent a photo';
              } else if (type === 'video') {
                notificationText = '🎥 Sent a video';
              } else if (type === 'file') {
                notificationText = '📎 Sent a file';
              }
              
              await notificationService.sendChatMessageNotification(
                participantId,
                chatId,
                senderName,
                notificationText,
                isGroup,
                currentUser.uid
              );
            } catch (notificationError) {
              console.error('Error sending notification to participant:', participantId, notificationError);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Create new chat
  async createChat(
    participantIds: string[],
    type: 'direct' | 'group' = 'direct',
    name?: string,
    description?: string,
    photoURL?: string
  ): Promise<string> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const token = await authService.getAuthToken();
      if (!token) {
        throw new Error('No authentication token');
      }

      // Use the backend API to create the chat
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3001'}/api/chats`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          participantIds,
          type,
          name,
          description,
          photoURL
        }),
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || 'Failed to create chat');
      }

      const data = await response.json() as { chatId: string };
      return data.chatId;
    } catch (error) {
      console.error('Error creating chat:', error);
      throw error;
    }
  }

  // Find existing direct chat between two users
  async findDirectChat(otherUserId: string): Promise<string | null> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const chatsQuery = query(
        collection(db, 'chats'),
        where('participants', 'array-contains', currentUser.uid),
        where('type', '==', 'direct')
      );

      const snapshot = await getDocs(chatsQuery);
      
      for (const doc of snapshot.docs) {
        const participants = doc.data().participants;
        if (participants.includes(otherUserId) && participants.length === 2) {
          return doc.id;
        }
      }

      return null;
    } catch (error) {
      console.error('Error finding direct chat:', error);
      return null;
    }
  }

  // Create a new direct chat
  async createDirectChat(otherUserId: string): Promise<string> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Check if chat already exists
      const existingChatId = await this.findDirectChat(otherUserId);
      if (existingChatId) {
        return existingChatId;
      }

      // Create new direct chat
      return await this.createChat([otherUserId], 'direct');
    } catch (error) {
      console.error('Error creating direct chat:', error);
      throw error;
    }
  }

  // Find or create a direct chat between two users
  async findOrCreateDirectChat(otherUserId: string): Promise<string> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Check if a chat already exists
      const existingChatId = await this.findDirectChat(otherUserId);
      if (existingChatId) {
        return existingChatId;
      }

      // If not, create a new direct chat
      const newChatId = await this.createDirectChat(otherUserId);
      return newChatId;
    } catch (error) {
      console.error('Error finding or creating direct chat:', error);
      throw error;
    }
  }

  // Add reaction to message
  async addReaction(messageId: string, emoji: string, chatId: string): Promise<void> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get current message to check if user already reacted
      const messageRef = doc(db, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);
      const messageData = messageDoc.data();
      const currentReactions = messageData?.reactions || {};
      
      // Check if user already reacted with this emoji
      const userReacted = currentReactions[emoji]?.includes(currentUser.uid);
      
      if (userReacted) {
        // Remove reaction
        await updateDoc(messageRef, {
          [`reactions.${emoji}`]: arrayRemove(currentUser.uid)
        });
      } else {
        // Add reaction
        await updateDoc(messageRef, {
          [`reactions.${emoji}`]: arrayUnion(currentUser.uid)
        });
      }

      // Use WebSocket for real-time updates (if available)
      webSocketService.addReaction(messageId, emoji, chatId);
    } catch (error) {
      console.error('Error adding reaction:', error);
      throw error;
    }
  }

  // Update chat details (for group chats)
  async updateChat(
    chatId: string,
    updates: { name?: string; description?: string; photoURL?: string }
  ): Promise<void> {
    try {
      await updateDoc(doc(db, 'chats', chatId), updates);
    } catch (error) {
      console.error('Error updating chat:', error);
      throw error;
    }
  }

  // Add members to a group
  async addMembersToGroup(chatId: string, memberIds: string[]): Promise<void> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const chatRef = doc(db, 'chats', chatId);
      
      // Get chat details for notification
      const chatDetails = await this.getChatDetails(chatId);
      if (!chatDetails) {
        throw new Error('Chat not found');
      }

      // Update participants
      await updateDoc(chatRef, {
        participants: arrayUnion(...memberIds),
      });

      // Send notifications to newly added members
      const groupName = chatDetails.name || 'Unnamed Group';
      const addedByUserName = currentUser.displayName || 'Someone';
      
      for (const memberId of memberIds) {
        try {
          await notificationService.sendGroupAddedNotification(
            memberId,
            groupName,
            addedByUserName,
            chatId
          );
        } catch (notificationError) {
          console.error('Error sending group addition notification to member:', memberId, notificationError);
        }
      }
    } catch (error) {
      console.error('Error adding members to group:', error);
      throw error;
    }
  }

  // Remove a member from a group
  async removeMemberFromGroup(chatId: string, memberId: string): Promise<void> {
    try {
      const chatRef = doc(db, 'chats', chatId);
      await updateDoc(chatRef, {
        participants: arrayRemove(memberId),
      });
    } catch (error) {
      console.error('Error removing member from group:', error);
      throw error;
    }
  }

  // Mark messages as read
  async markMessagesAsRead(chatId: string): Promise<void> {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) return;

      await updateDoc(doc(db, 'chats', chatId), {
        [`unreadCount.${currentUser.uid}`]: 0,
      });
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  }

  // Delete message
  async deleteMessage(messageId: string): Promise<void> {
    try {
      const messageRef = doc(db, 'messages', messageId);
      await updateDoc(messageRef, {
        deleted: true,
        deletedAt: new Date(),
        text: '', // Clear the message content
        imageUrl: null, // Clear image URL if present
        imageUri: null, // Clear image URI if present
        videoUrl: null, // Clear video URL if present
        videoThumbnail: null, // Clear video thumbnail if present
        fileUrl: null, // Clear file URL if present
        fileName: null, // Clear file name if present
        reactions: {} // Clear reactions
      });
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }

  // Delete an entire chat or leave a group
  async deleteChat(chatId: string): Promise<void> {
    try {
      const token = await authService.getAuthToken();
      if (!token) {
        throw new Error('No authentication token');
      }

      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/api/chats/${chatId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        try {
          const errorData = await response.json() as { error?: string };
          throw new Error(errorData.error || 'Failed to delete chat');
        } catch (e) {
          throw new Error(`Failed to delete chat with status: ${response.status}`);
        }
      }
    } catch (error) {
      console.error('Error deleting chat:', error);
      throw error;
    }
  }

  // Helper method to format chat list items
  private async formatChatListItem(
    chatId: string,
    chatData: any,
    currentUserId: string
  ): Promise<ChatListItem> {
    let name: string;
    let photoURL: string | undefined;

    if (chatData.type === 'group' || chatData.participants.length > 2) {
      name = chatData.name || 'Unnamed Group';
      photoURL = chatData.photoURL || undefined;
    } else {
      // For direct chats, get the other participant's info
      const otherParticipantId = chatData.participants.find((id: string) => id !== currentUserId);
      
      if (otherParticipantId) {
        const participantInfo = chatData.participantDetails?.[otherParticipantId];
        
        // Check if we have valid participant info that's not "Unknown User"
        if (participantInfo?.name && participantInfo.name !== 'Unknown User' && participantInfo.name.trim() !== '') {
          name = participantInfo.name;
          photoURL = participantInfo.photoURL || undefined;
        } else {
          // Fetch user data if not available, is "Unknown User", or is empty
          try {
            const user = await userService.getUserById(otherParticipantId);
            console.log('Fetched user in chatService:', user);
            if (user) {
              name = user.displayName || user.name || 'Unknown User';
              photoURL = user.photoURL || user.avatar || undefined;
            } else {
              name = 'Unknown User';
              photoURL = undefined;
            }
          } catch (error) {
            console.error('Error fetching user data in formatChatListItem:', error);
            name = 'Unknown User';
            photoURL = undefined;
          }
        }
      } else {
        name = 'Unknown User';
        photoURL = undefined;
      }
    }

    return {
      id: chatId,
      name,
      lastMessage: chatData.lastMessage?.text || 'No messages yet',
      lastMessageTime: chatData.lastActivity?.toDate() || new Date(),
      unreadCount: chatData.unreadCount?.[currentUserId] || 0,
      isGroup: chatData.type === 'group',
      participants: chatData.participants,
      photoURL,
    };
  }

  // Get chat details by ID
  async getChatDetails(chatId: string): Promise<Chat | null> {
    try {
      const chatRef = doc(db, 'chats', chatId);
      const chatDoc = await getDocs(query(collection(db, 'chats'), where('__name__', '==', chatId)));
      
      if (chatDoc.empty) {
        return null;
      }
      
      const chatData = chatDoc.docs[0].data();
      
      interface ParticipantDetails {
        name: string;
        email: string;
        photoURL?: string | null;
      }

      // Ensure participantDetails is properly populated
      const participantDetails: { [userId: string]: ParticipantDetails } = {};
      
      // If participantDetails exists, use it, otherwise fetch user data
      if (chatData.participantDetails) {
        // Validate and clean up participant details
        for (const [userId, details] of Object.entries(chatData.participantDetails)) {
          const participant = details as ParticipantDetails;
          if (participant && (participant.name === 'Unknown User' || !participant.name || participant.name.trim() === '')) {
            // Fetch fresh user data if current data is invalid
            try {
              const user = await userService.getUserById(userId);
              if (user) {
                participantDetails[userId] = {
                  name: user.displayName || user.name || 'Unknown User',
                  email: user.email || '',
                  photoURL: user.photoURL || user.avatar || null
                };
              } else {
                participantDetails[userId] = {
                  name: 'Unknown User',
                  email: '',
                  photoURL: null
                };
              }
            } catch (error) {
              console.error('Error fetching user data for participant details:', error);
              participantDetails[userId] = {
                name: 'Unknown User',
                email: '',
                photoURL: null
              };
            }
          } else {
            // Use existing valid data
            participantDetails[userId] = {
              name: participant.name || 'Unknown User',
              email: participant.email || '',
              photoURL: participant.photoURL || null
            };
          }
        }
      } else {
        // If no participantDetails, fetch for all participants
        for (const userId of chatData.participants) {
          try {
            const user = await userService.getUserById(userId);
            if (user) {
              participantDetails[userId] = {
                name: user.displayName || user.name || 'Unknown User',
                email: user.email || '',
                photoURL: user.photoURL || user.avatar || null
              };
            } else {
              participantDetails[userId] = {
                name: 'Unknown User',
                email: '',
                photoURL: null
              };
            }
          } catch (error) {
            console.error('Error fetching user data for participant:', error);
            participantDetails[userId] = {
              name: 'Unknown User',
              email: '',
              photoURL: null
            };
          }
        }
      }

      return {
        id: chatDoc.docs[0].id,
        participants: chatData.participants,
        participantDetails,
        type: chatData.type,
        name: chatData.name,
        description: chatData.description,
        photoURL: chatData.photoURL,
        createdBy: chatData.createdBy,
        createdAt: chatData.createdAt.toDate(),
        isGroup: chatData.type === 'group',
        lastMessage: chatData.lastMessage ? {
          text: chatData.lastMessage.text,
          senderId: chatData.lastMessage.senderId,
          timestamp: chatData.lastMessage.timestamp.toDate(),
          type: chatData.lastMessage.type
        } : undefined,
        lastActivity: chatData.lastActivity.toDate(),
        unreadCount: chatData.unreadCount || {}
      } as Chat;
    } catch (error) {
      console.error('Error getting chat details:', error);
      return null;
    }
  }

  // Listen to chat details changes in real-time
  listenToChatDetails(chatId: string, callback: (chat: Chat | null) => void): () => void {
    try {
      const chatRef = doc(db, 'chats', chatId);
      
      const unsubscribe = onSnapshot(chatRef, async (docSnapshot) => {
        if (!docSnapshot.exists()) {
          callback(null);
          return;
        }
        
        const chatData = docSnapshot.data();
        
        interface ParticipantDetails {
          name: string;
          email: string;
          photoURL?: string | null;
        }

        // Ensure participantDetails is properly populated
        const participantDetails: { [userId: string]: ParticipantDetails } = {};
        
        // If participantDetails exists, use it, otherwise fetch user data
        if (chatData.participantDetails) {
          // Validate and clean up participant details
          for (const [userId, details] of Object.entries(chatData.participantDetails)) {
            const participant = details as ParticipantDetails;
            if (participant && (participant.name === 'Unknown User' || !participant.name || participant.name.trim() === '')) {
              // Fetch fresh user data if current data is invalid
              try {
                const user = await userService.getUserById(userId);
                participantDetails[userId] = {
                  name: user?.displayName || 'Unknown User',
                  email: user?.email || '<EMAIL>',
                  photoURL: user?.photoURL || null,
                };
              } catch {
                participantDetails[userId] = {
                  name: 'Unknown User',
                  email: '<EMAIL>',
                  photoURL: null,
                };
              }
            } else {
              participantDetails[userId] = participant;
            }
          }
        }
        
        const chat: Chat = {
          id: docSnapshot.id,
          participants: chatData.participants || [],
          participantDetails,
          type: chatData.type || 'direct',
          name: chatData.name,
          description: chatData.description,
          photoURL: chatData.photoURL,
          createdBy: chatData.createdBy,
          createdAt: chatData.createdAt.toDate(),
          lastMessage: chatData.lastMessage,
          lastActivity: chatData.lastActivity.toDate(),
          unreadCount: chatData.unreadCount || {}
        };
        
        callback(chat);
      });
      
      return unsubscribe;
    } catch (error) {
      console.error('Error listening to chat details:', error);
      throw error;
    }
  }

  // Cleanup listeners
  cleanup(): void {
    this.messageListeners.forEach((unsubscribe) => unsubscribe());
    this.messageListeners.clear();
    
    this.chatListListeners.forEach((unsubscribe) => unsubscribe());
    this.chatListListeners.length = 0;
  }
}

// Export singleton instance
export const chatService = new ChatService();
export default chatService;