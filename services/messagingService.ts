import AsyncStorage from '@react-native-async-storage/async-storage';
import { addDoc, collection } from 'firebase/firestore';
import { db } from '../config/firebase';
import { authService } from './authService';

// Declare window for browser environment
declare const window: any;
import { ChatListItem, chatService, Message } from './chatService';
import { Contact, userService } from './userService';
import { SocketMessage, TypingUser, webSocketService } from './websocketService';

export interface TypingIndicator {
  chatId: string;
  users: TypingUser[];
}

export interface UnreadMessageCount {
  total: number;
  byChatId: { [chatId: string]: number };
}

class MessagingService {
  private typingTimers: Map<string, ReturnType<typeof setTimeout>> = new Map();
  private currentChatId: string | null = null;
  private messageListeners: Map<string, Function[]> = new Map();

  // Initialize messaging service
  async initialize(): Promise<boolean> {
    try {
      // Connect to WebSocket
      const connected = await webSocketService.connect();
      
      if (connected) {
        this.setupWebSocketListeners();
        
        // Restore last opened chat
        // Only run in web environment
        const isBrowser = typeof window !== 'undefined' && typeof window === 'object';
        if (isBrowser) {
          const lastChatId = await AsyncStorage.getItem('lastOpenedChat');
          if (lastChatId) {
            this.setCurrentChat(lastChatId);
          }
        }
      }

      return connected;
    } catch (error) {
      console.error('Error initializing messaging service:', error);
      return false;
    }
  }

  // Setup WebSocket event listeners
  private setupWebSocketListeners(): void {
    // New message handler
    webSocketService.on('new-message', (message: SocketMessage) => {
      this.emit('new-message', message);
      
      // Update unread count if not in current chat
      if (message.chatId !== this.currentChatId) {
        this.incrementUnreadCount(message.chatId);
      }
    });

    // Typing indicators
    webSocketService.on('user-typing', (data: TypingUser) => {
      this.emit('user-typing', data);
    });

    webSocketService.on('user-stopped-typing', (data: TypingUser) => {
      this.emit('user-stopped-typing', data);
    });

    // Message reactions
    webSocketService.on('reaction-updated', (data: any) => {
      this.emit('reaction-updated', data);
    });

    // User presence
    webSocketService.on('user-presence-changed', (data: any) => {
      this.emit('user-presence-changed', data);
    });

    // Connection status
    webSocketService.on('connected', () => {
      this.emit('connection-status-changed', { connected: true });
    });

    webSocketService.on('disconnected', () => {
      this.emit('connection-status-changed', { connected: false });
    });

    webSocketService.on('reconnected', () => {
      this.emit('connection-status-changed', { connected: true, reconnected: true });
    });

    // Error handling
    webSocketService.on('message-error', (error: any) => {
      this.emit('message-error', error);
    });

    webSocketService.on('connection_error', (error: any) => {
      this.emit('connection-error', error);
    });
  }

  // Set current active chat
  setCurrentChat(chatId: string | null): void {
    if (this.currentChatId) {
      webSocketService.leaveChat(this.currentChatId);
    }

    this.currentChatId = chatId;

    // Only run in web environment
    const isBrowser = typeof window !== 'undefined' && typeof window === 'object';
    if (isBrowser) {
      if (chatId) {
        webSocketService.joinChat(chatId);
        chatService.markMessagesAsRead(chatId);
        AsyncStorage.setItem('lastOpenedChat', chatId);
      } else {
        AsyncStorage.removeItem('lastOpenedChat');
      }
    }
  }

  // Get current chat ID
  getCurrentChatId(): string | null {
    return this.currentChatId;
  }

  // Send message with typing indicator cleanup
  async sendMessage(
    chatId: string, 
    text: string, 
    type: 'text' | 'image' | 'file' = 'text',
    options?: {
      replyTo?: string;
      imageUrl?: string;
      fileUrl?: string;
      fileName?: string;
      fileSize?: number;
    }
  ): Promise<void> {
    try {
      // Stop typing indicator
      this.stopTyping(chatId);

      // Send message through chat service (which uses WebSocket)
      await chatService.sendMessage(
        chatId,
        text,
        type,
        options?.replyTo,
        options?.imageUrl,
        options?.fileUrl,
        options?.fileName,
        options?.fileSize
      );

      this.emit('message-sent', { chatId, text, type });
    } catch (error) {
      console.error('Error sending message:', error);
      this.emit('message-error', { error: 'Failed to send message' });
      throw error;
    }
  }

  // Start typing indicator with auto-stop timer
  startTyping(chatId: string): void {
    webSocketService.startTyping(chatId);

    // Clear existing timer
    if (this.typingTimers.has(chatId)) {
      clearTimeout(this.typingTimers.get(chatId)!);
    }

    // Auto-stop after 3 seconds
    const timer = setTimeout(() => {
      this.stopTyping(chatId);
    }, 3000) as ReturnType<typeof setTimeout>;

    this.typingTimers.set(chatId, timer);
  }

  // Stop typing indicator
  stopTyping(chatId: string): void {
    webSocketService.stopTyping(chatId);

    if (this.typingTimers.has(chatId)) {
      clearTimeout(this.typingTimers.get(chatId)!);
      this.typingTimers.delete(chatId);
    }
  }

  // Create new chat and start conversation
  async createChatAndStart(
    participantIds: string[],
    type: 'direct' | 'group' = 'direct',
    name?: string,
    description?: string,
    photoURL?: string
  ): Promise<string> {
    try {
      const chatId = await chatService.createChat(participantIds, type, name, description, photoURL);
      this.setCurrentChat(chatId);
      this.emit('chat-created', { chatId, type, participants: participantIds });
      return chatId;
    } catch (error) {
      console.error('Error creating chat:', error);
      throw error;
    }
  }

  // Send notification (stub implementation)
  async sendNotification(
    recipientUserId: string,
    payload: any
  ): Promise<boolean> {
    try {
      const notification = {
        userId: recipientUserId,
        ...payload,
        read: false,
        timestamp: new Date(),
      };
      await addDoc(collection(db, 'notifications'), notification);
      return true;
    } catch (error) {
      console.error('Error sending notification:', error);
      return false;
    }
  }

  // Get user's chat list with real-time updates
  async getUserChats(): Promise<ChatListItem[]> {
    const currentUser = await authService.getCurrentUser();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    return chatService.getUserChats(currentUser.uid);
  }

  // Listen to chat list updates
  async listenToChats(callback: (chats: ChatListItem[]) => void): Promise<() => void> {
    const currentUser = await authService.getCurrentUser();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    return chatService.listenToUserChats(currentUser.uid, callback);
  }

  // Listen to messages in a specific chat
  listenToChatMessages(chatId: string, callback: (messages: Message[]) => void): () => void {
    return chatService.listenToChatMessages(chatId, callback);
  }

  // Get chat messages with pagination
  getChatMessages(chatId: string, pageSize?: number, lastDoc?: any) {
    return chatService.getChatMessages(chatId, pageSize, lastDoc);
  }

  // Add reaction to message
  addReaction(messageId: string, emoji: string, chatId: string): void {
    chatService.addReaction(messageId, emoji, chatId);
  }

  // Search and add contacts
  async searchUsers(query: string): Promise<any[]> {
    return userService.searchUsers(query);
  }

  async sendContactRequest(recipientId: string, message?: string): Promise<void> {
    return userService.sendContactRequest(recipientId, message);
  }

  async getContactRequests() {
    return userService.getContactRequests();
  }

  async respondToContactRequest(requestId: string, action: 'accept' | 'decline'): Promise<void> {
    return userService.respondToContactRequest(requestId, action);
  }

  // Get user contacts
  async getUserContacts(): Promise<Contact[]> {
    const currentUser = await authService.getCurrentUser();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    return userService.getUserContacts(currentUser.uid);
  }

  // Listen to contact list updates
  async listenToContacts(callback: (contacts: Contact[]) => void): Promise<() => void> {
    const currentUser = await authService.getCurrentUser();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    return userService.listenToUserContacts(currentUser.uid, callback);
  }

  // Get unread message counts
  async getUnreadCounts(): Promise<UnreadMessageCount> {
    try {
      const chats = await this.getUserChats();
      let total = 0;
      const byChatId: { [chatId: string]: number } = {};

      chats.forEach(chat => {
        const count = chat.unreadCount || 0;
        total += count;
        byChatId[chat.id] = count;
      });

      return { total, byChatId };
    } catch (error) {
      console.error('Error getting unread counts:', error);
      return { total: 0, byChatId: {} };
    }
  }

  // Helper method to increment unread count
  private async incrementUnreadCount(chatId: string): Promise<void> {
    try {
      // This would typically be handled by Firestore triggers in production
      // For now, we emit an event that the UI can handle
      this.emit('unread-count-changed', { chatId });
    } catch (error) {
      console.error('Error incrementing unread count:', error);
    }
  }

  // Check connection status
  isConnected(): boolean {
    return webSocketService.isSocketConnected();
  }

  // Reconnect WebSocket
  async reconnect(): Promise<boolean> {
    return await webSocketService.connect();
  }

  // Event listener management
  on(event: string, callback: Function): void {
    if (!this.messageListeners.has(event)) {
      this.messageListeners.set(event, []);
    }
    this.messageListeners.get(event)!.push(callback);
  }

  off(event: string, callback?: Function): void {
    if (!this.messageListeners.has(event)) return;
    
    if (callback) {
      const listeners = this.messageListeners.get(event)!;
      const index = listeners.indexOf(callback);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    } else {
      this.messageListeners.set(event, []);
    }
  }

  private emit(event: string, data?: any): void {
    if (this.messageListeners.has(event)) {
      this.messageListeners.get(event)!.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in messaging service event listener for ${event}:`, error);
        }
      });
    }
  }

  // Cleanup resources
  cleanup(): void {
    // Clear typing timers
    this.typingTimers.forEach(timer => clearTimeout(timer));
    this.typingTimers.clear();

    // Disconnect WebSocket
    webSocketService.disconnect();

    // Cleanup chat service listeners
    chatService.cleanup();

    // Clear event listeners
    this.messageListeners.clear();

    // Clear current chat
    this.currentChatId = null;
  }

  // Update user presence
  updatePresence(isOnline: boolean): void {
    webSocketService.updatePresence(isOnline);
  }
}

// Export singleton instance
export const messagingService = new MessagingService();
export default messagingService;