import { collection, doc, getDoc, getDocs, query, serverTimestamp, setDoc, updateDoc, where } from 'firebase/firestore';
import { getAuth, db } from '../config/firebase';

export interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  photoURL: string | null;
  isOnline: boolean;
  lastSeen: Date;
  searchTokens: string[];
  createdAt: Date;
}

export interface AuthResponse {
  success: boolean;
  user?: UserProfile;
  error?: string;
}

class AuthService {
  constructor() {
    console.log('🔐 AuthService initializing...');
  }

  // Register new user
  async registerUser(
    email: string, 
    password: string, 
    displayName: string
  ): Promise<AuthResponse> {
    try {
      const auth = await getAuth();
      if (!auth) {
        throw new Error('Firebase Auth not initialized');
      }
      
      // Dynamically import createUserWithEmailAndPassword and updateProfile
      const { createUserWithEmailAndPassword, updateProfile } = await import('firebase/auth');
      
      // Create user account
      const userCredential = await createUserWithEmailAndPassword(
        auth, 
        email, 
        password
      );
      
      const user = userCredential.user;

      // Update profile
      await updateProfile(user, {
        displayName: displayName,
      });

      // Create user document in Firestore
      const userProfile: UserProfile = {
        uid: user.uid,
        email: email.toLowerCase(),
        displayName: displayName,
        photoURL: user.photoURL || null,
        isOnline: true,
        lastSeen: new Date(),
        searchTokens: this.generateSearchTokens(displayName + ' ' + email),
        createdAt: new Date(),
      };

      await setDoc(doc(db, 'users', user.uid), userProfile);

      return {
        success: true,
        user: userProfile,
      };

    } catch (error: any) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code),
      };
    }
  }

  // Sign in user
  async signInUser(email: string, password: string): Promise<AuthResponse> {
    try {
      const auth = await getAuth();
      if (!auth) {
        throw new Error('Firebase Auth not initialized');
      }
      
      // Dynamically import signInWithEmailAndPassword
      const { signInWithEmailAndPassword } = await import('firebase/auth');
      
      const userCredential = await signInWithEmailAndPassword(
        auth, 
        email, 
        password
      );
      
      const user = userCredential.user;
      console.log('Firebase Auth user:', user);

      // Get user profile from Firestore
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      let userProfile: UserProfile;

      if (userDoc.exists()) {
        userProfile = userDoc.data() as UserProfile;
        console.log('User profile from Firestore:', userProfile);
      } else {
        // Create profile if it doesn't exist (fallback)
        console.log('User profile not found, creating fallback profile');
        userProfile = {
          uid: user.uid,
          email: email.toLowerCase(),
          displayName: user.displayName || 'Unknown User',
          photoURL: user.photoURL || null,
          isOnline: true,
          lastSeen: new Date(),
          searchTokens: this.generateSearchTokens((user.displayName || '') + ' ' + email),
          createdAt: new Date(),
        };
        await setDoc(doc(db, 'users', user.uid), userProfile);
      }

      return {
        success: true,
        user: userProfile,
      };

    } catch (error: any) {
      console.error('Sign in error:', error);
      return {
        success: false,
        error: this.getErrorMessage(error.code),
      };
    }
  }

  // Sign out user
  async signOutUser(): Promise<AuthResponse> {
    try {
      const auth = await getAuth();
      if (!auth) {
        throw new Error('Firebase Auth not initialized');
      }
      
      // Dynamically import signOut
      const { signOut } = await import('firebase/auth');
      
      await signOut(auth);
      return { success: true };
    } catch (error: any) {
      console.error('Sign out error:', error);
      return {
        success: false,
        error: 'Failed to sign out',
      };
    }
  }

  // Get auth token
  async getAuthToken(): Promise<string | null> {
    const auth = await getAuth();
    if (!auth) return null;
    
    const currentUser = auth.currentUser;
    if (currentUser) {
      try {
        // Dynamically import getIdToken
        const { getIdToken } = await import('firebase/auth');
        const token = await getIdToken(currentUser, true); // Force refresh
        return token;
      } catch (error) {
        console.error('Error getting auth token:', error);
        return null;
      }
    }
    return null;
  }

  // Update user profile
  async updateUserProfile(updates: Partial<UserProfile>): Promise<AuthResponse> {
    const auth = await getAuth();
    if (!auth) {
      return { success: false, error: 'Firebase Auth not initialized' };
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return { success: false, error: 'No authenticated user' };
    }

    try {
      // Dynamically import updateProfile
      const { updateProfile } = await import('firebase/auth');
      
      // Update Firebase Auth profile if display name or photo changed
      if (updates.displayName || updates.photoURL) {
        await updateProfile(currentUser, {
          displayName: updates.displayName,
          photoURL: updates.photoURL,
        });
      }

      // Update Firestore document
      const updateData: any = { ...updates };
      if (updates.displayName) {
        updateData.searchTokens = this.generateSearchTokens(
          updates.displayName + ' ' + currentUser.email!
        );
      }

      await updateDoc(doc(db, 'users', currentUser.uid), updateData);

      // Update contact records for users who have this user as a contact
      if (updates.displayName || updates.photoURL) {
        try {
          console.log('🔍 Searching for contact records to update for user:', currentUser.uid);
          console.log('🔍 Updates to apply:', updates);
          
          // Find all contact records where this user is the contact
          const contactsQuery = query(
            collection(db, 'contacts'),
            where('contactId', '==', currentUser.uid)
          );
          
          const contactsSnapshot = await getDocs(contactsQuery);
          console.log('🔍 Found', contactsSnapshot.docs.length, 'contact records to update');
          
          // Log each contact record found
          contactsSnapshot.forEach((doc) => {
            const data = doc.data();
            console.log('🔍 Contact record:', {
              id: doc.id,
              userId: data.userId,
              contactId: data.contactId,
              currentName: data.contactName,
              currentPhotoURL: data.contactPhotoURL
            });
          });
          
          // Update each contact record with the new information
          const batchUpdates: Promise<void>[] = [];
          contactsSnapshot.forEach((contactDoc) => {
            const contactUpdate: any = {};
            if (updates.displayName) {
              contactUpdate.contactName = updates.displayName;
            }
            if (updates.photoURL) {
              contactUpdate.contactPhotoURL = updates.photoURL;
            }
            
            console.log('🔄 Updating contact record:', contactDoc.id, 'with:', contactUpdate);
            batchUpdates.push(
              updateDoc(contactDoc.ref, contactUpdate)
            );
          });
          
          // Execute all updates in parallel
          if (batchUpdates.length > 0) {
            await Promise.all(batchUpdates);
            console.log(`✅ Updated ${batchUpdates.length} contact records for profile changes`);
          } else {
            console.log('⚠️ No contact records found to update');
          }
        } catch (error) {
          console.error('❌ Error updating contact records:', error);
          // Don't fail the profile update if contact updates fail
        }
      }

      return { success: true };
    } catch (error: any) {
      console.error('Error updating profile:', error);
      return {
        success: false,
        error: 'Failed to update profile',
      };
    }
  }

  // Listen for auth state changes
  async onAuthStateChange(callback: (user: any | null) => void) {
    const auth = await getAuth();
    if (!auth) {
      throw new Error('Firebase Auth not initialized');
    }
    
    // Dynamically import onAuthStateChanged
    const { onAuthStateChanged } = await import('firebase/auth');
    
    console.log('Setting up auth state change listener');
    return onAuthStateChanged(auth, callback);
  }

  // Get current authenticated user
  async getCurrentUser() {
    try {
      const auth = await getAuth();
      if (auth && auth.currentUser) {
        return {
          uid: auth.currentUser.uid,
          email: auth.currentUser.email,
          displayName: auth.currentUser.displayName,
          photoURL: auth.currentUser.photoURL,
        };
      }
      return null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Get current user ID
  async getCurrentUserId(): Promise<string | null> {
    try {
      const auth = await getAuth();
      if (auth && auth.currentUser) {
        return auth.currentUser.uid;
      }
      return null;
    } catch (error) {
      console.error('Error getting current user ID:', error);
      return null;
    }
  }

  // Force refresh of user state
  async refreshUserState(): Promise<void> {
    const auth = await getAuth();
    if (!auth) return;
    
    const currentUser = auth.currentUser;
    if (currentUser) {
      // Dynamically import reload
      const { reload } = await import('firebase/auth');
      await reload(currentUser);
    }
  }

  // Generate search tokens for user discovery
  private generateSearchTokens(text: string): string[] {
    const tokens = new Set<string>();
    const lowerText = text.toLowerCase();
    
    // Split by spaces and generate tokens for each word
    const words = lowerText.split(' ').filter(word => word.length > 0);
    
    words.forEach(word => {
      // Add progressively longer substrings
      for (let i = 1; i <= word.length; i++) {
        tokens.add(word.substring(0, i));
      }
    });
    
    return Array.from(tokens);
  }

  // Get user-friendly error messages
  private getErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/email-already-in-use':
        return 'This email is already registered. Please use a different email or sign in.';
      case 'auth/weak-password':
        return 'Password should be at least 6 characters long.';
      case 'auth/invalid-email':
        return 'Please enter a valid email address.';
      case 'auth/user-not-found':
        return 'No account found with this email. Please check your email or register.';
      case 'auth/wrong-password':
        return 'Incorrect password. Please try again.';
      case 'auth/invalid-credential':
        return 'Invalid email or password. Please check your credentials.';
      case 'auth/too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'auth/network-request-failed':
        return 'Network error. Please check your internet connection.';
      default:
        return 'An error occurred. Please try again.';
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;