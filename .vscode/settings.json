{"editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit", "source.sortMembers": "explicit"}, "typescript.preferences.exclude": ["**/node_modules/**", "**/node_modules/expo-haptics/**", "**/node_modules/expo-image/**"], "typescript.suggest.includeCompletionsForModuleExports": true, "typescript.updateImportsOnFileMove.enabled": "always", "files.exclude": {"**/node_modules/expo-haptics/tsconfig.json": true, "**/node_modules/expo-image/tsconfig.json": true}}