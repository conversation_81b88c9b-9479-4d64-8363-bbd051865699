{"name": "<PERSON><PERSON><PERSON><PERSON>-messenger", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^15.0.2", "@react-native-async-storage/async-storage": "2.2.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.6.4", "@react-navigation/native": "^7.1.6", "date-fns": "^3.6.0", "expo": "^54.0.0", "expo-blur": "~15.0.7", "expo-constants": "~18.0.8", "expo-dev-client": "~6.0.12", "expo-device": "~8.0.7", "expo-file-system": "~19.0.12", "expo-font": "~14.0.8", "expo-haptics": "~15.0.7", "expo-image": "~3.0.8", "expo-image-manipulator": "~14.0.7", "expo-image-picker": "~17.0.8", "expo-linking": "~8.0.8", "expo-media-library": "~18.1.1", "expo-notifications": "~0.32.11", "expo-router": "~6.0.1", "expo-splash-screen": "~31.0.9", "expo-status-bar": "~3.0.8", "expo-symbols": "~1.0.7", "expo-system-ui": "~6.0.7", "expo-video": "~3.0.11", "expo-audio": "~1.0.0", "expo-video-thumbnails": "~10.0.7", "expo-web-browser": "~15.0.7", "firebase": "^12.2.1", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-gesture-handler": "~2.28.0", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-uuid": "^2.0.2", "react-native-web": "^0.21.0", "react-native-webview": "13.15.0", "socket.io-client": "^4.7.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/config-types": "^54.0.0", "@playwright/test": "^1.55.0", "@types/react": "~19.1.10", "@types/react-native": "^0.72.8", "eslint": "^9.25.0", "eslint-config-expo": "~10.0.0", "expo-module-scripts": "^4.1.10", "playwright": "^1.55.0", "typescript": "~5.9.2"}, "private": true}