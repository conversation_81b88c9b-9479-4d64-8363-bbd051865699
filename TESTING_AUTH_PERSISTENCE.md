# Testing Authentication Persistence

## Overview

This document explains how to test that Firebase Authentication persistence is working correctly in the Namoshkar Messenger app.

## Prerequisites

1. The app should be running in Expo Go or a simulator
2. You should have valid Firebase credentials in your `.env` file
3. You should have created at least one test user account

## Testing Steps

### 1. Initial Login Test

1. Open the app in Expo Go
2. Navigate to the sign-in screen
3. Log in with a valid account
4. Verify you're redirected to the correct screen:
   - Profile setup screen if profile is incomplete
   - Main tabs screen if profile is complete
5. Check the console logs for successful auth state detection

### 2. App Reload Test

1. While logged in, reload the app (shake device and tap "Reload" or press `r` in the terminal)
2. Observe the console logs:
   - Look for "Auth state changed" messages
   - Verify the user UID is detected immediately after reload
3. Verify you're not redirected to the sign-in screen
4. Confirm you're redirected to the correct screen based on profile completion

### 3. App Restart Test

1. Close the Expo Go app completely
2. Reopen Expo Go and launch the app
3. Observe the loading screen briefly appears
4. Verify you're not redirected to the sign-in screen
5. Confirm you're redirected to the correct screen based on profile completion

### 4. Logout and Login Cycle Test

1. While logged in, navigate to settings and log out
2. Verify you're redirected to the sign-in screen
3. Log back in with the same account
4. Verify persistence works again after reload/restart

## Expected Console Log Patterns

When authentication persistence is working correctly, you should see logs like:

```
✅ Firebase Auth initialized with explicit AsyncStorage persistence for React Native
AuthProvider mounted, setting up auth state listener
🔐 Auth state changed: User <user-uid>
Current user on mount: User <user-uid>
User profile from Firestore on mount: <profile-data>
Profile completion check on mount: <profile-status>
Index screen - Auth state: {"hasCompletedProfile": true, "loading": false, "user": true}
Index screen - User UID: <user-uid>
Index screen - Redirecting to: /(tabs)
```

After a reload, you should see similar logs showing the user state is immediately available:

```
AuthProvider mounted, setting up auth state listener
Current user on mount: User <user-uid>
```

## Troubleshooting

### If You're Still Redirected to Sign-In

1. Check console logs for initialization errors
2. Verify AsyncStorage is properly configured:
   ```bash
   # Check AsyncStorage version
   npm list @react-native-async-storage/async-storage
   ```
3. Ensure only the Firebase JS SDK is installed (not @react-native-firebase/*):
   ```bash
   npm list firebase
   ```
4. Clear Expo cache and restart:
   ```bash
   npx expo start --clear
   ```

### If Auth State Takes Too Long to Load

1. Check that the `authInitialized` promise resolves correctly
2. Verify there are no network issues with Firebase
3. Check that Firestore rules allow reading user documents

## Manual Testing Script

You can also manually test by adding this to any component:

```javascript
// Test auth state
import { auth } from '../config/firebase';
import { onAuthStateChanged } from 'firebase/auth';

// Check current user
console.log('Current user:', auth.currentUser);

// Listen for changes
const unsubscribe = onAuthStateChanged(auth, (user) => {
  console.log('Auth state changed:', user);
});

// Remember to unsubscribe when component unmounts
// unsubscribe();
```

## Common Issues and Solutions

### "Component auth has not been registered yet"

This error occurs when there are conflicts between Firebase packages. Solution:
1. Remove @react-native-firebase/* packages
2. Use only firebase package
3. Clear npm cache and reinstall

### Auth State Not Persisting

1. Verify `getReactNativePersistence` is properly configured
2. Check that AsyncStorage is correctly imported
3. Ensure Firebase is initialized before use

### Infinite Loading Screen

1. Check that `authInitialized` promise resolves
2. Add timeout to prevent infinite waiting
3. Verify no circular dependencies in auth initialization