{"permissions": {"allow": ["Bash(npm run lint)", "Bash(npm run build:*)", "Bash(npm install:*)", "Bash(npm run)", "Bash(find:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(echo:*)", "Bash(git add:*)", "Bash(npx expo lint:*)", "Bash(npx expo start:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npx tsc:*)", "Bash(npm start:*)", "Bash(node:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run:*)", "Bash(firebase deploy:*)", "Bash(npx expo install:*)", "Bash(pod install:*)", "<PERSON><PERSON>(adb:*)", "Read(//Users/<USER>/Library/Android/**)", "Bash(export:*)", "Bash($ANDROID_HOME/emulator/emulator:*)", "Bash(lsof:*)", "mcp__context7__resolve-library-id", "<PERSON><PERSON>(cat:*)", "Bash(npx eas build:*)", "Bash(npx expo build:android:*)", "Bash(npx expo login:*)", "Bash(EAS_PROJECT_ID=namoshkar-messenger-local npx eas build --platform android --profile development)", "WebFetch(domain:expo.dev)", "Bash(eas build:list:*)", "Bash(eas build:*)", "Bash(expo build:android:*)", "Bash(git commit:*)", "mcp__context7__get-library-docs", "WebSearch"], "deny": [], "ask": []}}