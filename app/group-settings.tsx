import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { chatService } from '@/services/chatService';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState, useRef } from 'react';
import {
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { Chat } from '@/types/messenger';

export default function GroupSettingsScreen() {
  const { user } = useAuth();
  const { chatId, createdBy } = useLocalSearchParams<{ chatId: string, createdBy: string }>();
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [groupAvatar, setGroupAvatar] = useState<string | null>(null);
  const [chat, setChat] = useState<Chat | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const isCreator = user?.uid === createdBy;
  const isMounted = useRef(true);

  useEffect(() => {
    loadGroupDetails();
    
    return () => {
      isMounted.current = false;
    };
  }, [chatId]);

  const loadGroupDetails = async () => {
    try {
      setLoading(true);
      const chatDetails = await chatService.getChatDetails(chatId);
      if (chatDetails && isMounted.current) {
        setChat(chatDetails);
        setGroupName(chatDetails.name || '');
        setGroupDescription(chatDetails.description || '');
        setGroupAvatar(chatDetails.photoURL || null);
      }
    } catch (error) {
      console.error('Error loading group details:', error);
      Alert.alert('Error', 'Failed to load group details');
    } finally {
      setLoading(false);
    }
  };

  const pickGroupAvatar = async () => {
    if (!isCreator) {
      Alert.alert('Permission Denied', 'Only the group creator can change the group image');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled && result.assets[0]) {
      setGroupAvatar(result.assets[0].uri);
    }
  };

  const updateGroupDetails = async () => {
    if (!isCreator) {
      Alert.alert('Permission Denied', 'Only the group creator can update group details');
      return;
    }

    if (!groupName.trim()) {
      Alert.alert('Error', 'Please enter a group name');
      return;
    }

    try {
      setUpdating(true);
      
      const updateData: any = {
        name: groupName.trim(),
      };
      
      if (groupDescription.trim()) {
        updateData.description = groupDescription.trim();
      }
      
      if (groupAvatar) {
        updateData.photoURL = groupAvatar;
      }
      
      await chatService.updateChat(chatId, updateData);

      Alert.alert('Success', 'Group details updated successfully');
      if (isMounted.current) {
        // Update local state with new values
        setChat((prev: Chat | null) => {
          if (!prev) return prev;
          const updatedChat: any = {
            ...prev,
            name: groupName.trim(),
          };
          
          if (groupDescription.trim()) {
            updatedChat.description = groupDescription.trim();
          }
          
          if (groupAvatar) {
            updatedChat.photoURL = groupAvatar;
          }
          
          return updatedChat;
        });
        // Don't navigate back - stay on screen to show updated values
      }
    } catch (error) {
      console.error('Error updating group details:', error);
      Alert.alert('Error', 'Failed to update group details');
    } finally {
      setUpdating(false);
    }
  };

  const deleteGroup = () => {
    if (!isCreator) {
      Alert.alert('Permission Denied', 'Only the group creator can delete the group');
      return;
    }

    Alert.alert(
      'Delete Group',
      'Are you sure you want to delete this group? This action cannot be undone and all messages will be permanently deleted.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setUpdating(true);
              await chatService.deleteChat(chatId);
              Alert.alert('Success', 'Group deleted successfully');
              if (isMounted.current) {
                router.back();
              }
            } catch (error) {
              console.error('Error deleting group:', error);
              Alert.alert('Error', 'Failed to delete group');
            } finally {
              setUpdating(false);
            }
          },
        },
      ]
    );
  };

  const leaveGroup = () => {
    Alert.alert(
      'Leave Group',
      'Are you sure you want to leave this group? You will need to be added back by a member to rejoin.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Leave',
          style: 'destructive',
          onPress: async () => {
            try {
              setUpdating(true);
              await chatService.removeMemberFromGroup(chatId, user!.uid);
              Alert.alert('Success', 'You have left the group');
              if (isMounted.current) {
                router.back();
              }
            } catch (error) {
              console.error('Error leaving group:', error);
              Alert.alert('Error', 'Failed to leave group');
            } finally {
              setUpdating(false);
            }
          },
        },
      ]
    );
  };

  const navigateToMembers = () => {
    if (!chat?.participants || !isMounted.current) {
      Alert.alert('Error', 'Group data not loaded yet. Please try again.');
      return;
    }
    
    router.push({
      pathname: '/group-members',
      params: { 
        chatId, 
        createdBy,
        participants: JSON.stringify(chat.participants)
      }
    });
  };

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? '#000000' : '#f8f9fa' }]}>
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B35" />
            <Text style={[styles.loadingText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              Loading group settings...
            </Text>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#000000' : '#f8f9fa' }]}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={[styles.header, { 
          backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 8,
          elevation: 4,
        }]}>
          <TouchableOpacity onPress={() => router.back()} style={[styles.cancelButton, {
            backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
          }]}>
            <Text style={styles.cancelText}>Back</Text>
          </TouchableOpacity>
          
          <Text style={[styles.headerTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
            Group Settings
          </Text>
          
          <TouchableOpacity 
            onPress={updateGroupDetails} 
            style={[styles.saveButton, { 
              opacity: !updating ? 1 : 0.5,
              backgroundColor: '#FF6B35',
              shadowColor: '#FF6B35',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.3,
              shadowRadius: 4,
              elevation: 3,
            }]}
            disabled={updating}
          >
            {updating ? (
              <ActivityIndicator size="small" color="#ffffff" />
            ) : (
              <Text style={[styles.saveText, { color: '#ffffff' }]}>Save</Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Group Info Section */}
        <View style={[styles.groupInfoContainer, { 
          backgroundColor: isDark ? 'rgba(26, 26, 26, 0.8)' : 'rgba(255, 255, 255, 0.9)',
          shadowColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 12,
          elevation: 2,
        }]}>
          <TouchableOpacity onPress={pickGroupAvatar} style={styles.avatarButton} disabled={!isCreator}>
            {groupAvatar ? (
              <Image source={{ uri: groupAvatar }} style={[styles.groupAvatar, {
                shadowColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 1,
                shadowRadius: 4,
              }]} />
            ) : (
              <View style={[styles.placeholderAvatar, { 
                backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
                shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 1,
                shadowRadius: 4,
                elevation: 2,
              }]}>
                <IconSymbol name="camera.fill" size={24} color="#FF6B35" />
              </View>
            )}
            {!isCreator && (
              <View style={styles.disabledOverlay}>
                <IconSymbol name="lock.fill" size={16} color="rgba(255, 255, 255, 0.8)" />
              </View>
            )}
          </TouchableOpacity>
          
          <View style={styles.groupInfoInputs}>
            <TextInput
              style={[styles.groupNameInput, { 
                color: isDark ? '#ffffff' : '#000000',
                borderBottomColor: '#FF6B35',
              }]}
              placeholder="Group name"
              placeholderTextColor={isDark ? 'rgba(142, 142, 147, 0.7)' : 'rgba(142, 142, 147, 0.8)'}
              value={groupName}
              onChangeText={setGroupName}
              maxLength={50}
              editable={isCreator}
            />
            
            <TextInput
              style={[styles.groupDescriptionInput, { 
                color: isDark ? '#ffffff' : '#000000',
                borderBottomColor: isDark ? 'rgba(142, 142, 147, 0.3)' : 'rgba(142, 142, 147, 0.4)',
              }]}
              placeholder="Description (optional)"
              placeholderTextColor={isDark ? 'rgba(142, 142, 147, 0.7)' : 'rgba(142, 142, 147, 0.8)'}
              value={groupDescription}
              onChangeText={setGroupDescription}
              maxLength={100}
              multiline
              editable={isCreator}
            />
          </View>
        </View>

        {/* Permissions Info */}
        {!isCreator && (
          <View style={[styles.permissionsContainer, {
            backgroundColor: isDark ? 'rgba(255, 107, 53, 0.1)' : 'rgba(255, 107, 53, 0.05)',
          }]}>
            <IconSymbol name="info.circle.fill" size={16} color="#FF6B35" />
            <Text style={[styles.permissionsText, { color: isDark ? '#8e8e93' : '#6c6c70' }]}>
              Only the group creator can update group information
            </Text>
          </View>
        )}

        {/* Member Management Section */}
        <View style={styles.section}>
          <TouchableOpacity 
            onPress={navigateToMembers}
            style={[styles.sectionCard, {
              backgroundColor: isDark ? 'rgba(26, 26, 26, 0.8)' : 'rgba(255, 255, 255, 0.9)',
              shadowColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 1,
              shadowRadius: 8,
              elevation: 1,
            }]}
          >
            <View style={styles.sectionCardContent}>
              <View style={[styles.sectionIconContainer, {
                backgroundColor: isDark ? 'rgba(52, 199, 89, 0.2)' : 'rgba(52, 199, 89, 0.1)',
              }]}>
                <IconSymbol name="person.2.fill" size={24} color="#34C759" />
              </View>
              <View style={styles.sectionTextContainer}>
                <Text style={[styles.sectionTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
                  Manage Members
                </Text>
                <Text style={[styles.sectionDescription, { color: isDark ? 'rgba(142, 142, 147, 0.8)' : 'rgba(108, 108, 112, 0.8)' }]}>
                  View and manage group members
                </Text>
              </View>
              <IconSymbol name="chevron.right" size={20} color={isDark ? '#8e8e93' : '#6c6c70'} />
            </View>
          </TouchableOpacity>
        </View>

        {/* Danger Zone */}
        <View style={styles.dangerZone}>
          <Text style={[styles.dangerZoneTitle, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
            DANGER ZONE
          </Text>

          {isCreator ? (
            <TouchableOpacity 
              onPress={deleteGroup} 
              style={[styles.dangerButton, styles.deleteButton, {
                backgroundColor: isDark ? 'rgba(255, 59, 48, 0.2)' : 'rgba(255, 59, 48, 0.1)',
              }]}
              disabled={updating}
            >
              <IconSymbol name="trash.fill" size={20} color="#FF3B30" />
              <Text style={[styles.dangerButtonText, styles.deleteButtonText, { color: '#FF3B30' }]}>
                Delete Group
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity 
              onPress={leaveGroup} 
              style={[styles.dangerButton, styles.leaveButton, {
                backgroundColor: isDark ? 'rgba(255, 149, 0, 0.2)' : 'rgba(255, 149, 0, 0.1)',
              }]}
              disabled={updating}
            >
              <IconSymbol name="arrow.left.square.fill" size={20} color="#FF9500" />
              <Text style={[styles.dangerButtonText, styles.leaveButtonText, { color: '#FF9500' }]}>
                Leave Group
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  cancelText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF6B35',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  saveText: {
    fontSize: 16,
    fontWeight: '700',
  },
  groupInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    marginHorizontal: 20,
    marginVertical: 16,
    borderRadius: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  avatarButton: {
    position: 'relative',
    marginRight: 20,
  },
  groupAvatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
  },
  placeholderAvatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
  },
  groupInfoInputs: {
    flex: 1,
  },
  groupNameInput: {
    fontSize: 18,
    paddingVertical: 16,
    borderBottomWidth: 2,
    fontWeight: '500',
    letterSpacing: -0.2,
  },
  groupDescriptionInput: {
    fontSize: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    fontWeight: '400',
    letterSpacing: -0.2,
    marginTop: 8,
    minHeight: 40,
    maxHeight: 80,
  },
  permissionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginVertical: 16,
    padding: 16,
    borderRadius: 12,
    gap: 12,
  },
  permissionsText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  section: {
    paddingHorizontal: 20,
    marginVertical: 8,
  },
  sectionCard: {
    padding: 16,
    borderRadius: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  sectionCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  sectionTextContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 2,
    letterSpacing: -0.2,
  },
  sectionDescription: {
    fontSize: 14,
    fontWeight: '400',
  },
  dangerZone: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 32,
  },
  dangerZoneTitle: {
    fontSize: 13,
    fontWeight: '600',
    letterSpacing: 0.5,
    marginBottom: 12,
  },
  dangerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
  },
  deleteButton: {
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
  },
  leaveButton: {
    backgroundColor: 'rgba(255, 149, 0, 0.1)',
  },
  dangerButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  deleteButtonText: {
    color: '#FF3B30',
  },
  leaveButtonText: {
    color: '#FF9500',
  },
});