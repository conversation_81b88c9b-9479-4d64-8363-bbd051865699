import { useAuth } from '@/contexts/AuthContext';
import { Redirect } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, View } from 'react-native';

export default function Index() {
  const { user, loading, hasCompletedProfile } = useAuth();
  const [initializing, setInitializing] = useState(true);

  useEffect(() => {
    console.log('Index screen - Auth state:', { user: !!user, loading, hasCompletedProfile });
    console.log('Index screen - User UID:', user ? user.uid : 'null');
    
    // Wait for auth state to be determined
    const timer = setTimeout(() => {
      setInitializing(false);
    }, 3000); // Timeout after 3 seconds to prevent infinite loading
    
    if (!loading) {
      clearTimeout(timer);
      setInitializing(false);
    }
    
    return () => clearTimeout(timer);
  }, [loading, user, hasCompletedProfile]);

  // Show loading indicator while determining auth state
  if (initializing || loading) {
    console.log('Index screen - Showing loading indicator');
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#FF6B35" />
      </View>
    );
  }

  // Determine the correct redirect based on auth state and profile completion
  let redirectPath: any = '/sign-in';
  if (user) {
    if (hasCompletedProfile) {
      redirectPath = '/(tabs)';
    } else {
      redirectPath = '/profile-setup';
    }
  }
  
  console.log('Index screen - Redirecting to:', redirectPath);
  
  return <Redirect href={redirectPath} />;
}