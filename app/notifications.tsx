import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Notification, notificationApiService } from '@/services/notificationApiService';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    RefreshControl,
    SafeAreaView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

export default function NotificationsScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  // Dynamic notifications data
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadNotifications();
  }, []);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      // Updated to use the new Firestore-based service
      const notificationsData = await notificationApiService.getUserNotifications(50);
      setNotifications(notificationsData || []); // Handle potential undefined return
    } catch (error) {
      console.error('Error loading notifications:', error);
      // Set empty notifications array to handle the error gracefully
      setNotifications([]);
      
      // Use a more descriptive error message
      Alert.alert(
        'Network Error', 
        'Unable to load notifications. Please check your connection and try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    try {
      setRefreshing(true);
      // Updated to use the new Firestore-based service
      const notificationsData = await notificationApiService.getUserNotifications(50);
      setNotifications(notificationsData || []); // Handle potential undefined return
    } catch (error) {
      console.error('Error refreshing notifications:', error);
      
      // Show user-friendly error without disrupting UX
      Alert.alert(
        'Network Error', 
        'Unable to refresh notifications. Please check your connection and try again.'
      );
    } finally {
      setRefreshing(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleNotificationPress = async (notification: Notification) => {
    // Mark as read using the new Firestore-based service
    try {
      await notificationApiService.markNotificationsAsRead([notification.id]);
      setNotifications(prev => prev.map(n => 
        n.id === notification.id ? {...n, read: true} : n
      ));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
    
    // Handle notification press based on type
    if (notification.data) {
      switch (notification.data.type) {
        case 'message':
          // Navigate to chat
          if (notification.chatId) {
            router.push(`/chat/${notification.chatId}`);
          }
          break;
        case 'contact_request':
          // Navigate to user profile with contact request context
          if (notification.senderId) {
            router.push(`/user-profile?userId=${notification.senderId}&fromNotification=true`);
          }
          break;
        case 'contact_accepted':
          // Navigate to user profile
          if (notification.senderId) {
            router.push(`/user-profile?userId=${notification.senderId}`);
          }
          break;
        case 'group_invite':
          // Navigate to group chat
          if (notification.chatId) {
            router.push(`/chat/${notification.chatId}`);
          }
          break;
        default:
          // For other notification types, just mark as read
          break;
      }
    }
  };

  const handleClearAll = async () => {
    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to delete all notifications? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Clear All', 
          style: 'destructive',
          onPress: async () => {
            try {
              // Use the new Firestore-based service to delete all notifications
              await notificationApiService.deleteAllNotifications();
              setNotifications([]);
            } catch (error) {
              console.error('Error clearing all notifications:', error);
              Alert.alert('Error', 'Failed to clear notifications');
            }
          }
        },
      ]
    );
  };

  const renderNotificationItem = ({ item }: { item: Notification }) => (
    <TouchableOpacity
      style={[styles.notificationItem, {
        backgroundColor: isDark ? 'rgba(26, 26, 26, 0.8)' : 'rgba(255, 255, 255, 0.9)',
        shadowColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 1,
        shadowRadius: 20,
        elevation: 1,
      }]}
      onPress={() => handleNotificationPress(item)}
    >
      <View style={styles.notificationContent}>
        <Text style={[styles.notificationTitle, {
          color: isDark ? '#ffffff' : '#000000',
        }]}>
          {item.title}
        </Text>
        <Text style={[styles.notificationMessage, {
          color: isDark ? 'rgba(142, 142, 147, 0.8)' : 'rgba(108, 108, 112, 0.8)',
        }]} numberOfLines={2}>
          {item.body}
        </Text>
        <Text style={[styles.notificationTime, {
          color: isDark ? 'rgba(142, 142, 147, 0.6)' : 'rgba(108, 108, 112, 0.6)',
        }]}>
          {new Date(item.sentAt).toLocaleString()}
        </Text>
      </View>
      {!item.read && (
        <View style={[styles.unreadIndicator, {
          backgroundColor: '#FF6B35',
        }]} />
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? '#000000' : '#f8f9fa' }]}>
        <SafeAreaView style={styles.safeArea}>
          <View style={[styles.header, { 
            backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          }]}>
            <View style={styles.headerContent}>
              <TouchableOpacity onPress={handleBack} style={[styles.backButton, {
                backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
              }]}>
                <Text style={styles.backText}>Back</Text>
              </TouchableOpacity>
              
              <View style={styles.titleSection}>
                <Text style={[styles.headerTitle, { 
                  color: isDark ? '#ffffff' : '#000000',
                  letterSpacing: -1.2,
                }]}>
                  Notifications
                </Text>
                <View style={[styles.titleUnderline, {
                  backgroundColor: '#FF6B35',
                }]} />
              </View>
              
              <View style={styles.clearButton} />
            </View>
          </View>
          
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B35" />
            <Text style={[styles.loadingText, { color: isDark ? '#8e8e93' : '#6c6c70' }]}>
              Loading notifications...
            </Text>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#000000' : '#f8f9fa' }]}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={[styles.header, { 
          backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
        }]}>
          <View style={styles.headerContent}>
            <TouchableOpacity onPress={handleBack} style={[styles.backButton, {
              backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
            }]}>
              <Text style={styles.backText}>Back</Text>
            </TouchableOpacity>
            
            <View style={styles.titleSection}>
              <Text style={[styles.headerTitle, { 
                color: isDark ? '#ffffff' : '#000000',
                letterSpacing: -1.2,
              }]}>
                Notifications
              </Text>
              <View style={[styles.titleUnderline, {
                backgroundColor: '#FF6B35',
              }]} />
            </View>
            
            <TouchableOpacity 
              onPress={handleClearAll}
              style={[styles.clearButton, {
                backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
              }]}
              disabled={notifications.length === 0}
            >
              <Text style={[styles.clearText, {
                color: notifications.length > 0 ? '#FF6B35' : 'rgba(142, 142, 147, 0.6)',
              }]}>
                Clear All
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {notifications.length > 0 ? (
          <FlatList
            data={notifications}
            renderItem={renderNotificationItem}
            keyExtractor={(item) => item.id}
            style={styles.notificationsList}
            contentContainerStyle={styles.notificationsListContent}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={['#FF6B35']}
                tintColor="#FF6B35"
              />
            }
          />
        ) : (
          <View style={styles.emptyContainer}>
            <IconSymbol name="bell.slash.fill" size={48} color={isDark ? '#8e8e93' : '#8e8e93'} />
            <Text style={[styles.emptyText, {
              color: isDark ? '#8e8e93' : '#8e8e93',
            }]}>
              No notifications
            </Text>
            <Text style={[styles.emptySubtext, {
              color: isDark ? 'rgba(142, 142, 147, 0.8)' : 'rgba(108, 108, 112, 0.8)',
            }]}>
              You&apos;re all caught up!
            </Text>
          </View>
        )}
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  backButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF6B35',
  },
  titleSection: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    marginBottom: 8,
    fontSize: 24,
    fontWeight: '700',
  },
  titleUnderline: {
    width: 40,
    height: 4,
    borderRadius: 2,
  },
  clearButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  clearText: {
    fontSize: 16,
    fontWeight: '600',
  },
  notificationsList: {
    flex: 1,
  },
  notificationsListContent: {
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 40,
  },
  separator: {
    height: 8,
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 16,
    marginVertical: 4,
    position: 'relative',
    backdropFilter: 'blur(20px)',
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 4,
    letterSpacing: -0.2,
  },
  notificationMessage: {
    fontSize: 15,
    fontWeight: '400',
    marginBottom: 4,
    lineHeight: 22,
  },
  notificationTime: {
    fontSize: 13,
    fontWeight: '400',
  },
  unreadIndicator: {
    position: 'absolute',
    top: 20,
    right: 20,
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 15,
    fontWeight: '400',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
});