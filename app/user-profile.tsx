import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { userService } from '@/services/userService';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

export default function UserProfileScreen() {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { userId, fromNotification } = useLocalSearchParams();
  const [profileUser, setProfileUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [addingContact, setAddingContact] = useState(false);
  const [incomingRequest, setIncomingRequest] = useState<any>(null);
  const [isContact, setIsContact] = useState(false);
  const [processingRequest, setProcessingRequest] = useState(false);

  useEffect(() => {
    if (userId) {
      loadUserProfile();
      checkIncomingContactRequest();
      checkIsContact();
    }
  }, [userId, fromNotification]);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      const userData = await userService.getUserById(userId as string);
      setProfileUser(userData);
    } catch (error) {
      console.error('Error loading user profile:', error);
      Alert.alert('Error', 'Failed to load user profile');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const checkIsContact = async () => {
    try {
      const result = await userService.isContact(userId as string);
      setIsContact(result);
    } catch (error) {
      console.error('Error checking if user is a contact:', error);
    }
  };

  const checkIncomingContactRequest = async () => {
    try {
      const requests = await userService.getContactRequests();
      const incoming = requests.find((request: any) => request.senderId === userId);
      if (incoming) {
        setIncomingRequest(incoming);
      }
    } catch (error: any) {
      console.error('Error checking contact requests:', error);
      
      // Handle Firebase index errors specifically
      if (error.message && error.message.includes('index')) {
        console.warn('Firebase index is being created. This is normal on first use.');
        // Don't show alert to user for index errors as they resolve automatically
      } else {
        // For other errors, you might want to show a user-friendly message
        console.error('Unexpected error checking contact requests');
      }
    }
  };

  const handleAddToContacts = async () => {
    try {
      setAddingContact(true);
      await userService.sendContactRequest(userId as string);
      Alert.alert('Success', 'Contact request sent!');
      // Update the UI to show that contact request was sent
      setProfileUser((prev: any) => ({
        ...prev,
        requestSent: true
      }));
    } catch (error) {
      console.error('Error adding contact:', error);
      Alert.alert('Error', 'Failed to add contact');
    } finally {
      setAddingContact(false);
    }
  };

  const handleAcceptRequest = async () => {
    if (!incomingRequest) return;
    
    try {
      setProcessingRequest(true);
      await userService.acceptContactRequest(incomingRequest.id);
      Alert.alert('Success', 'Contact request accepted!');
      setIncomingRequest(null);
      // Refresh the profile to update the UI
      loadUserProfile();
    } catch (error) {
      console.error('Error accepting contact request:', error);
      Alert.alert('Error', 'Failed to accept contact request');
    } finally {
      setProcessingRequest(false);
    }
  };

  const handleRemoveContact = async () => {
    Alert.alert(
      "Remove Contact",
      "Are you sure you want to remove this contact?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          style: "destructive",
          onPress: async () => {
            try {
              await userService.removeContact(userId as string);
              setIsContact(false);
              Alert.alert("Success", "Contact removed.");
            } catch (error) {
              console.error('Error removing contact:', error);
              Alert.alert("Error", "Failed to remove contact.");
            }
          },
        },
      ]
    );
  };

  const handleIgnoreRequest = async () => {
    if (!incomingRequest) return;
    
    try {
      setProcessingRequest(true);
      await userService.rejectContactRequest(incomingRequest.id);
      Alert.alert('Success', 'Contact request ignored');
      setIncomingRequest(null);
      // Refresh the profile to update the UI
      loadUserProfile();
    } catch (error) {
      console.error('Error ignoring contact request:', error);
      Alert.alert('Error', 'Failed to ignore contact request');
    } finally {
      setProcessingRequest(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#000' : '#f8f9fa' }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B35" />
          <Text style={[styles.loadingText, { color: isDark ? '#8e8e93' : '#6c6c70' }]}>
            Loading profile...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#000' : '#f8f9fa' }]}>
      {/* Header */}
      <View style={[styles.header, { 
        backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
        shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
      }]}>
        <TouchableOpacity onPress={() => router.back()} style={[styles.backButton, {
          backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
        }]}>
          <IconSymbol name="chevron.left" size={20} color={isDark ? '#fff' : '#000'} />
        </TouchableOpacity>
        
        <Text style={[styles.headerTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
          Profile
        </Text>
        
        <View style={styles.emptyButton} />
      </View>

      {/* Profile Content */}
      <View style={styles.content}>
        {/* Avatar */}
        <View style={styles.avatarContainer}>
          <Image 
            source={{ uri: profileUser?.photoURL || profileUser?.avatar || 'https://via.placeholder.com/150' }} 
            style={styles.avatar}
          />
          {profileUser?.isOnline && <View style={styles.onlineIndicator} />}
        </View>

        {/* User Info */}
        <View style={styles.infoContainer}>
          <Text style={[styles.name, { color: isDark ? '#fff' : '#000' }]}>
            {profileUser?.name || profileUser?.displayName}
          </Text>
          <Text style={[styles.email, { color: isDark ? '#8e8e93' : '#6c6c70' }]}>
            {profileUser?.email}
          </Text>
          
          {profileUser?.lastSeen && (
            <Text style={[styles.lastSeen, { color: isDark ? '#8e8e93' : '#6c6c70' }]}>
              {profileUser.isOnline 
                ? 'Active now' 
                : `Last seen ${new Date(profileUser.lastSeen).toLocaleDateString()}`}
            </Text>
          )}
        </View>

        {/* Action Buttons */}
        {profileUser?.id !== user?.uid && incomingRequest && (
          <View style={styles.requestActionsContainer}>
            <TouchableOpacity
              style={[styles.acceptButton, { 
                backgroundColor: '#4CAF50',
              }]}
              onPress={handleAcceptRequest}
              disabled={processingRequest}
            >
              {processingRequest ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.actionButtonText}>
                  Accept Contact Request
                </Text>
              )}
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.ignoreButton, { 
                backgroundColor: '#f44336',
              }]}
              onPress={handleIgnoreRequest}
              disabled={processingRequest}
            >
              {processingRequest ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.actionButtonText}>
                  Ignore
                </Text>
              )}
            </TouchableOpacity>
          </View>
        )}

        {profileUser?.id !== user?.uid && !incomingRequest && (
          isContact ? (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: '#f44336' }]}
              onPress={handleRemoveContact}
            >
              <Text style={styles.actionButtonText}>Remove from Contacts</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[styles.actionButton, { 
                backgroundColor: profileUser?.requestSent ? '#4CAF50' : '#FF6B35',
              }]}
              onPress={handleAddToContacts}
              disabled={addingContact || profileUser?.requestSent}
            >
              {addingContact ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.actionButtonText}>
                  {profileUser?.requestSent ? 'Contact Request Sent' : 'Add to Contacts'}
                </Text>
              )}
            </TouchableOpacity>
          )
        )}

        {profileUser?.id === user?.uid && (
          <View style={[styles.actionButton, { backgroundColor: '#8e8e93' }]}>
            <Text style={styles.actionButtonText}>This is you</Text>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 8,
    elevation: 4,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  emptyButton: {
    width: 40,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 32,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 24,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: '#FF6B35',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#4CAF50',
    borderWidth: 3,
    borderColor: '#fff',
  },
  infoContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  email: {
    fontSize: 16,
    marginBottom: 4,
  },
  lastSeen: {
    fontSize: 14,
  },
  actionButton: {
    width: '100%',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FF6B35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  acceptButton: {
    width: '100%',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
    marginBottom: 12,
  },
  ignoreButton: {
    width: '100%',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#f44336',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  requestActionsContainer: {
    width: '100%',
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});