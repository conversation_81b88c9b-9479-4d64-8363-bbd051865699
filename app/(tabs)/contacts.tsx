import NotificationPopup from '@/components/NotificationPopup';
import FloatingActionButton from '@/components/FloatingActionButton';
import SearchBar from '@/components/SearchBar';
import { AnimatedTabScreen } from '@/components/AnimatedTabScreen';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { getAndroidVersionAwarePadding } from '@/utils/platform';
import { useActiveTab } from '@/hooks/useActiveTab';
import { chatService } from '@/services/chatService';
import { notificationApiService } from '@/services/notificationApiService';
import { userService } from '@/services/userService';
import { User } from '@/types/messenger';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { Image } from 'expo-image';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    Platform,
    SafeAreaView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    StatusBar
} from 'react-native';

export default function ContactsScreen() {
  const { user } = useAuth();
  const [contacts, setContacts] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredContacts, setFilteredContacts] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNotifications, setShowNotifications] = useState(false);
  const [unreadNotificationsCount, setUnreadNotificationsCount] = useState(0);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const activeTab = useActiveTab();
  const isActive = activeTab === 'contacts';

  // Load data on mount and set up real-time listener
  useEffect(() => {
    if (!user) return;
    
    // Set up real-time listener for contacts
    const unsubscribe = userService.listenToUserContacts(user.uid, (userContacts) => {
      console.log('🔄 Real-time contacts update received:', userContacts.length, 'contacts');
      // Ensure contacts have proper data
      const validatedContacts = userContacts.map(contact => ({
        ...contact,
        id: contact.contactId,
        name: contact.contactName,
        email: contact.contactEmail,
        photoURL: contact.contactPhotoURL,
      }));
      setContacts(validatedContacts);
      setFilteredContacts(validatedContacts);
      setLoading(false);
    });

    loadUnreadNotificationsCount();
    
    // Refresh notifications count every 30 seconds
    const interval = setInterval(() => {
      loadUnreadNotificationsCount();
    }, 30000);
    
    // Cleanup listener and interval on unmount
    return () => {
      unsubscribe();
      clearInterval(interval);
    };
  }, [user]);

  // Filter contacts when search query changes
  useEffect(() => {
    const filtered = contacts.filter(contact =>
      contact.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.email?.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredContacts(filtered);
  }, [searchQuery, contacts]);

  
  // Load unread notifications count
  const loadUnreadNotificationsCount = async () => {
    try {
      const count = await notificationApiService.getUnreadNotificationsCount();
      setUnreadNotificationsCount(count);
    } catch (error) {
      console.error('Failed to load unread notifications count:', error);
    }
  };

  const handleUserPress = async (userId: string) => {
    Haptics.selectionAsync();
    const contact = contacts.find(u => u.id === userId);
    if (!contact) return;

    try {
      setLoading(true);
      // Check for existing chat with this user
      const existingChatId = await chatService.findDirectChat(userId);
      
      if (existingChatId) {
        // Navigate to existing chat
        router.push(`/chat/${existingChatId}`);
      } else {
        // Create new chat
        const newChatId = await chatService.createDirectChat(userId);
        router.push(`/chat/${newChatId}`);
      }
    } catch (error) {
      console.error('Error starting chat:', error);
      Alert.alert('Error', 'Failed to start chat');
    } finally {
      setLoading(false);
    }
  };

  const handleDiscoverContacts = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push('/discover-contacts');
  };

  const renderUserItem = ({ item }: { item: User }) => (
    <TouchableOpacity
      style={[styles.userItem, { backgroundColor: isDark ? '#1c1c1e' : '#fff' }]}
      onPress={() => {
        Haptics.selectionAsync();
        handleUserPress(item.id);
      }}
      disabled={loading}
    >
      <View style={styles.avatarContainer}>
        {item.photoURL ? (
          <Image 
            source={{ uri: item.photoURL }} 
            style={styles.avatar} 
          />
        ) : (
          <View style={[styles.avatar, styles.avatarPlaceholder]}>
            {item.name && item.name !== 'Unknown User' && (
              <Text style={styles.avatarText}>
                {item.name.charAt(0).toUpperCase()}
              </Text>
            )}
          </View>
        )}
        {item.isOnline && <View style={styles.onlineIndicator} />}
      </View>
      
      <View style={styles.userContent}>
        <Text style={[styles.userName, { color: isDark ? '#fff' : '#000' }]} numberOfLines={1}>
          {item.name || item.displayName || 'Unknown User'}
        </Text>
        <Text style={[styles.userStatus, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
          {item.isOnline ? 'Active now' : `Last seen ${item.lastSeen ? new Date(item.lastSeen).toLocaleDateString() : 'recently'}`}
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.messageButton, { backgroundColor: '#FF6B35' }]}
        onPress={() => handleUserPress(item.id)}
        disabled={loading}
      >
        <IconSymbol name="message.fill" size={16} color="#ffffff" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <AnimatedTabScreen isActive={isActive}>
      <View style={[styles.container, { backgroundColor: isDark ? '#000' : '#f8f9fa' }]}>
        <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
        <SafeAreaView style={styles.safeArea}>
          <View style={[styles.header, { 
            backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          }]}>
          <View style={styles.headerContent}>
            <View style={styles.titleSection}>
              <Text style={[styles.headerTitle, { 
                color: isDark ? '#ffffff' : '#000000',
                letterSpacing: -1.2,
              }]}>
                Contacts
              </Text>
              <View style={[styles.titleUnderline, {
                backgroundColor: '#FF6B35',
              }]} />
            </View>
            <TouchableOpacity 
              style={[styles.notificationButton, {
                backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
              }]}
              onPress={() => setShowNotifications(true)}
            >
              <IconSymbol name="bell.fill" size={20} color="#FF6B35" />
              {unreadNotificationsCount > 0 && (
                <View style={[styles.notificationBadge, {
                  backgroundColor: '#FF6B35',
                }]}>
                  <Text style={styles.notificationBadgeText}>
                    {unreadNotificationsCount > 9 ? '9+' : unreadNotificationsCount}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>

  
        <View style={styles.searchSection}>
          <View style={[styles.searchWrapper, {
            backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
            shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 1,
            shadowRadius: 16,
            elevation: 4,
          }]}>
            <SearchBar
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search contacts to start chat"
              onClear={() => setSearchQuery('')}
            />
          </View>
        </View>

        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { 
            color: isDark ? '#8e8e93' : '#6c6c70',
            fontWeight: '600',
            letterSpacing: 0.5,
          }]}>
            ALL CONTACTS ({loading ? '...' : filteredContacts.length})
          </Text>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B35" />
            <Text style={[styles.loadingText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              Loading contacts...
            </Text>
          </View>
        ) : filteredContacts.length === 0 ? (
          <View style={styles.emptyContainer}>
            <IconSymbol 
              name={searchQuery ? "person.crop.circle.badge.questionmark" : "person.2.fill"} 
              size={48} 
              color={isDark ? '#8e8e93' : '#8e8e93'} 
            />
            <Text style={[styles.emptyText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              {searchQuery ? 'No contacts found' : 'No contacts yet'}
            </Text>
            <Text style={[styles.emptySubtext, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              {searchQuery ? 'Try a different search term' : 'Add contacts to start chatting'}
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredContacts}
            renderItem={renderUserItem}
            keyExtractor={(item) => item.id}
            style={styles.userList}
            contentContainerStyle={styles.userListContent}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        )}
      </SafeAreaView>
      {/* Floating Action Button */}
      <FloatingActionButton
        onPress={handleDiscoverContacts}
        icon="magnifyingglass"
        style={styles.discoverContactsFab}
      />
      <NotificationPopup
        visible={showNotifications}
        onClose={() => setShowNotifications(false)}
      />
      </View>
    </AnimatedTabScreen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: Platform.select({ 
      ios: 16, 
      android: getAndroidVersionAwarePadding(32, 40) 
    }),
    paddingBottom: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleSection: {
    flex: 1,
  },
  headerTitle: {
    marginBottom: 8,
    fontSize: 24,
    fontWeight: '700',
  },
  titleUnderline: {
    width: 40,
    height: 4,
    borderRadius: 2,
  },
  searchSection: {
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 16,
  },
  searchWrapper: {
    borderRadius: 20,
    overflow: 'hidden',
    backdropFilter: 'blur(20px)',
  },
  sectionHeader: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  sectionTitle: {
    fontSize: 13,
  },
  userList: {
    flex: 1,
  },
  userListContent: {
    paddingHorizontal: 20,
  },
  separator: {
    height: 8,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    marginVertical: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  avatarPlaceholder: {
    backgroundColor: '#FF6B35',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#30d158',
    borderWidth: 2,
    borderColor: '#fff',
  },
  userContent: {
    flex: 1,
  },
  userName: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 4,
    letterSpacing: -0.2,
  },
  userStatus: {
    fontSize: 14,
    fontWeight: '400',
  },
  messageButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  notificationBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    fontWeight: '400',
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.7,
  },
  discoverContactsFab: {
    position: 'absolute',
    bottom: Platform.select({ 
      ios: 90, 
      android: getAndroidVersionAwarePadding(120, 130) 
    }),
    right: 20,
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF6B35',
    shadowColor: '#FF6B35',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  });