import ChatListItem from '@/components/ChatListItem';
import FloatingActionButton from '@/components/FloatingActionButton';
import NotificationPopup from '@/components/NotificationPopup';
import SearchBar from '@/components/SearchBar';
import { AnimatedTabScreen } from '@/components/AnimatedTabScreen';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { getAndroidVersionAwarePadding } from '@/utils/platform';
import { useActiveTab } from '@/hooks/useActiveTab';
import { ChatListItem as ChatItem, chatService } from '@/services/chatService';
import { notificationApiService } from '@/services/notificationApiService';
import { ChatPreview } from '@/types/messenger';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    Platform,
    RefreshControl,
    SafeAreaView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    StatusBar,
} from 'react-native';

export default function ChatsScreen() {
  const [chats, setChats] = useState<ChatPreview[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [unreadNotificationsCount, setUnreadNotificationsCount] = useState(0);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { user } = useAuth();
  const activeTab = useActiveTab();
  const isActive = activeTab === 'index';

  // Convert ChatItem to ChatPreview format for backward compatibility
  const convertToPreview = (item: ChatItem): ChatPreview => {
    const preview = {
      id: item.id,
      name: item.name,
      lastMessage: item.lastMessage,
      lastMessageTime: item.lastMessageTime,
      timestamp: item.lastMessageTime,
      unreadCount: item.unreadCount,
      isGroup: item.isGroup,
      participants: item.participants,
      photoURL: item.photoURL,
      avatar: item.photoURL,
      isOnline: undefined,
    };
    
    return preview;
  };

  // Load conversations from Firebase
  const loadConversations = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const userChats = await chatService.getUserChats(user.uid);
      const chatPreviews = userChats.map(convertToPreview);
      setChats(chatPreviews);
    } catch (error) {
      console.error('Failed to load conversations:', error);
      Alert.alert('Error', 'Failed to load conversations');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Load unread notifications count
  const loadUnreadNotificationsCount = async () => {
    try {
      const count = await notificationApiService.getUnreadNotificationsCount();
      setUnreadNotificationsCount(count);
    } catch (error) {
      console.error('Failed to load unread notifications count:', error);
    }
  };

  // Refresh conversations
  const onRefresh = () => {
    setRefreshing(true);
    loadConversations();
  };

  // Initialize on mount and set up real-time listener
  useEffect(() => {
    if (!user) return;
    
    // Set up real-time listener for chats
    const unsubscribe = chatService.listenToUserChats(user.uid, (userChats: ChatItem[]) => {
      console.log('🔄 Real-time chat update received:', userChats.length, 'chats');
      const chatPreviews = userChats.map(convertToPreview);
      setChats(chatPreviews);
      setLoading(false);
    });

    loadUnreadNotificationsCount();
    
    // Refresh notifications count every 30 seconds
    const interval = setInterval(() => {
      loadUnreadNotificationsCount();
    }, 30000);
    
    // Cleanup listener and interval on unmount
    return () => {
      unsubscribe();
      clearInterval(interval);
    };
  }, [user]);

  const filteredChats = chats.filter(chat =>
    chat.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleChatPress = (chatId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push(`/chat/${chatId}`);
  };

  const handleNewChat = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // Navigate to the new chat page instead of contacts page
    router.push('/new-chat');
  };

  const handleDeleteChat = async (chatId: string) => {
    try {
      // Optimistically remove the chat from the UI
      setChats(prevChats => prevChats.filter(chat => chat.id !== chatId));
      // Perform the delete operation in the background
      await chatService.deleteChat(chatId);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Failed to delete chat:', error);
      Alert.alert('Error', 'Failed to delete the chat. Please try again.');
      // If deletion fails, refresh the list to bring back the chat
      loadConversations();
    }
  };

  const handleMuteChat = (chatId: string) => {
    Alert.alert('Chat Muted', 'This feature will be implemented in a future update');
  };

  const renderChatItem = ({ item }: { item: ChatPreview }) => (
    <ChatListItem
      chat={item}
      onPress={handleChatPress}
      onDelete={handleDeleteChat}
      onMute={handleMuteChat}
    />
  );

  return (
    <AnimatedTabScreen isActive={isActive}>
      <View style={[styles.container, { backgroundColor: isDark ? '#000' : '#f8f9fa' }]}>
        <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
        <SafeAreaView style={styles.safeArea}>
          {/* Header */}
          <View style={[styles.header, { 
            backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          }]}>
          <View style={styles.headerContent}>
            <View style={styles.titleSection}>
              <Text style={[styles.headerTitle, { 
                color: isDark ? '#ffffff' : '#000000',
                letterSpacing: -1.2,
              }]}>
                Chats
              </Text>
              <View style={[styles.titleUnderline, {
                backgroundColor: '#FF6B35',
              }]} />
            </View>
            <TouchableOpacity 
              style={[styles.notificationButton, {
                backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
              }]}
              onPress={() => setShowNotifications(true)}
            >
              <IconSymbol name="bell.fill" size={20} color="#FF6B35" />
              {unreadNotificationsCount > 0 && (
                <View style={[styles.notificationBadge, {
                  backgroundColor: '#FF6B35',
                }]}>
                  <Text style={styles.notificationBadgeText}>
                    {unreadNotificationsCount > 9 ? '9+' : unreadNotificationsCount}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>

        {/* Premium Search */}
        <View style={styles.searchSection}>
          <View style={[styles.searchWrapper, {
            backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
            shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 1,
            shadowRadius: 16,
            elevation: 4,
          }]}>
            <SearchBar
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search conversations"
              onClear={() => setSearchQuery('')}
            />
          </View>
        </View>

        {/* Chat List with Modern Styling */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B35" />
            <Text style={[styles.loadingText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              Loading conversations...
            </Text>
          </View>
        ) : filteredChats.length === 0 ? (
          <View style={styles.emptyContainer}>
            <IconSymbol 
              name={searchQuery ? "magnifyingglass" : "message.fill"} 
              size={48} 
              color={isDark ? '#8e8e93' : '#8e8e93'} 
            />
            <Text style={[styles.emptyText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              {searchQuery ? 'No chats found' : 'No chats yet'}
            </Text>
            <Text style={[styles.emptySubtext, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              {searchQuery ? 'Try a different search term' : 'Start a new conversation to see it here'}
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredChats}
            renderItem={renderChatItem}
            keyExtractor={(item) => item.id}
            style={styles.chatList}
            contentContainerStyle={styles.chatListContent}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={['#FF6B35']}
                tintColor="#FF6B35"
              />
            }
          />
        )}
      </SafeAreaView>
      {/* Floating Action Button */}
      <FloatingActionButton
        onPress={handleNewChat}
        icon="person.badge.plus"
        style={styles.newChatFab}
      />
      <NotificationPopup
        visible={showNotifications}
        onClose={() => setShowNotifications(false)}
      />
      </View>
    </AnimatedTabScreen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: Platform.select({ 
      ios: 16, 
      android: getAndroidVersionAwarePadding(32, 40) 
    }),
    paddingBottom: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleSection: {
    flex: 1,
  },
  headerTitle: {
    marginBottom: 8,
    fontSize: 24,
    fontWeight: '700',
  },
  titleUnderline: {
    width: 40,
    height: 4,
    borderRadius: 2,
  },
  composeButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 4,
  },
  searchSection: {
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 16,
  },
  searchWrapper: {
    borderRadius: 20,
    overflow: 'hidden',
    backdropFilter: 'blur(20px)',
  },
  chatList: {
    flex: 1,
  },
  chatListContent: {
    paddingHorizontal: 20,
  },
  separator: {
    height: 8,
  },
  newChatFab: {
    position: 'absolute',
    bottom: Platform.select({ 
      ios: 90, 
      android: getAndroidVersionAwarePadding(120, 130) 
    }),
    right: 20,
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF6B35',
    shadowColor: '#FF6B35',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  newGroupFab: {
    position: 'absolute',
    bottom: Platform.select({ 
      ios: 60, 
      android: getAndroidVersionAwarePadding(90, 100) 
    }),
    right: 20,
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF6B35',
    shadowColor: '#FF6B35',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  notificationBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    fontWeight: '400',
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.7,
  },
});