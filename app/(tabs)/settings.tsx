import { AnimatedTabScreen } from "@/components/AnimatedTabScreen";
import NotificationPopup from "@/components/NotificationPopup";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { useAuth } from "@/contexts/AuthContext";
import { useTheme } from "@/contexts/ThemeContext";
import { useActiveTab } from "@/hooks/useActiveTab";
import { getAndroidVersionAwarePadding } from "@/utils/platform";
import { notificationApiService } from "@/services/notificationApiService";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  Image,
  Linking,
  Modal,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  StatusBar,
  View,
} from "react-native";

export default function SettingsScreen() {
  const { theme, setTheme, isDark } = useTheme();
  const { userProfile, signOut } = useAuth();
  const [showNotifications, setShowNotifications] = useState(false);
  const [showThemePicker, setShowThemePicker] = useState(false);
  const [showAboutModal, setShowAboutModal] = useState(false);
  const [unreadNotificationsCount, setUnreadNotificationsCount] = useState(0);
  const activeTab = useActiveTab();
  const isActive = activeTab === "settings";

  // Handle theme change
  const handleThemeChange = (newTheme: "light" | "dark" | "system") => {
    setTheme(newTheme);
  };

  
  // Load unread notifications count
  const loadUnreadNotificationsCount = async () => {
    try {
      const count = await notificationApiService.getUnreadNotificationsCount();
      setUnreadNotificationsCount(count);
    } catch {
      console.error("Failed to load unread notifications count");
    }
  };

  // Initialize on mount
  useEffect(() => {
    loadUnreadNotificationsCount();

    // Refresh notifications count every 30 seconds
    const interval = setInterval(() => {
      loadUnreadNotificationsCount();
    }, 30000);

    // Cleanup interval on unmount
    return () => {
      clearInterval(interval);
    };
  }, []);

  const handleAbout = () => {
    setShowAboutModal(true);
  };

  const handleVisitWebsite = () => {
    Linking.openURL("https://namoshkar.app");
  };

  const handleContactSupport = () => {
    Linking.openURL("mailto:<EMAIL>");
  };

  const handlePrivacy = () => {
    Alert.alert("Privacy", "Privacy settings coming soon!");
  };

  const handleSupport = () => {
    Alert.alert("Support", "Contact <NAME_EMAIL>");
  };

  // Updated sign out handler
  const handleSignOut = () => {
    Alert.alert("Sign Out", "Are you sure you want to sign out?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Sign Out",
        style: "destructive",
        onPress: async () => {
          try {
            const response = await signOut(); // Using signOut from AuthContext
            if (response.success) {
              router.replace("/sign-in");
            } else {
              Alert.alert("Error", response.error || "Failed to sign out");
            }
          } catch {
            Alert.alert(
              "Error",
              "An unexpected error occurred while signing out"
            );
          }
        },
      },
    ]);
  };

  const SettingsItem = ({
    icon,
    title,
    subtitle,
    onPress,
    showArrow = true,
    rightComponent,
  }: {
    icon: string;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    showArrow?: boolean;
    rightComponent?: React.ReactNode;
  }) => (
    <TouchableOpacity
      style={[
        styles.settingsItem,
        {
          backgroundColor: isDark
            ? "rgba(26, 26, 26, 0.8)"
            : "rgba(255, 255, 255, 0.9)",
          shadowColor: isDark
            ? "rgba(255, 255, 255, 0.05)"
            : "rgba(0, 0, 0, 0.04)",
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 1,
          shadowRadius: 20,
          elevation: 1,
        },
      ]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View
        style={[
          styles.iconContainer,
          {
            backgroundColor: isDark
              ? "rgba(42, 42, 42, 0.8)"
              : "rgba(240, 240, 240, 0.8)",
          },
        ]}
      >
        <IconSymbol name={icon as any} size={20} color="#FF6B35" />
      </View>
      <View style={styles.textContainer}>
        <Text
          style={[
            styles.itemTitle,
            {
              color: isDark ? "#ffffff" : "#000000",
            },
          ]}
        >
          {title}
        </Text>
        {subtitle && (
          <Text
            style={[
              styles.itemSubtitle,
              {
                color: isDark
                  ? "rgba(142, 142, 147, 0.8)"
                  : "rgba(108, 108, 112, 0.8)",
              },
            ]}
          >
            {subtitle}
          </Text>
        )}
      </View>
      {rightComponent ||
        (showArrow && (
          <IconSymbol name="chevron.right" size={16} color="#8e8e93" />
        ))}
    </TouchableOpacity>
  );

  return (
    <>
      <AnimatedTabScreen isActive={isActive}>
        <View
          style={[
            styles.container,
            { backgroundColor: isDark ? "#000000" : "#f8f9fa" },
          ]}
        >
          <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
          <SafeAreaView style={styles.safeArea}>
            {/* Header */}
            <View
              style={[
                styles.header,
                {
                  backgroundColor: isDark
                    ? "rgba(26, 26, 26, 0.95)"
                    : "rgba(255, 255, 255, 0.95)",
                },
              ]}
            >
              <View style={styles.headerContent}>
                <View style={styles.titleSection}>
                  <Text
                    style={[
                      styles.headerTitle,
                      {
                        color: isDark ? "#ffffff" : "#000000",
                        letterSpacing: -1.2,
                      },
                    ]}
                  >
                    Settings
                  </Text>
                  <View
                    style={[
                      styles.titleUnderline,
                      {
                        backgroundColor: "#FF6B35",
                      },
                    ]}
                  />
                </View>
                <TouchableOpacity
                  style={[
                    styles.notificationButton,
                    {
                      backgroundColor: isDark
                        ? "rgba(42, 42, 42, 0.8)"
                        : "rgba(240, 240, 240, 0.8)",
                    },
                  ]}
                  onPress={() => setShowNotifications(true)}
                >
                  <IconSymbol name="bell.fill" size={20} color="#FF6B35" />
                  {unreadNotificationsCount > 0 && (
                    <View
                      style={[
                        styles.notificationBadge,
                        {
                          backgroundColor: "#FF6B35",
                        },
                      ]}
                    >
                      <Text style={styles.notificationBadgeText}>
                        {unreadNotificationsCount > 9
                          ? "9+"
                          : unreadNotificationsCount}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            </View>

            <ScrollView
              style={styles.scrollView}
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={false}
            >
  
              {/* Profile Section */}
              <View style={[styles.section, styles.firstSection]}>
                <View
                  style={[
                    styles.profileCard,
                    {
                      backgroundColor: isDark
                        ? "rgba(26, 26, 26, 0.8)"
                        : "rgba(255, 255, 255, 0.9)",
                      shadowColor: isDark
                        ? "rgba(255, 255, 255, 0.1)"
                        : "rgba(0, 0, 0, 0.1)",
                      shadowOffset: { width: 0, height: 4 },
                      shadowOpacity: 1,
                      shadowRadius: 20,
                      elevation: 4,
                    },
                  ]}
                >
                  <View
                    style={[
                      styles.profileAvatar,
                      {
                        shadowColor: isDark
                          ? "rgba(255, 255, 255, 0.2)"
                          : "rgba(0, 0, 0, 0.2)",
                        shadowOffset: { width: 0, height: 4 },
                        shadowOpacity: 1,
                        shadowRadius: 12,
                        elevation: 6,
                      },
                    ]}
                  >
                    <Image
                      source={{
                        uri:
                          userProfile?.photoURL ||
                          "https://ui-avatars.com/api/?name=" +
                            encodeURIComponent(
                              userProfile?.displayName || "User"
                            ),
                      }}
                      style={styles.avatarImage}
                    />
                  </View>
                  <View style={styles.profileInfo}>
                    <Text
                      style={[
                        styles.profileName,
                        {
                          color: isDark ? "#ffffff" : "#000000",
                        },
                      ]}
                    >
                      {userProfile?.displayName}
                    </Text>
                    <Text
                      style={[
                        styles.profileEmail,
                        {
                          color: isDark
                            ? "rgba(142, 142, 147, 0.8)"
                            : "rgba(108, 108, 112, 0.8)",
                        },
                      ]}
                    >
                      {userProfile?.email}
                    </Text>
                  </View>
                </View>
              </View>

              {/* Account Section */}
              <View style={styles.section}>
                <Text
                  style={[
                    styles.sectionTitle,
                    {
                      color: isDark
                        ? "rgba(142, 142, 147, 0.8)"
                        : "rgba(108, 108, 112, 0.8)",
                    },
                  ]}
                >
                  ACCOUNT
                </Text>
                <SettingsItem
                  icon="person.fill"
                  title="Edit Profile"
                  subtitle="Update your profile information"
                  onPress={() => router.push("/edit-profile")}
                />
                <SettingsItem
                  icon="key.fill"
                  title="Change Password"
                  subtitle="Update your password"
                  onPress={() =>
                    Alert.alert(
                      "Change Password",
                      "This feature will be implemented in a future update"
                    )
                  }
                />
              </View>

              {/* Preferences Section */}
              <View style={styles.section}>
                <Text
                  style={[
                    styles.sectionTitle,
                    {
                      color: isDark
                        ? "rgba(142, 142, 147, 0.8)"
                        : "rgba(108, 108, 112, 0.8)",
                    },
                  ]}
                >
                  PREFERENCES
                </Text>
                <SettingsItem
                  icon="paintpalette.fill"
                  title="Themes"
                  subtitle={
                    theme === "system"
                      ? "System"
                      : theme === "dark"
                        ? "Dark"
                        : "Light"
                  }
                  onPress={() => setShowThemePicker(true)}
                />
                <SettingsItem
                  icon="bell.fill"
                  title="Notifications"
                  subtitle="Manage notification settings"
                  onPress={() => router.push("/notifications")}
                />
              </View>

              {/* About Section */}
              <View style={styles.section}>
                <Text
                  style={[
                    styles.sectionTitle,
                    {
                      color: isDark
                        ? "rgba(142, 142, 147, 0.8)"
                        : "rgba(108, 108, 112, 0.8)",
                    },
                  ]}
                >
                  ABOUT
                </Text>
                <SettingsItem
                  icon="info.circle.fill"
                  title="About"
                  onPress={handleAbout}
                />
                <SettingsItem
                  icon="lock.fill"
                  title="Privacy Policy"
                  onPress={handlePrivacy}
                />
                <SettingsItem
                  icon="questionmark.circle.fill"
                  title="Support"
                  onPress={handleSupport}
                />
              </View>

              {/* Sign Out */}
              <View style={[styles.section, styles.lastSection]}>
                <SettingsItem
                  icon="rectangle.portrait.and.arrow.right"
                  title="Sign Out"
                  onPress={handleSignOut}
                />
              </View>
            </ScrollView>
          </SafeAreaView>

          <NotificationPopup
            visible={showNotifications}
            onClose={() => setShowNotifications(false)}
          />
        </View>
      </AnimatedTabScreen>

      {/* Modals positioned outside AnimatedTabScreen for proper stacking */}
      <Modal
        visible={showThemePicker}
        transparent
        animationType="slide"
        onRequestClose={() => setShowThemePicker(false)}
      >
        <View
          style={[
            styles.modalOverlay,
            { backgroundColor: "rgba(0, 0, 0, 0.5)" },
          ]}
        >
          <TouchableOpacity
            style={styles.modalTouchableOverlay}
            activeOpacity={1}
            onPress={() => setShowThemePicker(false)}
          />
          <View style={[styles.modalContentContainer]}>
            <View
              style={[
                styles.modalContent,
                {
                  backgroundColor: isDark ? "#1c1c1e" : "#ffffff",
                  shadowColor: isDark
                    ? "rgba(255, 255, 255, 0.1)"
                    : "rgba(0, 0, 0, 0.1)",
                },
              ]}
            >
              <View
                style={[
                  styles.modalHeader,
                  {
                    borderBottomColor: isDark
                      ? "rgba(255, 255, 255, 0.1)"
                      : "rgba(0, 0, 0, 0.1)",
                  },
                ]}
              >
                <Text
                  style={[
                    styles.modalTitle,
                    {
                      color: isDark ? "#ffffff" : "#000000",
                    },
                  ]}
                >
                  Select Theme
                </Text>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setShowThemePicker(false)}
                >
                  <Text
                    style={[
                      styles.closeButtonText,
                      {
                        color: isDark ? "#ffffff" : "#000000",
                      },
                    ]}
                  >
                    ✕
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.themeOptions}>
                <TouchableOpacity
                  style={[
                    styles.themeOption,
                    {
                      backgroundColor:
                        theme === "light" ? "#FF6B3520" : "transparent",
                      borderColor: isDark
                        ? "rgba(255, 255, 255, 0.1)"
                        : "rgba(0, 0, 0, 0.1)",
                    },
                  ]}
                  onPress={() => {
                    handleThemeChange("light");
                    setShowThemePicker(false);
                  }}
                >
                  <View
                    style={[styles.themePreview, styles.lightThemePreview]}
                  />
                  <View style={styles.themeInfo}>
                    <Text
                      style={[
                        styles.themeName,
                        {
                          color: isDark ? "#ffffff" : "#000000",
                        },
                      ]}
                    >
                      Light
                    </Text>
                    <Text
                      style={[
                        styles.themeDescription,
                        {
                          color: isDark
                            ? "rgba(255, 255, 255, 0.6)"
                            : "rgba(0, 0, 0, 0.6)",
                        },
                      ]}
                    >
                      Always use light theme
                    </Text>
                  </View>
                  {theme === "light" && (
                    <View
                      style={[
                        styles.checkmark,
                        {
                          backgroundColor: "#FF6B35",
                        },
                      ]}
                    >
                      <Text style={styles.checkmarkText}>✓</Text>
                    </View>
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.themeOption,
                    {
                      backgroundColor:
                        theme === "dark" ? "#FF6B3520" : "transparent",
                      borderColor: isDark
                        ? "rgba(255, 255, 255, 0.1)"
                        : "rgba(0, 0, 0, 0.1)",
                    },
                  ]}
                  onPress={() => {
                    handleThemeChange("dark");
                    setShowThemePicker(false);
                  }}
                >
                  <View
                    style={[styles.themePreview, styles.darkThemePreview]}
                  />
                  <View style={styles.themeInfo}>
                    <Text
                      style={[
                        styles.themeName,
                        {
                          color: isDark ? "#ffffff" : "#000000",
                        },
                      ]}
                    >
                      Dark
                    </Text>
                    <Text
                      style={[
                        styles.themeDescription,
                        {
                          color: isDark
                            ? "rgba(255, 255, 255, 0.6)"
                            : "rgba(0, 0, 0, 0.6)",
                        },
                      ]}
                    >
                      Always use dark theme
                    </Text>
                  </View>
                  {theme === "dark" && (
                    <View
                      style={[
                        styles.checkmark,
                        {
                          backgroundColor: "#FF6B35",
                        },
                      ]}
                    >
                      <Text style={styles.checkmarkText}>✓</Text>
                    </View>
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.themeOption,
                    {
                      backgroundColor:
                        theme === "system" ? "#FF6B3520" : "transparent",
                      borderColor: isDark
                        ? "rgba(255, 255, 255, 0.1)"
                        : "rgba(0, 0, 0, 0.1)",
                    },
                  ]}
                  onPress={() => {
                    handleThemeChange("system");
                    setShowThemePicker(false);
                  }}
                >
                  <View
                    style={[styles.themePreview, styles.systemThemePreview]}
                  >
                    <View style={styles.systemThemePreviewHalf} />
                  </View>
                  <View style={styles.themeInfo}>
                    <Text
                      style={[
                        styles.themeName,
                        {
                          color: isDark ? "#ffffff" : "#000000",
                        },
                      ]}
                    >
                      System
                    </Text>
                    <Text
                      style={[
                        styles.themeDescription,
                        {
                          color: isDark
                            ? "rgba(255, 255, 255, 0.6)"
                            : "rgba(0, 0, 0, 0.6)",
                        },
                      ]}
                    >
                      Follow device settings
                    </Text>
                  </View>
                  {theme === "system" && (
                    <View
                      style={[
                        styles.checkmark,
                        {
                          backgroundColor: "#FF6B35",
                        },
                      ]}
                    >
                      <Text style={styles.checkmarkText}>✓</Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      <Modal
        visible={showAboutModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowAboutModal(false)}
      >
        <View
          style={[
            styles.modalOverlay,
            { backgroundColor: "rgba(0, 0, 0, 0.5)" },
          ]}
        >
          <TouchableOpacity
            style={styles.modalTouchableOverlay}
            activeOpacity={1}
            onPress={() => setShowAboutModal(false)}
          />
          <View style={[styles.modalContentContainer]}>
            <View
              style={[
                styles.modalContent,
                {
                  backgroundColor: isDark ? "#1c1c1e" : "#ffffff",
                  shadowColor: isDark
                    ? "rgba(255, 255, 255, 0.1)"
                    : "rgba(0, 0, 0, 0.1)",
                },
              ]}
            >
              <View
                style={[
                  styles.modalHeader,
                  {
                    borderBottomColor: isDark
                      ? "rgba(255, 255, 255, 0.1)"
                      : "rgba(0, 0, 0, 0.1)",
                  },
                ]}
              >
                <Text
                  style={[
                    styles.modalTitle,
                    {
                      color: isDark ? "#ffffff" : "#000000",
                    },
                  ]}
                >
                  About
                </Text>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setShowAboutModal(false)}
                >
                  <Text
                    style={[
                      styles.closeButtonText,
                      {
                        color: isDark ? "#ffffff" : "#000000",
                      },
                    ]}
                  >
                    ✕
                  </Text>
                </TouchableOpacity>
              </View>

              <ScrollView
                style={styles.aboutScrollView}
                showsVerticalScrollIndicator={false}
              >
                <View style={styles.aboutContent}>
                  {/* Logo */}
                  <View style={styles.logoContainer}>
                    <Image
                      source={
                        isDark
                          ? require("../../assets/images/messenger-logo-dark.png")
                          : require("../../assets/images/messenger-logo-light-mode.png")
                      }
                      style={styles.logoImage}
                      resizeMode="contain"
                    />
                    <Text
                      style={[
                        styles.aboutAppName,
                        {
                          color: isDark ? "#ffffff" : "#000000",
                        },
                      ]}
                    >
                      Namoshkar Messenger
                    </Text>
                    <Text
                      style={[
                        styles.aboutAppVersion,
                        {
                          color: isDark
                            ? "rgba(255, 255, 255, 0.6)"
                            : "rgba(0, 0, 0, 0.6)",
                        },
                      ]}
                    >
                      Version 1.0.0
                    </Text>
                  </View>

                  {/* Description */}
                  <View style={styles.descriptionSection}>
                    <Text
                      style={[
                        styles.descriptionTitle,
                        {
                          color: isDark ? "#ffffff" : "#000000",
                        },
                      ]}
                    >
                      A Sacred Connection
                    </Text>
                    <Text
                      style={[
                        styles.descriptionText,
                        {
                          color: isDark
                            ? "rgba(255, 255, 255, 0.8)"
                            : "rgba(0, 0, 0, 0.8)",
                        },
                      ]}
                    >
                      Namoshkar Messenger is a blessed platform for the Hindu
                      community to connect, share spiritual moments, and
                      strengthen our dharmic bonds. Built with devotion and
                      respect for our sacred traditions.
                    </Text>
                  </View>

                  {/* Features */}
                  <View style={styles.featuresSection}>
                    <Text
                      style={[
                        styles.featuresTitle,
                        {
                          color: isDark ? "#ffffff" : "#000000",
                        },
                      ]}
                    >
                      Sacred Features
                    </Text>
                    <View style={styles.featureItem}>
                      <IconSymbol
                        name="message.fill"
                        size={20}
                        color="#FF6B35"
                      />
                      <Text
                        style={[
                          styles.featureText,
                          {
                            color: isDark
                              ? "rgba(255, 255, 255, 0.8)"
                              : "rgba(0, 0, 0, 0.8)",
                          },
                        ]}
                      >
                        Divine Messaging
                      </Text>
                    </View>
                    <View style={styles.featureItem}>
                      <IconSymbol
                        name="person.2.fill"
                        size={20}
                        color="#FF6B35"
                      />
                      <Text
                        style={[
                          styles.featureText,
                          {
                            color: isDark
                              ? "rgba(255, 255, 255, 0.8)"
                              : "rgba(0, 0, 0, 0.8)",
                          },
                        ]}
                      >
                        Community Connections
                      </Text>
                    </View>
                    <View style={styles.featureItem}>
                      <IconSymbol name="photo.fill" size={20} color="#FF6B35" />
                      <Text
                        style={[
                          styles.featureText,
                          {
                            color: isDark
                              ? "rgba(255, 255, 255, 0.8)"
                              : "rgba(0, 0, 0, 0.8)",
                          },
                        ]}
                      >
                        Spiritual Moments
                      </Text>
                    </View>
                    <View style={styles.featureItem}>
                      <IconSymbol name="lock.fill" size={20} color="#FF6B35" />
                      <Text
                        style={[
                          styles.featureText,
                          {
                            color: isDark
                              ? "rgba(255, 255, 255, 0.8)"
                              : "rgba(0, 0, 0, 0.8)",
                          },
                        ]}
                      >
                        Sacred Privacy
                      </Text>
                    </View>
                  </View>

                  {/* Contact */}
                  <View style={styles.contactSection}>
                    <Text
                      style={[
                        styles.contactTitle,
                        {
                          color: isDark ? "#ffffff" : "#000000",
                        },
                      ]}
                    >
                      Connect With Us
                    </Text>
                    <TouchableOpacity
                      style={[
                        styles.contactButton,
                        {
                          backgroundColor: isDark
                            ? "rgba(42, 42, 42, 0.8)"
                            : "rgba(240, 240, 240, 0.8)",
                        },
                      ]}
                      onPress={handleVisitWebsite}
                    >
                      <IconSymbol name="globe" size={20} color="#FF6B35" />
                      <Text
                        style={[
                          styles.contactButtonText,
                          {
                            color: isDark ? "#ffffff" : "#000000",
                          },
                        ]}
                      >
                        Visit Website
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        styles.contactButton,
                        {
                          backgroundColor: isDark
                            ? "rgba(42, 42, 42, 0.8)"
                            : "rgba(240, 240, 240, 0.8)",
                        },
                      ]}
                      onPress={handleContactSupport}
                    >
                      <IconSymbol
                        name="envelope.fill"
                        size={20}
                        color="#FF6B35"
                      />
                      <Text
                        style={[
                          styles.contactButtonText,
                          {
                            color: isDark ? "#ffffff" : "#000000",
                          },
                        ]}
                      >
                        Contact Support
                      </Text>
                    </TouchableOpacity>
                  </View>

                  {/* Footer */}
                  <View style={styles.footerSection}>
                    <Text
                      style={[
                        styles.footerText,
                        {
                          color: isDark
                            ? "rgba(255, 255, 255, 0.6)"
                            : "rgba(0, 0, 0, 0.6)",
                        },
                      ]}
                    >
                      Built with devotion 🙏
                    </Text>
                    <Text
                      style={[
                        styles.footerText,
                        {
                          color: isDark
                            ? "rgba(255, 255, 255, 0.6)"
                            : "rgba(0, 0, 0, 0.6)",
                        },
                      ]}
                    >
                      © 2024 Namoshkar Messenger
                    </Text>
                  </View>
                </View>
              </ScrollView>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: Platform.select({ 
      ios: 16, 
      android: getAndroidVersionAwarePadding(32, 40) 
    }),
    paddingBottom: 16,
    borderBottomWidth: 0,
    backdropFilter: "blur(20px)",
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  titleSection: {
    flex: 1,
  },
  headerTitle: {
    marginBottom: 8,
    fontSize: 24,
    fontWeight: "700",
  },
  titleUnderline: {
    width: 40,
    height: 4,
    borderRadius: 2,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  section: {
    marginBottom: 32,
  },
  firstSection: {
    marginTop: 24,
  },
  lastSection: {
    marginBottom: 48,
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: "600",
    letterSpacing: 0.5,
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  profileCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderRadius: 20,
    backdropFilter: "blur(20px)",
  },
  profileAvatar: {
    borderRadius: 32,
  },
  avatarImage: {
    width: 64,
    height: 64,
    borderRadius: 32,
  },
  profileInfo: {
    marginLeft: 20,
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: "700",
    letterSpacing: -0.3,
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 15,
    fontWeight: "500",
  },
  settingsItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderRadius: 16,
    marginVertical: 4,
    backdropFilter: "blur(20px)",
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 17,
    fontWeight: "600",
    letterSpacing: -0.2,
    marginBottom: 2,
  },
  itemSubtitle: {
    fontSize: 14,
    fontWeight: "400",
  },
  aboutCard: {
    alignItems: "center",
    padding: 24,
    borderRadius: 20,
    backdropFilter: "blur(20px)",
  },
  logo: {
    width: 240,
    height: 240,
    marginBottom: 16,
  },
  appName: {
    fontSize: 24,
    fontWeight: "700",
    letterSpacing: -0.5,
    marginBottom: 8,
  },
  appVersion: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 12,
  },
  appDescription: {
    fontSize: 15,
    fontWeight: "400",
    textAlign: "center",
    lineHeight: 22,
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 16,
    position: "relative",
  },
  notificationBadge: {
    position: "absolute",
    top: -5,
    right: -5,
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 4,
  },
  notificationBadgeText: {
    color: "white",
    fontSize: 12,
    fontWeight: "bold",
  },
  // Theme Picker Modal Styles
  modalOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "flex-end",
    alignItems: "center",
  },
  modalTouchableOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContentContainer: {
    flex: 1,
    justifyContent: "flex-end",
    width: "100%",
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingBottom: 40,
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 1,
    shadowRadius: 20,
    elevation: 10,
    maxHeight: "90%",
    width: "100%",
    alignSelf: "center",
    backgroundColor: "#ffffff",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    letterSpacing: -0.5,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  closeButtonText: {
    fontSize: 18,
    fontWeight: "600",
  },
  themeOptions: {
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  themeOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
  },
  themePreview: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 16,
  },
  lightThemePreview: {
    backgroundColor: "#ffffff",
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  darkThemePreview: {
    backgroundColor: "#1c1c1e",
  },
  systemThemePreview: {
    backgroundColor: "#ffffff",
    overflow: "hidden",
  },
  systemThemePreviewHalf: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "#1c1c1e",
    width: "50%",
  },
  themeInfo: {
    flex: 1,
  },
  themeName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  themeDescription: {
    fontSize: 14,
    fontWeight: "400",
  },
  checkmark: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  checkmarkText: {
    color: "white",
    fontSize: 14,
    fontWeight: "bold",
  },
  // About Modal Styles
  aboutScrollView: {},
  aboutContent: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  logoContainer: {
    alignItems: "center",
    marginBottom: 32,
    paddingTop: 16,
  },
  logoImage: {
    width: 120,
    height: 120,
    marginBottom: 16,
  },
  aboutAppName: {
    fontSize: 24,
    fontWeight: "700",
    letterSpacing: -0.5,
    marginBottom: 4,
    textAlign: "center",
  },
  aboutAppVersion: {
    fontSize: 16,
    fontWeight: "500",
    textAlign: "center",
  },
  descriptionSection: {
    marginBottom: 32,
  },
  descriptionTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 8,
    textAlign: "center",
  },
  descriptionText: {
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    textAlign: "center",
  },
  featuresSection: {
    marginBottom: 32,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
    textAlign: "center",
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
    backgroundColor: "rgba(255, 107, 53, 0.1)",
  },
  featureText: {
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 12,
  },
  contactSection: {
    marginBottom: 32,
  },
  contactTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
    textAlign: "center",
  },
  contactButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  contactButtonText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 12,
  },
  footerSection: {
    alignItems: "center",
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 107, 53, 0.2)",
  },
  footerText: {
    fontSize: 14,
    fontWeight: "400",
    marginBottom: 4,
    textAlign: "center",
  },
});
