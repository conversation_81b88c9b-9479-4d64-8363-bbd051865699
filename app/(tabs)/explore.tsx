import FloatingActionButton from '@/components/FloatingActionButton';
import NotificationPopup from '@/components/NotificationPopup';
import SearchBar from '@/components/SearchBar';
import { AnimatedTabScreen } from '@/components/AnimatedTabScreen';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { getAndroidVersionAwarePadding } from '@/utils/platform';
import { useActiveTab } from '@/hooks/useActiveTab';
import { ChatListItem, chatService } from '@/services/chatService';
import { notificationApiService } from '@/services/notificationApiService';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    Platform,
    SafeAreaView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    StatusBar
} from 'react-native';
import { Image } from 'expo-image';

export default function SanghaScreen() {
  const { user } = useAuth();
  const [groups, setGroups] = useState<ChatListItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredGroups, setFilteredGroups] = useState<ChatListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNotifications, setShowNotifications] = useState(false);
  const [unreadNotificationsCount, setUnreadNotificationsCount] = useState(0);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const activeTab = useActiveTab();
  const isActive = activeTab === 'explore';

  // Load data on mount
  useEffect(() => {
    loadGroups();
    loadUnreadNotificationsCount();
    
    // Refresh notifications count every 30 seconds
    const interval = setInterval(() => {
      loadUnreadNotificationsCount();
    }, 30000);
    
    // Cleanup interval on unmount
    return () => {
      clearInterval(interval);
    };
  }, [user]);

  // Filter groups when search query changes
  useEffect(() => {
    const filtered = groups.filter(group =>
      group.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredGroups(filtered);
  }, [searchQuery, groups]);

  const loadGroups = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const userChats = await chatService.getUserChats(user.uid);
      // Filter only group chats
      const groupChats = userChats.filter(chat => chat.isGroup);
      setGroups(groupChats);
      setFilteredGroups(groupChats);
    } catch (error) {
      console.error('Error loading groups:', error);
      Alert.alert('Error', 'Failed to load groups');
    } finally {
      setLoading(false);
    }
  };

  // Load unread notifications count
  const loadUnreadNotificationsCount = async () => {
    try {
      const count = await notificationApiService.getUnreadNotificationsCount();
      setUnreadNotificationsCount(count);
    } catch (error) {
      console.error('Failed to load unread notifications count:', error);
    }
  };

  const handleGroupPress = (groupId: string) => {
    Haptics.selectionAsync();
    router.push(`/chat/${groupId}`);
  };

  const handleCreateGroup = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push('/create-group');
  };

  const renderGroupItem = ({ item }: { item: ChatListItem }) => {
    const displayAvatar = item.photoURL;
    
    return (
      <TouchableOpacity
        style={[styles.userItem, { 
          backgroundColor: isDark ? 'rgba(26, 26, 26, 0.8)' : 'rgba(255, 255, 255, 0.9)',
          shadowColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 1,
          shadowRadius: 20,
          elevation: 1,
        }]}
        onPress={() => {
          Haptics.selectionAsync();
          handleGroupPress(item.id);
        }}
        disabled={loading}
        activeOpacity={0.9}
      >
        <View style={styles.avatarContainer}>
          <View style={[styles.avatarWrapper, {
            shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.15)',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 1,
            shadowRadius: 8,
            elevation: 4,
          }]}>
            {displayAvatar ? (
              <Image
                source={{ uri: displayAvatar }}
                style={styles.avatar}
              />
            ) : (
              <View style={[styles.avatar, styles.avatarPlaceholder]}>
                <Text style={styles.avatarText}>
                  {item.name.charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
          </View>
          {item.unreadCount > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadText}>{item.unreadCount > 99 ? '99+' : item.unreadCount}</Text>
            </View>
          )}
          <View style={[styles.groupIcon, {
            backgroundColor: '#FF6B35',
            shadowColor: '#FF6B35',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.3,
            shadowRadius: 2,
            elevation: 2,
          }]}>
            <IconSymbol name="person.2.fill" size={10} color="#fff" />
          </View>
        </View>
        
        <View style={styles.userContent}>
          <Text style={[styles.userName, { color: isDark ? '#fff' : '#000' }]} numberOfLines={1}>
            {item.name}
          </Text>
          <Text style={[styles.userStatus, { color: isDark ? '#8e8e93' : '#8e8e93' }]} numberOfLines={1}>
            {item.lastMessage || 'No messages yet'}
          </Text>
        </View>

        <View style={styles.groupInfo}>
          <IconSymbol name="person.2.fill" size={14} color={isDark ? '#8e8e93' : '#8e8e93'} />
          <Text style={[styles.participantCount, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
            {item.participants.length}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <AnimatedTabScreen isActive={isActive}>
      <View style={[styles.container, { backgroundColor: isDark ? '#000' : '#f8f9fa' }]}>
        <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
        <SafeAreaView style={styles.safeArea}>
          <View style={[styles.header, { 
            backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          }]}>
          <View style={styles.headerContent}>
            <View style={styles.titleSection}>
              <Text style={[styles.headerTitle, { 
                color: isDark ? '#ffffff' : '#000000',
                letterSpacing: -1.2,
              }]}>
                Groups
              </Text>
              <View style={[styles.titleUnderline, {
                backgroundColor: '#FF6B35',
              }]} />
            </View>
            <TouchableOpacity 
              style={[styles.notificationButton, {
                backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
              }]}
              onPress={() => setShowNotifications(true)}
            >
              <IconSymbol name="bell.fill" size={20} color="#FF6B35" />
              {unreadNotificationsCount > 0 && (
                <View style={[styles.notificationBadge, {
                  backgroundColor: '#FF6B35',
                }]}>
                  <Text style={styles.notificationBadgeText}>
                    {unreadNotificationsCount > 9 ? '9+' : unreadNotificationsCount}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.searchSection}>
          <View style={[styles.searchWrapper, {
            backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
            shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 1,
            shadowRadius: 16,
            elevation: 4,
          }]}>
            <SearchBar
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search groups"
              onClear={() => setSearchQuery('')}
            />
          </View>
        </View>

        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { 
            color: isDark ? '#8e8e93' : '#6c6c70',
            fontWeight: '600',
            letterSpacing: 0.5,
          }]}>
            YOUR GROUPS ({loading ? '...' : filteredGroups.length})
          </Text>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B35" />
            <Text style={[styles.loadingText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              Loading groups...
            </Text>
          </View>
        ) : filteredGroups.length === 0 ? (
          <View style={styles.emptyContainer}>
            <IconSymbol 
              name={searchQuery ? "magnifyingglass.circle" : "person.2.fill"} 
              size={48} 
              color={isDark ? '#8e8e93' : '#8e8e93'} 
            />
            <Text style={[styles.emptyText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              {searchQuery ? 'No groups found' : 'No group chats yet'}
            </Text>
            <Text style={[styles.emptySubtext, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              {searchQuery ? 'Try a different search term' : 'Create or join groups to connect with your community'}
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredGroups}
            renderItem={renderGroupItem}
            keyExtractor={(item) => item.id}
            style={styles.userList}
            contentContainerStyle={styles.userListContent}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        )}
      </SafeAreaView>
      <FloatingActionButton
        onPress={handleCreateGroup}
        icon="person.2.badge.plus"
        style={styles.fab}
      />
      <NotificationPopup
        visible={showNotifications}
        onClose={() => setShowNotifications(false)}
      />
      </View>
    </AnimatedTabScreen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: Platform.select({ 
      ios: 16, 
      android: getAndroidVersionAwarePadding(32, 40) 
    }),
    paddingBottom: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleSection: {
    flex: 1,
  },
  headerTitle: {
    marginBottom: 8,
    fontSize: 24,
    fontWeight: '700',
  },
  titleUnderline: {
    width: 40,
    height: 4,
    borderRadius: 2,
  },
  searchSection: {
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 16,
  },
  searchWrapper: {
    borderRadius: 20,
    overflow: 'hidden',
    backdropFilter: 'blur(20px)',
  },
  createGroupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 20,
    marginVertical: 8,
    borderRadius: 16,
    borderWidth: 0,
  },
  createGroupIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  createGroupText: {
    fontSize: 17,
    letterSpacing: -0.2,
  },
  sectionHeader: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  sectionTitle: {
    fontSize: 13,
  },
  userList: {
    flex: 1,
  },
  userListContent: {
    paddingHorizontal: 20,
  },
  separator: {
    height: 8,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginVertical: 2,
    borderRadius: 20,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatarWrapper: {
    borderRadius: 28,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  avatarPlaceholder: {
    backgroundColor: '#FF6B35',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  groupIcon: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    borderRadius: 10,
    padding: 4,
    borderWidth: 2,
    borderColor: '#fff',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#30d158',
    borderWidth: 3,
    borderColor: '#fff',
    shadowColor: '#30d158',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.5,
    shadowRadius: 2,
    elevation: 2,
  },
  unreadBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: '#FF6B35',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
    borderWidth: 2,
    borderColor: '#fff',
  },
  unreadText: {
    color: '#ffffff',
    fontSize: 11,
    fontWeight: '700',
    textAlign: 'center',
  },
  userContent: {
    flex: 1,
    justifyContent: 'center',
  },
  userName: {
    fontSize: 17,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  userStatus: {
    fontSize: 15,
    fontWeight: '400',
    lineHeight: 20,
  },
  fab: {
    position: 'absolute',
    bottom: Platform.select({ 
      ios: 90, 
      android: getAndroidVersionAwarePadding(120, 130) 
    }),
    right: 20,
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF6B35',
    shadowColor: '#FF6B35',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  notificationBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  groupInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
  },
  participantCount: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    fontWeight: '400',
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.7,
  },
});
