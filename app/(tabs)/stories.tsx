import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions,
  Platform,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { Image } from 'expo-image';
import { getAndroidVersionAwarePadding } from '@/utils/platform';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useActiveTab } from '@/hooks/useActiveTab';
import { AnimatedTabScreen } from '@/components/AnimatedTabScreen';
import StoryUpload from '@/components/StoryUpload';
import StoryViewer from '@/components/StoryViewer';
import FloatingActionButton from '@/components/FloatingActionButton';
import NotificationPopup from '@/components/NotificationPopup';
import { Story } from '@/types/story';
import { storyService } from '@/services/storyService';
import { userService } from '@/services/userService';
import { notificationApiService } from '@/services/notificationApiService';
import * as Haptics from 'expo-haptics';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function StoriesScreen() {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const activeTab = useActiveTab();
  const isActive = activeTab === 'stories';
  
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState(true);
  const [showUpload, setShowUpload] = useState(false);
  const [showViewer, setShowViewer] = useState(false);
  const [viewerIndex, setViewerIndex] = useState(0);
  const [visibleToUsers, setVisibleToUsers] = useState<string[]>([]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [unreadNotificationsCount, setUnreadNotificationsCount] = useState(0);

  
  // Load unread notifications count
  const loadUnreadNotificationsCount = async () => {
    try {
      const count = await notificationApiService.getUnreadNotificationsCount();
      setUnreadNotificationsCount(count);
    } catch (error) {
      console.error('Failed to load unread notifications count:', error);
    }
  };

  // Load stories and user contacts
  useEffect(() => {
    if (!user) return;

    const loadStoriesAndContacts = async () => {
      try {
        setLoading(true);
        
        // Load user contacts
        const contacts = await userService.getUserContacts(user.uid);
        const contactIds = contacts.map(contact => contact.contactId);
        const visibleTo = [user.uid, ...contactIds]; // Include self and contacts
        setVisibleToUsers(visibleTo);

        // Set up real-time story listener
        const unsubscribe = storyService.listenToContactStories(
          user.uid,
          (contactStories) => {
            setStories(contactStories);
            setLoading(false);
          }
        );

        return unsubscribe;
      } catch (error) {
        console.error('Error loading stories:', error);
        setLoading(false);
      }
    };

    loadStoriesAndContacts();
    loadUnreadNotificationsCount();
    
    // Refresh notifications count every 30 seconds
    const interval = setInterval(() => {
      loadUnreadNotificationsCount();
    }, 30000);
    
    // Cleanup interval on unmount
    return () => {
      clearInterval(interval);
    };
  }, [user]);

  const handleUploadStory = async (storyData: any) => {
    try {
      await storyService.createStory(storyData);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error uploading story:', error);
      Alert.alert('Error', 'Failed to upload story');
      throw error;
    }
  };

  const handleStoryPress = (index: number, isMyStory: boolean = false) => {
    Haptics.selectionAsync();
    
    // Calculate the actual index in the flattened stories array
    let actualIndex = 0;
    
    if (isMyStory) {
      // For My Stories, find the index in the flattened array
      const myStoriesGroup = storyGroups.find(group => group.userId === user?.uid);
      if (myStoriesGroup && index < myStoriesGroup.stories.length) {
        actualIndex = stories.findIndex(story => story.id === myStoriesGroup.stories[index].id);
      }
    } else {
      // For contact stories, find the index in the flattened array
      const contactStoriesGroup = contactStories[index];
      if (contactStoriesGroup && contactStoriesGroup.stories.length > 0) {
        actualIndex = stories.findIndex(story => story.id === contactStoriesGroup.stories[0].id);
      }
    }
    
    console.log('Story pressed:', { 
      index, 
      isMyStory, 
      actualIndex, 
      totalStories: stories.length,
      storyId: stories[actualIndex]?.id 
    });
    
    setViewerIndex(actualIndex);
    setTimeout(() => setShowViewer(true), 50);
  };

  const handleAddStory = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    if (visibleToUsers.length <= 1) { // Only self in the list
      Alert.alert('No Contacts', 'Add contacts to share stories with. Stories are only visible to your contacts.');
      return;
    }
    setShowUpload(true);
  };

  const formatTimeAgo = (date: Date | undefined) => {
    if (!date) return 'Unknown time';
    
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const groupStoriesByUser = (stories: Story[]) => {
    const grouped = stories.reduce((acc, story) => {
      if (!acc[story.userId]) {
        acc[story.userId] = [];
      }
      acc[story.userId].push(story);
      return acc;
    }, {} as Record<string, Story[]>);

    return Object.entries(grouped).map(([userId, userStories]) => ({
      userId,
      userName: userStories[0].userName,
      userAvatar: userStories[0].userAvatar,
      stories: userStories.sort((a, b) => (b.timestamp?.getTime() || 0) - (a.timestamp?.getTime() || 0)),
      latestStory: userStories[0],
    }));
  };

  const storyGroups = groupStoriesByUser(stories);
  
  // Separate user's stories from contact stories
  const myStories = storyGroups.filter(group => group.userId === user?.uid);
  const contactStories = storyGroups.filter(group => group.userId !== user?.uid);
  
  
  const renderStoryItem = ({ item, index }: { item: any; index: number }) => {
    const { userId, userName, userAvatar, stories, latestStory } = item;
    const hasViewed = latestStory.viewedBy?.includes(user?.uid || '') || false;
    const isOwnStory = userId === user?.uid;
    const isMyStory = userId === user?.uid;

    return (
      <TouchableOpacity
        style={[styles.storyItem, isMyStory && styles.myStoryItem]}
        onPress={() => handleStoryPress(index, isMyStory)}
        activeOpacity={0.7}
        onPressIn={() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)}
      >
        {/* Story Ring */}
        <View style={[
          styles.storyRing,
          !hasViewed && styles.storyRingUnviewed,
          isOwnStory && styles.storyRingOwn,
        ]}>
          <Image
            source={{ uri: userAvatar }}
            style={styles.storyAvatar}
          />
          {isOwnStory && (
            <View style={[styles.addStoryButton, { borderColor: isDark ? '#000' : '#fff' }]}>
              <IconSymbol name="plus" size={16} color="#fff" />
            </View>
          )}
        </View>

        {/* Story Info */}
        <View style={styles.storyInfo}>
          <Text style={[styles.storyUserName, { color: isDark ? '#fff' : '#000' }]} numberOfLines={1}>
            {isOwnStory ? 'Your Story' : userName}
          </Text>
          <Text style={[styles.storyTime, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
            {formatTimeAgo(latestStory.timestamp)}
          </Text>
        </View>

        {/* Story Count */}
        {stories.length > 1 && (
          <View style={styles.storyCount}>
            <Text style={styles.storyCountText}>{stories.length}</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => {
    const hasContacts = visibleToUsers.length > 1; // More than just self
    
    return (
      <View style={styles.emptyContainer}>
        <IconSymbol name="photo.circle.fill" size={64} color={isDark ? '#8e8e93' : '#8e8e93'} />
        <Text style={[styles.emptyTitle, { color: isDark ? '#fff' : '#000' }]}>
          {hasContacts ? 'No Stories Yet' : 'No Contacts'}
        </Text>
        <Text style={[styles.emptySubtitle, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
          {hasContacts 
            ? 'Share your moments with your contacts. Stories disappear after 24 hours.'
            : 'Add contacts to start sharing stories. Stories are only visible to your contacts.'
          }
        </Text>
                      </View>
    );
  };

  return (
    <AnimatedTabScreen isActive={isActive}>
      <View style={[styles.container, { backgroundColor: isDark ? '#000' : '#f8f9fa' }]}>
        <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
        <SafeAreaView style={styles.safeArea}>
          {/* Ultra Modern Header */}
          <View style={[styles.header, { 
            backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          }]}>
          <View style={styles.headerContent}>
            <View style={styles.titleSection}>
              <Text style={[styles.headerTitle, { 
                color: isDark ? '#ffffff' : '#000000',
                letterSpacing: -1.2,
              }]}>
                Stories
              </Text>
              <View style={[styles.titleUnderline, {
                backgroundColor: '#FF6B35',
              }]} />
            </View>
            <TouchableOpacity 
              style={[styles.notificationButton, {
                backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
              }]}
              onPress={() => setShowNotifications(true)}
            >
              <IconSymbol name="bell.fill" size={20} color="#FF6B35" />
              {unreadNotificationsCount > 0 && (
                <View style={[styles.notificationBadge, {
                  backgroundColor: '#FF6B35',
                }]}>
                  <Text style={styles.notificationBadgeText}>
                    {unreadNotificationsCount > 9 ? '9+' : unreadNotificationsCount}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
          </View>

        {/* Stories List */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B35" />
            <Text style={[styles.loadingText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              Loading stories...
            </Text>
          </View>
        ) : storyGroups.length === 0 ? (
          renderEmptyState()
        ) : (
          <FlatList
            data={contactStories}
            renderItem={renderStoryItem}
            keyExtractor={(item) => item.userId}
            contentContainerStyle={styles.storiesList}
            showsVerticalScrollIndicator={false}
            numColumns={3}
            columnWrapperStyle={styles.columnWrapper}
            ListHeaderComponent={
              myStories.length > 0 ? (
                <View style={styles.myStoriesSection}>
                  <View style={styles.sectionHeader}>
                    <Text style={[styles.sectionTitle, { 
                      color: isDark ? '#8e8e93' : '#6c6c70',
                      fontWeight: '600',
                      letterSpacing: 0.5,
                    }]}>
                      MY STORIES ({myStories.length})
                    </Text>
                  </View>
                  <FlatList
                    data={myStories}
                    renderItem={renderStoryItem}
                    keyExtractor={(item) => `my-${item.userId}`}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.myStoriesList}
                    ItemSeparatorComponent={() => <View style={styles.myStoriesSeparator} />}
                  />
                </View>
              ) : null
            }
          />
        )}

      {/* Floating Action Button */}
      <FloatingActionButton
        onPress={handleAddStory}
        icon="plus"
        style={styles.newStoryFab}
      />
      
  
          {/* Story Upload Modal */}
          <StoryUpload
            visible={showUpload}
            onClose={() => setShowUpload(false)}
            onUpload={handleUploadStory}
            visibleTo={visibleToUsers}
          />

          {/* Story Viewer Modal */}
          <StoryViewer
            visible={showViewer}
            onClose={() => setShowViewer(false)}
            stories={stories}
            initialStoryIndex={viewerIndex}
            currentUserId={user?.uid || ''}
          />
        </SafeAreaView>
      </View>
      <NotificationPopup
        visible={showNotifications}
        onClose={() => setShowNotifications(false)}
      />
    </AnimatedTabScreen>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: Platform.select({ 
      ios: 16, 
      android: getAndroidVersionAwarePadding(32, 40) 
    }),
    paddingBottom: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleSection: {
    flex: 1,
  },
  headerTitle: {
    marginBottom: 8,
    fontSize: 24,
    fontWeight: '700',
  },
  titleUnderline: {
    width: 40,
    height: 4,
    borderRadius: 2,
  },
  storiesList: {
    padding: 16,
  },
  columnWrapper: {
    justifyContent: 'flex-start',
    gap: 16,
  },
  myStoriesSection: {
    marginBottom: 24,
  },
  sectionHeader: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  sectionTitle: {
    fontSize: 13,
  },
  myStoriesList: {
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  myStoriesSeparator: {
    width: 16,
  },
  storyItem: {
    alignItems: 'center',
    width: SCREEN_WIDTH / 3 - 32,
  },
  myStoryItem: {
    width: 80,
    marginRight: 16,
  },
  storyRing: {
    width: 64,
    height: 64,
    borderRadius: 32,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    transform: [{ scale: 1 }],
    transitionProperty: 'transform',
    transitionDuration: '200ms',
  },
  storyRingUnviewed: {
    borderColor: '#FF6B35',
    borderWidth: 3,
    shadowColor: '#FF6B35',
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  storyRingOwn: {
    borderColor: '#FF6B35',
    borderStyle: 'dashed',
    borderWidth: 2,
  },
  storyAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  addStoryButton: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: '#FF6B35',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  storyInfo: {
    alignItems: 'center',
  },
  storyUserName: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 2,
  },
  storyTime: {
    fontSize: 10,
    textAlign: 'center',
  },
  storyCount: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#FF6B35',
    borderRadius: 10,
    minWidth: 18,
    height: 18,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  storyCountText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '700',
    textAlign: 'center',
  },
  newStoryFab: {
    position: 'absolute',
    bottom: Platform.select({ 
      ios: 90, 
      android: getAndroidVersionAwarePadding(120, 130) 
    }),
    right: 20,
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF6B35',
    shadowColor: '#FF6B35',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  notificationBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    fontWeight: '400',
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
      });