import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  <PERSON><PERSON>,
  FlatList,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { userService } from '@/services/userService';
import { chatService } from '@/services/chatService';
import { User } from '@/types/messenger';
import { Image } from 'expo-image';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@/contexts/AuthContext';
import { IconSymbol } from '@/components/ui/IconSymbol';

export default function GroupMembersScreen() {
  const { user } = useAuth();
  const { participants: participantsString, chatId, createdBy } = useLocalSearchParams<{ participants: string, chatId: string, createdBy: string }>();
  const [participants, setParticipants] = useState(JSON.parse(participantsString || '[]'));
  const [members, setMembers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const isCreator = user?.uid === createdBy;

  useEffect(() => {
    fetchMembers();
  }, [participantsString]);

  const fetchMembers = async () => {
    try {
      setLoading(true);
      const memberDetails = await Promise.all(
        participants.map((id: string) => userService.getUserById(id))
      );
      setMembers(memberDetails.filter(Boolean) as User[]);
    } catch (error) {
      console.error('Error fetching group members:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddMembers = () => {
    router.push({
      pathname: '/add-group-members',
      params: {
        chatId,
        existingParticipants: JSON.stringify(participants),
      }
    });
  };

  const handleRemoveMember = (memberId: string) => {
    if (memberId === createdBy) {
      Alert.alert("Cannot remove the group creator.");
      return;
    }

    Alert.alert(
      "Remove Member",
      "Are you sure you want to remove this member from the group?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          style: "destructive",
          onPress: async () => {
            try {
              await chatService.removeMemberFromGroup(chatId, memberId);
              const newParticipants = participants.filter((id: string) => id !== memberId);
              setParticipants(newParticipants);
              setMembers(members.filter(m => m.id !== memberId));
            } catch (error) {
              console.error('Error removing member:', error);
              Alert.alert("Error", "Failed to remove member.");
            }
          },
        },
      ]
    );
  };

  const renderMemberItem = ({ item }: { item: User }) => (
    <View style={[styles.memberItem, { 
      backgroundColor: isDark ? 'rgba(26, 26, 26, 0.8)' : 'rgba(255, 255, 255, 0.9)',
      shadowColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 1,
      shadowRadius: 8,
      elevation: 1,
    }]}>
      <View style={styles.avatarContainer}>
        <View style={[styles.avatarWrapper, {
          shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.15)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 4,
          elevation: 2,
        }]}>
          <Image
            source={{ uri: item.photoURL || 'https://via.placeholder.com/50' }}
            style={styles.avatar}
          />
        </View>
        {item.isOnline && (
          <View style={[styles.onlineIndicator, {
            shadowColor: '#30d158',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.5,
            shadowRadius: 2,
            elevation: 2,
          }]} />
        )}
      </View>
      
      <View style={styles.memberInfo}>
        <Text style={[styles.name, { 
          color: isDark ? '#ffffff' : '#000000',
          fontWeight: '600',
          letterSpacing: -0.2,
        }]} numberOfLines={1}>
          {item.name}
        </Text>
        <Text style={[styles.email, { 
          color: isDark ? 'rgba(142, 142, 147, 0.8)' : 'rgba(108, 108, 112, 0.8)',
          fontWeight: '400',
        }]}>
          {item.isOnline ? 'Active now' : 'Last seen recently'}
        </Text>
        {createdBy === item.id && (
          <View style={[styles.creatorBadge, {
            backgroundColor: isDark ? 'rgba(255, 107, 53, 0.2)' : 'rgba(255, 107, 53, 0.1)',
          }]}>
            <Text style={[styles.creatorText, { color: '#FF6B35' }]}>Creator</Text>
          </View>
        )}
      </View>

      {isCreator && user?.uid !== item.id && (
        <TouchableOpacity 
          onPress={() => handleRemoveMember(item.id)} 
          style={[styles.removeButton, {
            backgroundColor: isDark ? 'rgba(255, 59, 48, 0.2)' : 'rgba(255, 59, 48, 0.1)',
          }]}
          activeOpacity={0.8}
        >
          <IconSymbol name="trash" size={16} color="#FF3B30" />
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#000000' : '#f8f9fa' }]}>
      <SafeAreaView style={styles.safeArea}>
        {/* Modern Header */}
        <View style={[styles.header, { 
          backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 8,
          elevation: 4,
        }]}>
          <TouchableOpacity onPress={() => router.back()} style={[styles.cancelButton, {
            backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
          }]}>
            <Text style={styles.cancelText}>Back</Text>
          </TouchableOpacity>
          
          <Text style={[styles.headerTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
            Group Members
          </Text>
          
          <View style={styles.headerButtons}>
            {isCreator && (
              <TouchableOpacity 
                onPress={handleAddMembers} 
                style={[styles.createButton, { 
                  backgroundColor: '#FF6B35',
                  shadowColor: '#FF6B35',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.3,
                  shadowRadius: 4,
                  elevation: 3,
                }]}
              >
                <IconSymbol name="plus" size={16} color="#ffffff" />
              </TouchableOpacity>
            )}
            <TouchableOpacity 
              onPress={() => {
                console.log('Settings button pressed');
                try {
                  console.log('Navigating to group settings...');
                  router.push(`/group-settings?chatId=${chatId}&createdBy=${createdBy}`);
                  console.log('Navigation called');
                } catch (error) {
                  console.error('Navigation error:', error);
                  Alert.alert('Error', 'Failed to open group settings: ' + error.message);
                }
              }} 
              style={[styles.settingsButton, { 
                backgroundColor: isDark ? 'rgba(142, 142, 147, 0.2)' : 'rgba(142, 142, 147, 0.1)',
              }]}
            >
              <IconSymbol name="gearshape.fill" size={16} color={isDark ? '#8e8e93' : '#6c6c70'} />
            </TouchableOpacity>
          </View>
        </View>

  
        {/* Members List */}
        <View style={styles.membersSection}>
          <Text style={[styles.sectionTitle, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
            MEMBERS ({members.length})
          </Text>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#FF6B35" />
              <Text style={[styles.loadingText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
                Loading members...
              </Text>
            </View>
          ) : members.length === 0 ? (
            <View style={styles.emptyContainer}>
              <IconSymbol name="person.2.fill" size={48} color={isDark ? '#8e8e93' : '#8e8e93'} />
              <Text style={[styles.emptyText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
                No members found
              </Text>
            </View>
          ) : (
            <FlatList
              data={members}
              renderItem={renderMemberItem}
              keyExtractor={(item) => item.id}
              style={styles.membersList}
              showsVerticalScrollIndicator={false}
            />
          )}
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  cancelText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF6B35',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  createButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingsButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  membersSection: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: '600',
    letterSpacing: 0.5,
    paddingVertical: 12,
  },
  membersList: {
    flex: 1,
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginVertical: 4,
    borderRadius: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatarWrapper: {
    borderRadius: 28,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#30d158',
    borderWidth: 3,
    borderColor: '#fff',
  },
  memberInfo: {
    flex: 1,
  },
  name: {
    fontSize: 17,
    marginBottom: 4,
  },
  email: {
    fontSize: 14,
  },
  creatorBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 4,
  },
  creatorText: {
    fontSize: 12,
    fontWeight: '600',
  },
  removeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});