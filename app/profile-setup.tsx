import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { storageService } from '@/services/storageService';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Image,
    SafeAreaView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

export default function ProfileSetupScreen() {
  const { user, userProfile, updateProfile, setHasCompletedProfile } = useAuth();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [avatar, setAvatar] = useState<string | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [about, setAbout] = useState('');
  const [name, setName] = useState('');
  const [username, setUsername] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Initialize with user data from sign up
    if (userProfile) {
      setName(userProfile.displayName || '');
      setUsername(userProfile.displayName?.toLowerCase().replace(/\s+/g, '') || '');
    }
  }, [userProfile]);

  const handleSelectAvatar = async () => {
    // Request permission to access media library
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (permissionResult.granted === false) {
      Alert.alert('Permission required', 'Permission to access camera roll is required!');
      return;
    }
    
    // Launch image picker
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });
    
    if (!result.canceled) {
      setAvatar(result.assets[0].uri);
      setAvatarUrl(null); // Reset the uploaded URL when a new image is selected
    }
  };

  const uploadAvatar = async () => {
    if (!avatar) {
      throw new Error('No avatar selected');
    }

    setUploading(true);
    try {
      const downloadURL = await storageService.uploadAvatar(avatar);
      setAvatarUrl(downloadURL);
      return downloadURL;
    } catch (error) {
      console.error('Error uploading avatar:', error);
      Alert.alert('Upload Error', 'Failed to upload avatar. Please try again.');
      throw error;
    } finally {
      setUploading(false);
    }
  };

  const handleSaveProfile = async () => {
    if (!avatar) {
      Alert.alert('Profile Picture Required', 'Please select a profile picture');
      return;
    }
    
    if (!username.trim()) {
      Alert.alert('Username Required', 'Please enter a username');
      return;
    }

    setLoading(true);
    
    try {
      // Upload avatar if it hasn't been uploaded yet
      let avatarDownloadUrl = avatarUrl;
      if (!avatarDownloadUrl) {
        avatarDownloadUrl = await uploadAvatar();
      }

      // Update profile with the uploaded avatar URL
      const updates = {
        photoURL: avatarDownloadUrl,
        displayName: name,
      };
      
      const result = await updateProfile(updates);
      
      if (result.success) {
        // Mark profile as completed
        setHasCompletedProfile(true);
        // Navigate to main app after profile setup
        setTimeout(() => {
          router.replace('/(tabs)');
        }, 500); // Small delay to ensure state is updated
      } else {
        Alert.alert('Error', result.error || 'Failed to save profile');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to save profile');
      console.error('Profile save error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#000000' : '#f8f9fa' }]}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: isDark ? '#ffffff' : '#000000' }]}>
            Set Up Your Profile
          </Text>
          <Text style={[styles.subtitle, { color: isDark ? '#8e8e93' : '#6c6c70' }]}>
            Personalize your Namoshkar Messenger experience
          </Text>
        </View>

        {/* Profile Setup Form */}
        <View style={styles.form}>
          {/* Avatar Selection */}
          <View style={styles.avatarSection}>
            <Text style={[styles.sectionTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
              Profile Picture
            </Text>
            
            <TouchableOpacity 
              style={styles.avatarContainer}
              onPress={handleSelectAvatar}
              disabled={uploading || loading}
            >
              {avatarUrl ? (
                <Image 
                  source={{ uri: avatarUrl }} 
                  style={styles.avatarImage} 
                />
              ) : avatar ? (
                <Image 
                  source={{ uri: avatar }} 
                  style={styles.avatarImage} 
                />
              ) : (
                <View style={[styles.avatarPlaceholder, { 
                  backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
                }]}>
                  <IconSymbol name="person.fill" size={48} color={isDark ? '#8e8e93' : '#6c6c70'} />
                </View>
              )}
              
              <View style={[styles.editOverlay, { 
                backgroundColor: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
              }]}>
                <IconSymbol name="pencil" size={20} color="#ffffff" />
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.changeAvatarButton, { 
                backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
              }]}
              onPress={handleSelectAvatar}
              disabled={uploading || loading}
            >
              <Text style={[styles.changeAvatarText, { color: isDark ? '#ffffff' : '#000000' }]}>
                {uploading ? 'Uploading...' : 'Change Photo'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Username Section */}
          <View style={styles.usernameSection}>
            <Text style={[styles.sectionTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
              Username
            </Text>
            
            <View style={[styles.inputContainer, { 
              backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
            }]}>
              <TextInput
                style={[styles.input, { 
                  color: isDark ? '#ffffff' : '#000000',
                }]}
                value={username}
                onChangeText={setUsername}
                placeholder="Enter your username"
                placeholderTextColor={isDark ? '#8e8e93' : '#6c6c70'}
              />
            </View>
          </View>

          {/* About Section */}
          <View style={styles.aboutSection}>
            <Text style={[styles.sectionTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
              About You
            </Text>
            
            <View style={[styles.textInputContainer, { 
              backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
            }]}>
              <TextInput
                style={[styles.textInput, { 
                  color: isDark ? '#ffffff' : '#000000',
                }]}
                value={about}
                onChangeText={setAbout}
                placeholder="Tell us about yourself..."
                placeholderTextColor={isDark ? '#8e8e93' : '#6c6c70'}
                multiline
                numberOfLines={4}
                maxLength={160}
              />
              <Text style={[styles.characterCount, { color: isDark ? '#8e8e93' : '#6c6c70' }]}>
                {about.length}/160
              </Text>
            </View>
          </View>
        </View>

        {/* Save Button */}
        <TouchableOpacity 
          style={[styles.saveButton, { 
            backgroundColor: '#FF6B35',
            opacity: loading || uploading ? 0.7 : 1,
          }]}
          onPress={handleSaveProfile}
          disabled={loading || uploading}
        >
          <Text style={styles.saveButtonText}>
            {loading || uploading ? 'Saving...' : 'Continue to Messenger'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    alignItems: 'center',
    marginVertical: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '400',
    textAlign: 'center',
  },
  form: {
    flex: 1,
  },
  avatarSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  usernameSection: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  avatarImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: '#FF6B35',
  },
  avatarPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#FF6B35',
  },
  editOverlay: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  changeAvatarButton: {
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  changeAvatarText: {
    fontSize: 16,
    fontWeight: '600',
  },
  inputContainer: {
    borderRadius: 16,
    paddingHorizontal: 16,
  },
  input: {
    fontSize: 16,
    fontWeight: '400',
    paddingVertical: 16,
  },
  aboutSection: {
    marginBottom: 40,
  },
  textInputContainer: {
    borderRadius: 16,
    padding: 16,
  },
  textInput: {
    fontSize: 16,
    fontWeight: '400',
    minHeight: 100,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: 12,
    fontWeight: '400',
    textAlign: 'right',
    marginTop: 8,
  },
  saveButton: {
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
    shadowColor: '#FF6B35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  saveButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});