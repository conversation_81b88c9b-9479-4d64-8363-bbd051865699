import ChatHeader from '@/components/ChatHeader';
import MessageBubble from '@/components/MessageBubble';
import VideoMessageBubble from '@/components/VideoMessageBubble';
import MessageInput from '@/components/MessageInput';
import ImageViewer from '@/components/ImageViewer';
import UploadProgress from '@/components/UploadProgress';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Chat, chatService } from '@/services/chatService';
import { Message, User } from '@/types/messenger';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
    ActivityIndicator,
    FlatList,
    KeyboardAvoidingView,
    Platform,
    StatusBar,
    StyleSheet,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function ChatScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { user, userProfile } = useAuth();
  const [chat, setChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [replyingTo, setReplyingTo] = useState<Message | null>(null);
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [uploadVisible, setUploadVisible] = useState(false);
  const [uploadFileType, setUploadFileType] = useState<'image' | 'video' | 'file'>('image');
  const [uploadFileName, setUploadFileName] = useState<string | null>(null);
  const flatListRef = useRef<FlatList>(null);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Debug upload state changes
  useEffect(() => {
    console.log(`🔍 State changed - uploadVisible: ${uploadVisible}`);
  }, [uploadVisible]);

  // Set up real-time listeners
  useEffect(() => {
    if (!user || !id) return;

    let isMounted = true;

    const loadInitialData = async () => {
      setLoading(true);
      try {
        const chatDetails = await chatService.getChatDetails(id as string);
        if (isMounted) {
          setChat(chatDetails);
        }
      } catch (error) {
        console.error('Failed to load chat:', error);
      }
    }
    loadInitialData();
    
    // Listen to chat details changes (including avatar updates)
    const chatDetailsUnsubscribe = chatService.listenToChatDetails(id as string, (chatDetails) => {
      if (isMounted) {
        setChat(chatDetails);
      }
    });
    
    // Listen to chat messages
    const messagesUnsubscribe = chatService.listenToChatMessages(id as string, (newMessages) => {
      if (isMounted) {
        setMessages(newMessages);
        setLoading(false);
      }
    });

    return () => {
      isMounted = false;
      chatDetailsUnsubscribe();
      messagesUnsubscribe();
    };
  }, [user, id]);

  const handleSendMessage = async (text: string) => {
    if (!user || !userProfile || !id) return;

    try {
      await chatService.sendMessage(
        id as string,
        text.trim(),
        'text',
        replyingTo?.id
      );
      
      setReplyingTo(null);
      
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleSendImage = async (imageUri: string) => {
    if (!user || !userProfile || !id) return;

    // Start upload progress tracking
    console.log('🚀 Starting image upload...');
    setUploadVisible(true);
    setUploadFileType('image');
    setUploadFileName('Uploading image...');
    console.log('📱 Upload state set to visible');

    try {
      await chatService.sendMessage(
        id as string,
        '',
        'image',
        replyingTo?.id,
        imageUri,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        () => {
          setTimeout(() => {
            setUploadVisible(false);
          }, 500);
        }
      );
      
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('Failed to send image:', error);
      setUploadVisible(false);
    }
  };

  const handleSendVideo = async (videoUri: string) => {
    if (!user || !userProfile || !id) return;

    console.log('🎥 handleSendVideo called with URI:', videoUri);

    // Start upload progress tracking
    setUploadVisible(true);
    setUploadFileType('video');
    setUploadFileName('Uploading video...');

    try {
      await chatService.sendMessage(
        id as string,
        '',
        'video',
        replyingTo?.id,
        undefined,
        undefined,
        undefined,
        undefined,
        videoUri,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        () => {
          setTimeout(() => {
            setUploadVisible(false);
          }, 500);
        }
      );
      
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('❌ Failed to send video:', error);
      setUploadVisible(false);
    }
  };

  const handleAddReaction = async (messageId: string, emoji: string) => {
    if (!user || !id) return;
    
    try {
      await chatService.addReaction(messageId, emoji, id as string);
    } catch (error) {
      console.error('Failed to add reaction:', error);
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    if (!user || !id) return;
    
    try {
      await chatService.deleteMessage(messageId, id as string);
    } catch (error) {
      console.error('Failed to delete message:', error);
    }
  };

  const handleReply = (message: Message) => {
    setReplyingTo(message);
  };

  const handleBack = () => {
    router.back();
  };

  const handleImagePress = (imageUrl: string) => {
    setSelectedImageUrl(imageUrl);
    setImageViewerVisible(true);
  };

  const renderMessage = ({ item }: { item: Message }) => {
    const isCurrentUser = item.senderId === user?.uid;
    
    // Debug logging for video messages
    if (item.type === 'video') {
      console.log('🎬 Rendering video message:', {
        id: item.id,
        videoUrl: item.videoUrl,
        videoThumbnail: item.videoThumbnail,
        videoDuration: item.videoDuration,
        videoSize: item.videoSize
      });
    }
    
    const senderDetails = chat?.participantDetails?.[item.senderId];

    const sender: User = {
      id: item.senderId,
      name: senderDetails?.name || item.senderName || 'Unknown User',
      email: senderDetails?.email || item.senderEmail || '',
      displayName: senderDetails?.name || item.senderName || 'Unknown User',
      avatar: senderDetails?.photoURL || 'https://via.placeholder.com/32x32.png?text=U',
      isOnline: false, // This would require another service to check online status
      lastSeen: new Date(), // This would also require another service
      createdAt: new Date(),
    };

    // Add currentUserId to message for video bubble
    const messageWithUserId = {
      ...item,
      currentUserId: user?.uid
    };

    if (item.type === 'video') {
      return (
        <VideoMessageBubble
          message={messageWithUserId}
          isDark={isDark}
          onAddReaction={handleAddReaction}
          onDelete={handleDeleteMessage}
        />
      );
    }

    return (
      <MessageBubble
        message={item}
        sender={sender}
        isCurrentUser={isCurrentUser}
        isGroup={chat?.type === 'group' || false}
        onAddReaction={handleAddReaction}
        onReply={handleReply}
        onImagePress={handleImagePress}
        onDelete={handleDeleteMessage}
      />
    );
  };

  if (loading || !chat) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: isDark ? '#000' : '#fff' }}>
        <ActivityIndicator size="large" color="#FF6B35" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <ChatHeader 
          chat={chat || {
            id: id || 'new',
            participants: [],
            participantDetails: {},
            name: '',
            photoURL: undefined,
            type: 'direct',
            createdAt: new Date(),
            lastActivity: new Date(),
            messages: [],
            isGroup: false,
            unreadCount: {},
            createdBy: ''
          }} 
          onBack={handleBack} 
          onViewInfo={() => {}} 
          isDark={isDark}
        />
        
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={[styles.messagesContainer, { backgroundColor: isDark ? '#000' : '#f8f9fa' }]}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
          onLayout={() => flatListRef.current?.scrollToEnd({ animated: true })}
        />
        
        <MessageInput
          onSend={handleSendMessage}
          onSendImage={handleSendImage}
          onSendVideo={handleSendVideo}
          replyingTo={replyingTo}
          onCancelReply={() => setReplyingTo(null)}
          isDark={isDark}
        />
        <ImageViewer
          visible={imageViewerVisible}
          imageUrl={selectedImageUrl}
          onClose={() => setImageViewerVisible(false)}
        />
        <UploadProgress
          visible={uploadVisible}
          fileName={uploadFileName}
          fileType={uploadFileType}
          onCancel={() => {
            setUploadVisible(false);
          }}
          isDark={isDark}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  keyboardContainer: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
  },
});