import { notificationApiService } from '@/services/notificationApiService';
import React, { useState } from 'react';
import { <PERSON><PERSON>, Button, StyleSheet, Text, View } from 'react-native';

export default function TestNotificationsScreen() {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);

  const testGetNotifications = async () => {
    try {
      setLoading(true);
      console.log('Fetching notifications...');
      const notifs = await notificationApiService.getUserNotifications(10);
      console.log('Notifications fetched:', notifs);
      setNotifications(notifs);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      Alert.alert('Error', 'Failed to fetch notifications: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const testGetUnreadCount = async () => {
    try {
      console.log('Fetching unread count...');
      const count = await notificationApiService.getUnreadNotificationsCount();
      console.log('Unread count:', count);
      setUnreadCount(count);
    } catch (error) {
      console.error('Error fetching unread count:', error);
      Alert.alert('Error', 'Failed to fetch unread count: ' + (error as Error).message);
    }
  };

  const testMarkAsRead = async () => {
    try {
      if (notifications.length > 0) {
        const firstNotification = notifications[0];
        console.log('Marking notification as read:', firstNotification.id);
        await notificationApiService.markNotificationsAsRead([firstNotification.id]);
        Alert.alert('Success', 'Notification marked as read');
        // Refresh the notifications
        testGetNotifications();
        testGetUnreadCount();
      } else {
        Alert.alert('Info', 'No notifications to mark as read');
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      Alert.alert('Error', 'Failed to mark notification as read: ' + (error as Error).message);
    }
  };

  const testMarkAllAsRead = async () => {
    try {
      console.log('Marking all notifications as read...');
      await notificationApiService.markAllNotificationsAsRead();
      Alert.alert('Success', 'All notifications marked as read');
      // Refresh the notifications and count
      testGetNotifications();
      testGetUnreadCount();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      Alert.alert('Error', 'Failed to mark all notifications as read: ' + (error as Error).message);
    }
  };

  const createTestNotification = async () => {
    try {
      console.log('Creating test notification...');
      const success = await notificationApiService.createTestNotification();
      if (success) {
        Alert.alert('Success', 'Test notification created successfully');
        // Refresh the notifications and count
        testGetNotifications();
        testGetUnreadCount();
      } else {
        Alert.alert('Error', 'Failed to create test notification');
      }
    } catch (error) {
      console.error('Error creating test notification:', error);
      Alert.alert('Error', 'Failed to create test notification: ' + (error as Error).message);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Notification Service Test</Text>
      
      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>Unread Count: {unreadCount}</Text>
        <Text style={styles.infoText}>Notifications: {notifications.length}</Text>
      </View>
      
      <View style={styles.buttonContainer}>
        <Button title="Get Notifications" onPress={testGetNotifications} disabled={loading} />
        <Button title="Get Unread Count" onPress={testGetUnreadCount} disabled={loading} />
        <Button title="Create Test Notification" onPress={createTestNotification} disabled={loading} />
        <Button title="Mark First as Read" onPress={testMarkAsRead} disabled={loading || notifications.length === 0} />
        <Button title="Mark All as Read" onPress={testMarkAllAsRead} disabled={loading} />
      </View>
      
      {loading && <Text style={styles.loadingText}>Loading...</Text>}
      
      <View style={styles.notificationsContainer}>
        <Text style={styles.sectionTitle}>Notifications:</Text>
        {notifications.map((notification) => (
          <View key={notification.id} style={styles.notificationItem}>
            <Text style={styles.notificationTitle}>{notification.title}</Text>
            <Text style={styles.notificationBody}>{notification.body}</Text>
            <Text style={styles.notificationStatus}>Read: {notification.read ? 'Yes' : 'No'}</Text>
            <Text style={styles.notificationStatus}>Sent: {notification.sentAt.toString()}</Text>
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  infoContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoText: {
    fontSize: 18,
    marginBottom: 10,
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    marginBottom: 20,
  },
  notificationsContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  notificationItem: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  notificationBody: {
    fontSize: 14,
    marginBottom: 5,
  },
  notificationStatus: {
    fontSize: 12,
    color: '#666',
  },
});