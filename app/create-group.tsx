import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Contact } from '@/types/messenger';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  FlatList,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { messagingService } from '../services/messagingService';
import { userService } from '../services/userService';

export default function CreateGroupScreen() {
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [groupAvatar, setGroupAvatar] = useState<string | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { user } = useAuth();

  useEffect(() => {
    if (!user) return;
    
    // Set up real-time listener for contacts
    const unsubscribe = userService.listenToUserContacts(user.uid, (userContacts) => {
      console.log('🔄 Real-time contacts update received in create-group:', userContacts.length, 'contacts');
      setContacts(userContacts);
      setLoading(false);
    });
    
    return () => {
      unsubscribe();
    };
  }, [user]);

  
  const filteredContacts = contacts.filter(contact =>
    contact.contactName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.contactEmail.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const toggleUserSelection = (contactId: string) => {
    setSelectedUsers(prev => 
      prev.includes(contactId) 
        ? prev.filter(id => id !== contactId)
        : [...prev, contactId]
    );
  };

  const pickGroupAvatar = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled && result.assets[0]) {
      setGroupAvatar(result.assets[0].uri);
    }
  };

  const createGroup = async () => {
    if (!groupName.trim()) {
      Alert.alert('Error', 'Please enter a sangha name');
      return;
    }

    if (selectedUsers.length === 0) {
      Alert.alert('Error', 'Please select at least 1 devotee for the sangha');
      return;
    }

    try {
      setCreating(true);
      
      // Create group chat with selected participants
      const chatId = await messagingService.createChatAndStart(
        selectedUsers, // participant contact IDs
        'group',
        groupName.trim(),
        groupDescription.trim() || undefined,
        groupAvatar || undefined
      );

      Alert.alert(
        'Sangha Created! 🙏',
        `"${groupName}" has been created with ${selectedUsers.length + 1} members. May this sangha bring spiritual growth and divine blessings to all!`,
        [
          { 
            text: 'Hari Om', 
            onPress: () => {
              // Go back to the chat list where the new group should appear
              setTimeout(() => {
                router.back();
              }, 1000); // Small delay to ensure Firestore propagation
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error creating group:', error);
      Alert.alert('Error', 'Failed to create sangha. Please try again.');
    } finally {
      setCreating(false);
    }
  };

  const renderContactItem = ({ item }: { item: Contact }) => {
    const isSelected = selectedUsers.includes(item.contactId);
    
    return (
      <TouchableOpacity
        style={[styles.userItem, { 
          backgroundColor: isDark ? 'rgba(26, 26, 26, 0.8)' : 'rgba(255, 255, 255, 0.9)',
          shadowColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 1,
          shadowRadius: 8,
          elevation: 1,
        }]}
        onPress={() => toggleUserSelection(item.contactId)}
        activeOpacity={0.8}
      >
        <View style={styles.avatarContainer}>
          <View style={[styles.avatarWrapper, {
            shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.15)',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 1,
            shadowRadius: 4,
            elevation: 2,
          }]}>
            <Image
              source={{ uri: item.contactPhotoURL || 'https://via.placeholder.com/50' }}
              style={styles.avatar}
            />
          </View>
          {item.isOnline && (
            <View style={[styles.onlineIndicator, {
              shadowColor: '#30d158',
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.5,
              shadowRadius: 2,
              elevation: 2,
            }]} />
          )}
        </View>
        
        <View style={styles.userContent}>
          <Text style={[styles.userName, { 
            color: isDark ? '#ffffff' : '#000000',
            fontWeight: '600',
            letterSpacing: -0.2,
          }]} numberOfLines={1}>
            {item.contactName}
          </Text>
          <Text style={[styles.userStatus, { 
            color: isDark ? 'rgba(142, 142, 147, 0.8)' : 'rgba(108, 108, 112, 0.8)',
            fontWeight: '400',
          }]}>
            {item.isOnline ? 'Active now' : 'Last seen recently'}
          </Text>
        </View>

        <View style={[
          styles.checkbox,
          { 
            backgroundColor: isSelected ? '#FF6B35' : 'transparent',
            borderColor: isSelected ? '#FF6B35' : '#8e8e93',
            shadowColor: isSelected ? '#FF6B35' : 'transparent',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: isSelected ? 0.3 : 0,
            shadowRadius: 4,
            elevation: isSelected ? 3 : 0,
          }
        ]}>
          {isSelected && <IconSymbol name="checkmark" size={16} color="#fff" />}
        </View>
      </TouchableOpacity>
    );
  };

  const renderSelectedContact = ({ item }: { item: string }) => {
    const contact = contacts.find(c => c.contactId === item);
    if (!contact) return null;

    return (
      <View style={styles.selectedUserItem}>
        <Image
          source={{ uri: contact.contactPhotoURL || 'https://via.placeholder.com/40' }}
          style={styles.selectedUserAvatar}
        />
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => toggleUserSelection(item)}
          activeOpacity={0.8}
        >
          <IconSymbol name="xmark" size={12} color="#ffffff" />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#000000' : '#f8f9fa' }]}>
      <SafeAreaView style={styles.safeArea}>
        {/* Modern Header */}
        <View style={[styles.header, { 
          backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 8,
          elevation: 4,
        }]}>
          <TouchableOpacity onPress={() => router.back()} style={[styles.cancelButton, {
            backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
          }]}>
            <Text style={styles.cancelText}>Cancel</Text>
          </TouchableOpacity>
          
          <Text style={[styles.headerTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
            New Sangha
          </Text>
          
          <TouchableOpacity 
            onPress={createGroup} 
            style={[styles.createButton, { 
              opacity: (groupName.trim() && selectedUsers.length >= 0 && !creating) ? 1 : 0.5,
              backgroundColor: '#FF6B35',
              shadowColor: '#FF6B35',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.3,
              shadowRadius: 4,
              elevation: 3,
            }]}
            disabled={!groupName.trim() || creating}
          >
            {creating ? (
              <ActivityIndicator size="small" color="#ffffff" />
            ) : (
              <Text style={[styles.createText, { color: '#ffffff' }]}>Create</Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Modern Group Info */}
        <View style={[styles.groupInfoContainer, { 
          backgroundColor: isDark ? 'rgba(26, 26, 26, 0.8)' : 'rgba(255, 255, 255, 0.9)',
          shadowColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 12,
          elevation: 2,
        }]}>
          <TouchableOpacity onPress={pickGroupAvatar} style={styles.avatarButton}>
            {groupAvatar ? (
              <Image source={{ uri: groupAvatar }} style={[styles.groupAvatar, {
                shadowColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 1,
                shadowRadius: 4,
              }]} />
            ) : (
              <View style={[styles.placeholderAvatar, { 
                backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
                shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 1,
                shadowRadius: 4,
                elevation: 2,
              }]}>
                <IconSymbol name="camera.fill" size={24} color="#FF6B35" />
              </View>
            )}
          </TouchableOpacity>
          
          <TextInput
            style={[styles.groupNameInput, { 
              color: isDark ? '#ffffff' : '#000000',
              borderBottomColor: '#FF6B35',
            }]}
            placeholder="Sangha name"
            placeholderTextColor={isDark ? 'rgba(142, 142, 147, 0.7)' : 'rgba(142, 142, 147, 0.8)'}
            value={groupName}
            onChangeText={setGroupName}
            maxLength={50}
          />
          
          <TextInput
            style={[styles.groupDescriptionInput, { 
              color: isDark ? '#ffffff' : '#000000',
              borderBottomColor: isDark ? 'rgba(142, 142, 147, 0.3)' : 'rgba(142, 142, 147, 0.4)',
            }]}
            placeholder="Description (optional)"
            placeholderTextColor={isDark ? 'rgba(142, 142, 147, 0.7)' : 'rgba(142, 142, 147, 0.8)'}
            value={groupDescription}
            onChangeText={setGroupDescription}
            maxLength={100}
            multiline
          />
        </View>

        {/* Selected Contacts */}
        {selectedUsers.length > 0 && (
          <View style={styles.selectedUsersContainer}>
            <Text style={[styles.selectedUsersTitle, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
              SELECTED ({selectedUsers.length})
            </Text>
            <FlatList
              data={selectedUsers}
              renderItem={renderSelectedContact}
              keyExtractor={(item) => item}
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.selectedUsersList}
            />
          </View>
        )}

        {/* Modern Search */}
        <View style={styles.searchSection}>
          <View style={[styles.searchWrapper, {
            backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
            shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 1,
            shadowRadius: 8,
            elevation: 2,
          }]}>
            <View style={[styles.searchContainer, { backgroundColor: 'transparent' }]}>
              <View style={[styles.iconContainer, {
                backgroundColor: isDark ? 'rgba(142, 142, 147, 0.2)' : 'rgba(142, 142, 147, 0.15)',
              }]}>
                <IconSymbol name="magnifyingglass" size={16} color="#8e8e93" />
              </View>
              <TextInput
                style={[styles.searchInput, { 
                  color: isDark ? '#ffffff' : '#000000',
                  fontSize: 17,
                  fontWeight: '400',
                  letterSpacing: -0.2,
                }]}
                placeholder="Search devotees"
                placeholderTextColor={isDark ? 'rgba(142, 142, 147, 0.8)' : 'rgba(142, 142, 147, 0.9)'}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </View>
          </View>
        </View>

        {/* Contacts List */}
        <View style={styles.usersSection}>
          <Text style={[styles.sectionTitle, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
            CONTACTS ({filteredContacts.length})
          </Text>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#FF6B35" />
              <Text style={[styles.loadingText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
                Loading contacts...
              </Text>
            </View>
          ) : filteredContacts.length === 0 ? (
            <View style={styles.emptyContainer}>
              <IconSymbol name="person.2.fill" size={48} color={isDark ? '#8e8e93' : '#8e8e93'} />
              <Text style={[styles.emptyText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
                {contacts.length === 0 ? 'No contacts found' : 'No contacts match your search'}
              </Text>
              {contacts.length === 0 && (
                <Text style={[styles.emptySubtext, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
                  Add contacts first to create a group
                </Text>
              )}
            </View>
          ) : (
            <FlatList
              data={filteredContacts}
              renderItem={renderContactItem}
              keyExtractor={(item) => item.id}
              style={styles.usersList}
              showsVerticalScrollIndicator={false}
            />
          )}
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  cancelText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF6B35',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  createButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  createText: {
    fontSize: 16,
    fontWeight: '700',
  },
  groupInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    marginHorizontal: 20,
    marginVertical: 16,
    borderRadius: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  avatarButton: {
    marginRight: 20,
  },
  groupAvatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
  },
  placeholderAvatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
  },
  groupNameInput: {
    flex: 1,
    fontSize: 18,
    paddingVertical: 16,
    borderBottomWidth: 2,
    fontWeight: '500',
    letterSpacing: -0.2,
  },
  groupDescriptionInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    fontWeight: '400',
    letterSpacing: -0.2,
    marginTop: 8,
    minHeight: 40,
    maxHeight: 80,
  },
  searchSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchWrapper: {
    borderRadius: 16,
    overflow: 'hidden',
    backdropFilter: 'blur(20px)',
  },
  selectedUsersContainer: {
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  selectedUsersTitle: {
    fontSize: 13,
    fontWeight: '600',
    letterSpacing: 0.5,
    marginBottom: 12,
  },
  selectedUsersList: {
    paddingHorizontal: 0,
  },
  selectedUserItem: {
    position: 'relative',
    marginRight: 16,
  },
  selectedUserAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 3,
  },
  removeButton: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: '#FF6B35',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#FF6B35',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 0,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  searchInput: {
    flex: 1,
  },
  usersSection: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: '600',
    letterSpacing: 0.5,
    paddingVertical: 12,
  },
  usersList: {
    flex: 1,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginVertical: 4,
    borderRadius: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatarWrapper: {
    borderRadius: 28,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#30d158',
    borderWidth: 3,
    borderColor: '#fff',
  },
  userContent: {
    flex: 1,
  },
  userName: {
    fontSize: 17,
    marginBottom: 4,
  },
  userStatus: {
    fontSize: 14,
  },
  checkbox: {
    width: 28,
    height: 28,
    borderRadius: 14,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  emptySubtext: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '400',
    textAlign: 'center',
  },
});