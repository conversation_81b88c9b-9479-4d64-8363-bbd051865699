import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { storageService } from '@/services/storageService';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import React, { useEffect, useState, useRef } from 'react';
import {
    Alert,
    Image,
    SafeAreaView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
    ActivityIndicator
} from 'react-native';

export default function EditProfileScreen() {
    const { user, userProfile, updateProfile } = useAuth();
    const colorScheme = useColorScheme();
    const isDark = colorScheme === 'dark';
    const [avatar, setAvatar] = useState<string | null>(null);
    const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
    const [uploading, setUploading] = useState(false);
    const [loading, setLoading] = useState(false);
    const [name, setName] = useState('');
    const isMounted = useRef(true);

    useEffect(() => {
        // Load current user data
        if (userProfile) {
            setName(userProfile.displayName || '');
            setAvatarUrl(userProfile.photoURL || null);
            setAvatar(null); // Clear any local file selection
        }
    }, [userProfile]);

    useEffect(() => {
        return () => {
            isMounted.current = false;
        };
    }, []);

    const handleSelectAvatar = async () => {
        // Request permission to access media library
        const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
        
        if (permissionResult.granted === false) {
            Alert.alert('Permission required', 'Permission to access camera roll is required!');
            return;
        }
        
        // Launch image picker
        const result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ['images'],
            allowsEditing: true,
            aspect: [1, 1],
            quality: 1,
        });
        
        if (!result.canceled) {
            setAvatar(result.assets[0].uri);
            // Don't reset avatarUrl yet - keep showing current avatar until new one is uploaded
        }
    };

    const uploadAvatar = async () => {
        if (!avatar) {
            throw new Error('No avatar selected');
        }

        setUploading(true);
        try {
            const downloadURL = await storageService.uploadAvatar(avatar);
            setAvatarUrl(downloadURL);
            return downloadURL;
        } catch (error) {
            console.error('Error uploading avatar:', error);
            Alert.alert('Upload Error', 'Failed to upload avatar. Please try again.');
            throw error;
        } finally {
            setUploading(false);
        }
    };

    const handleSaveProfile = async () => {
        if (!name.trim()) {
            Alert.alert('Name Required', 'Please enter your name');
            return;
        }

        setLoading(true);
        
        try {
            let finalAvatarUrl = avatarUrl;
            
            // Upload new avatar if one was selected
            if (avatar) {
                finalAvatarUrl = await uploadAvatar();
            }

            // Update profile
            const updates = {
                displayName: name.trim(),
                ...(finalAvatarUrl && { photoURL: finalAvatarUrl }),
            };
            
            const result = await updateProfile(updates);
            
            if (result.success) {
                Alert.alert('Success', 'Profile updated successfully');
                if (isMounted.current) {
                    router.back();
                }
            } else {
                Alert.alert('Error', result.error || 'Failed to update profile');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to update profile');
            console.error('Profile update error:', error);
        } finally {
            if (isMounted.current) {
                setLoading(false);
            }
        }
    };

    const handleRemoveAvatar = () => {
        Alert.alert(
            'Remove Profile Picture',
            'Are you sure you want to remove your profile picture?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Remove',
                    style: 'destructive',
                    onPress: () => {
                        setAvatar(null);
                        setAvatarUrl(null);
                    }
                }
            ]
        );
    };

    return (
        <View style={[styles.container, { backgroundColor: isDark ? '#000000' : '#f8f9fa' }]}>
            <SafeAreaView style={styles.safeArea}>
                {/* Header */}
                <View style={[styles.header, { 
                    backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
                    shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 1,
                    shadowRadius: 8,
                    elevation: 4,
                }]}>
                    <TouchableOpacity onPress={() => router.back()} style={[styles.backButton, {
                        backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
                    }]}>
                        <Text style={styles.backText}>Back</Text>
                    </TouchableOpacity>
                    
                    <Text style={[styles.headerTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
                        Edit Profile
                    </Text>
                    
                    <View style={styles.headerSpacer} />
                </View>

                <View style={styles.content}>

                {/* Profile Edit Form */}
                <View style={styles.form}>
                    {/* Avatar Section */}
                    <View style={[styles.avatarSection, {
                        marginHorizontal: 20,
                        marginVertical: 16,
                    }]}>
                        <Text style={[styles.sectionTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
                            Profile Picture
                        </Text>
                        
                        <TouchableOpacity 
                            style={styles.avatarContainer}
                            onPress={handleSelectAvatar}
                            disabled={uploading || loading}
                        >
                            {avatar ? (
                                <Image 
                                    source={{ uri: avatar }} 
                                    style={styles.avatarImage} 
                                />
                            ) : avatarUrl ? (
                                <Image 
                                    source={{ uri: avatarUrl }} 
                                    style={styles.avatarImage} 
                                />
                            ) : (
                                <View style={[styles.avatarPlaceholder, { 
                                    backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
                                }]}>
                                    <IconSymbol name="person.fill" size={48} color={isDark ? '#8e8e93' : '#6c6c70'} />
                                </View>
                            )}
                            
                            <View style={[styles.editOverlay, { 
                                backgroundColor: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
                            }]}>
                                <IconSymbol name="pencil" size={20} color="#ffffff" />
                            </View>
                        </TouchableOpacity>

                        <View style={styles.avatarButtonContainer}>
                            <TouchableOpacity 
                                style={[styles.changeAvatarButton, { 
                                    backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
                                }]}
                                onPress={handleSelectAvatar}
                                disabled={uploading || loading}
                            >
                                <Text style={[styles.changeAvatarText, { color: isDark ? '#ffffff' : '#000000' }]}>
                                    {uploading ? 'Uploading...' : 'Change Photo'}
                                </Text>
                            </TouchableOpacity>

                            {avatarUrl && (
                                <TouchableOpacity 
                                    style={[styles.removeAvatarButton, { 
                                        backgroundColor: isDark ? 'rgba(255, 59, 48, 0.2)' : 'rgba(255, 59, 48, 0.1)',
                                    }]}
                                    onPress={handleRemoveAvatar}
                                    disabled={uploading || loading}
                                >
                                    <Text style={[styles.removeAvatarText, { color: '#FF3B30' }]}>
                                        Remove
                                    </Text>
                                </TouchableOpacity>
                            )}
                        </View>
                    </View>

                    {/* Name Section */}
                    <View style={[styles.nameSection, {
                        marginHorizontal: 20,
                        marginVertical: 16,
                    }]}>
                        <Text style={[styles.sectionTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
                            Name
                        </Text>
                        
                        <View style={[styles.inputContainer, { 
                            backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
                        }]}>
                            <TextInput
                                style={[styles.input, { 
                                    color: isDark ? '#ffffff' : '#000000',
                                }]}
                                value={name}
                                onChangeText={setName}
                                placeholder="Enter your name"
                                placeholderTextColor={isDark ? '#8e8e93' : '#6c6c70'}
                                maxLength={50}
                            />
                        </View>
                    </View>

                    {/* Email Section (Read-only) */}
                    <View style={[styles.emailSection, {
                        marginHorizontal: 20,
                        marginVertical: 16,
                    }]}>
                        <Text style={[styles.sectionTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
                            Email
                        </Text>
                        
                        <View style={[styles.inputContainer, { 
                            backgroundColor: isDark ? 'rgba(42, 42, 42, 0.4)' : 'rgba(240, 240, 240, 0.4)',
                        }]}>
                            <TextInput
                                style={[styles.input, { 
                                    color: isDark ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)',
                                }]}
                                value={user?.email || ''}
                                placeholder="Email"
                                placeholderTextColor={isDark ? '#8e8e93' : '#6c6c70'}
                                editable={false}
                                selectTextOnFocus={false}
                            />
                        </View>
                        <Text style={[styles.emailHint, { color: isDark ? '#8e8e93' : '#6c6c70' }]}>
                            Email cannot be changed
                        </Text>
                    </View>
                </View>

                {/* Save Button */}
                <TouchableOpacity 
                    style={[styles.saveButton, { 
                        backgroundColor: '#FF6B35',
                        opacity: loading || uploading ? 0.7 : 1,
                        marginHorizontal: 20,
                        marginVertical: 16,
                    }]}
                    onPress={handleSaveProfile}
                    disabled={loading || uploading}
                >
                    {loading || uploading ? (
                        <ActivityIndicator size="small" color="#ffffff" />
                    ) : (
                        <Text style={styles.saveButtonText}>
                            Save Changes
                        </Text>
                    )}
                </TouchableOpacity>
                </View>
            </SafeAreaView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    safeArea: {
        flex: 1,
    },
    content: {
        flex: 1,
        paddingHorizontal: 24,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        paddingVertical: 16,
        borderBottomWidth: 0,
        backdropFilter: 'blur(20px)',
    },
    backButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
    },
    backText: {
        fontSize: 16,
        fontWeight: '600',
        color: '#FF6B35',
    },
    headerSpacer: {
        width: 60, // Same width as back button for centering
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: '700',
        letterSpacing: -0.3,
        flex: 1,
        textAlign: 'center',
    },
    form: {
        flex: 1,
    },
    avatarSection: {
        alignItems: 'center',
        marginBottom: 30,
    },
    nameSection: {
        marginBottom: 30,
    },
    emailSection: {
        marginBottom: 30,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 16,
    },
    avatarContainer: {
        position: 'relative',
        marginBottom: 16,
    },
    avatarImage: {
        width: 120,
        height: 120,
        borderRadius: 60,
        borderWidth: 3,
        borderColor: '#FF6B35',
    },
    avatarPlaceholder: {
        width: 120,
        height: 120,
        borderRadius: 60,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 3,
        borderColor: '#FF6B35',
    },
    editOverlay: {
        position: 'absolute',
        bottom: 5,
        right: 5,
        width: 36,
        height: 36,
        borderRadius: 18,
        justifyContent: 'center',
        alignItems: 'center',
    },
    avatarButtonContainer: {
        flexDirection: 'row',
        gap: 12,
    },
    changeAvatarButton: {
        borderRadius: 20,
        paddingVertical: 8,
        paddingHorizontal: 16,
    },
    changeAvatarText: {
        fontSize: 16,
        fontWeight: '600',
    },
    removeAvatarButton: {
        borderRadius: 20,
        paddingVertical: 8,
        paddingHorizontal: 16,
    },
    removeAvatarText: {
        fontSize: 16,
        fontWeight: '600',
    },
    inputContainer: {
        borderRadius: 16,
        paddingHorizontal: 16,
    },
    input: {
        fontSize: 16,
        fontWeight: '400',
        paddingVertical: 16,
    },
    emailHint: {
        fontSize: 12,
        fontWeight: '400',
        marginTop: 8,
        fontStyle: 'italic',
    },
    saveButton: {
        borderRadius: 16,
        padding: 16,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 32,
        shadowColor: '#FF6B35',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 12,
        elevation: 8,
    },
    saveButtonText: {
        color: '#ffffff',
        fontSize: 16,
        fontWeight: '600',
    },
});