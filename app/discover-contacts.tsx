import SearchBar from '@/components/SearchBar';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { userService } from '@/services/userService';
import { User } from '@/types/messenger';
import * as Haptics from 'expo-haptics';
import { Image } from 'expo-image';
import { router, Stack } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    SafeAreaView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

export default function DiscoverContactsScreen() {
  const { user } = useAuth();
  const [contacts, setContacts] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Load user's contacts on mount
  useEffect(() => {
    loadContacts();
  }, [user]);

  const loadContacts = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const userContacts = await userService.getContacts();
      setContacts(userContacts);
    } catch (error) {
      console.error('Error loading contacts:', error);
      Alert.alert('Error', 'Failed to load contacts');
    } finally {
      setLoading(false);
    }
  };

  // Handle search functionality
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    // Search for new users if query is long enough
    if (searchQuery.length >= 2) {
      searchUsers();
    }
  }, [searchQuery, contacts]);

  const searchUsers = async () => {
    try {
      setSearchLoading(true);
      const results = await userService.searchUsers(searchQuery);
      // Filter out current user and existing contacts
      const newUsers = results.filter(u => 
        u.id !== user?.uid && 
        !contacts.some(c => c.id === u.id)
      );
      setSearchResults(newUsers);
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleAddContact = async (userId: string) => {
    try {
      setLoading(true);
      await userService.sendContactRequest(userId);
      Alert.alert('Success', 'Contact request sent!');
      // Refresh contacts and search results
      await loadContacts();
      if (searchQuery) {
        await searchUsers();
      }
    } catch (error) {
      console.error('Error sending contact request:', error);
      Alert.alert('Error', 'Failed to send contact request');
    } finally {
      setLoading(false);
    }
  };

  const renderSearchResultItem = ({ item }: { item: User }) => (
    <TouchableOpacity
      style={[styles.userItem, { backgroundColor: isDark ? '#1c1c1e' : '#fff' }]}
      onPress={() => router.push(`/user-profile?userId=${item.id}`)}
      disabled={loading}
    >
      <View style={styles.avatarContainer}>
        <Image
          source={{ uri: item.photoURL || item.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(item.name || item.displayName || 'User')}&background=random` }}
          style={styles.avatar}
        />
        {item.isOnline && <View style={styles.onlineIndicator} />}
      </View>
      
      <View style={styles.userContent}>
        <Text style={[styles.userName, { color: isDark ? '#fff' : '#000' }]} numberOfLines={1}>
          {item.name || item.displayName}
        </Text>
        <Text style={[styles.userStatus, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
          {item.email}
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: '#FF6B35' }]}
        onPress={() => handleAddContact(item.id)}
        disabled={loading}
      >
        <IconSymbol name="person.fill.badge.plus" size={16} color="#ffffff" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={[styles.container, { backgroundColor: isDark ? '#000' : '#f8f9fa' }]}>
        <SafeAreaView style={styles.safeArea}>
          {/* Modern Header */}
          <View style={[styles.header, { 
            backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
            shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 1,
            shadowRadius: 8,
            elevation: 4,
          }]}>
            <TouchableOpacity onPress={() => router.back()} style={[styles.cancelButton, {
              backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
            }]}>
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
            
            <Text style={[styles.headerTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
              Discover New Contacts
            </Text>
            
            <View style={styles.emptyButton} />
          </View>

          <View style={styles.searchSection}>
            <View style={[styles.searchWrapper, {
              backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(240, 240, 240, 0.8)',
              shadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 1,
              shadowRadius: 16,
              elevation: 4,
            }]}>
              <SearchBar
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Search for people to connect with"
                onClear={() => setSearchQuery('')}
              />
            </View>
          </View>

          {/* Loading State */}
          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#FF6B35" />
              <Text style={[styles.loadingText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
                Loading...
              </Text>
            </View>
          )}

          {/* Search Results Section */}
          {!loading && searchResults.length > 0 && (
            <>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { 
                  color: isDark ? '#8e8e93' : '#6c6c70',
                  fontWeight: '600',
                  letterSpacing: 0.5,
                }]}>
                  DISCOVER NEW PEOPLE
                  {searchLoading && (
                    <ActivityIndicator size="small" color="#FF6B35" style={{ marginLeft: 8 }} />
                  )}
                </Text>
              </View>

              <FlatList
                data={searchResults}
                renderItem={renderSearchResultItem}
                keyExtractor={(item) => item.id}
                style={styles.userList}
                contentContainerStyle={styles.userListContent}
                showsVerticalScrollIndicator={false}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
              />
            </>
          )}

          {/* No Results Message */}
          {!loading && searchResults.length === 0 && searchQuery.length === 0 && (
            <View style={styles.noResultsContainer}>
              <IconSymbol name="magnifyingglass" size={48} color={isDark ? '#8e8e93' : '#8e8e93'} />
              <Text style={[styles.noResultsText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
                Discover New People
              </Text>
              <Text style={[styles.noResultsSubtext, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
                Search for people to connect and start chatting
              </Text>
            </View>
          )}

          {/* No Search Results */}
          {!loading && searchQuery.length > 0 && searchResults.length === 0 && !searchLoading && (
            <View style={styles.noResultsContainer}>
              <IconSymbol name="person.crop.circle.badge.questionmark" size={48} color={isDark ? '#8e8e93' : '#8e8e93'} />
              <Text style={[styles.noResultsText, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
                No users found
              </Text>
              <Text style={[styles.noResultsSubtext, { color: isDark ? '#8e8e93' : '#8e8e93' }]}>
                Try a different search or check your spelling
              </Text>
            </View>
          )}
        </SafeAreaView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 0,
    backdropFilter: 'blur(20px)',
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  cancelText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF6B35',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  emptyButton: {
    width: 60, // Match the width of the cancel button area
  },
  searchSection: {
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 16,
  },
  searchWrapper: {
    borderRadius: 20,
    overflow: 'hidden',
    backdropFilter: 'blur(20px)',
  },
  sectionHeader: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  sectionTitle: {
    fontSize: 13,
  },
  userList: {
    flex: 1,
  },
  userListContent: {
    paddingHorizontal: 20,
  },
  separator: {
    height: 8,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    marginVertical: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#30d158',
    borderWidth: 2,
    borderColor: '#fff',
  },
  userContent: {
    flex: 1,
  },
  userName: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 4,
    letterSpacing: -0.2,
  },
  userStatus: {
    fontSize: 14,
    fontWeight: '400',
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 16,
    textAlign: 'center',
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  noResultsText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  noResultsSubtext: {
    fontSize: 14,
    fontWeight: '400',
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.7,
  },
});