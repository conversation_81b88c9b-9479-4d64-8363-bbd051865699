// Simple test script to verify Firebase Auth initialization
const { initializeApp } = require('firebase/app');
const { getAuth } = require('firebase/auth');

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID,
};

console.log('Initializing Firebase app...');
const app = initializeApp(firebaseConfig);
console.log('✅ Firebase app initialized');

console.log('Initializing Firebase Auth...');
try {
  const auth = getAuth(app);
  console.log('✅ Firebase Auth initialized successfully');
  console.log('Auth instance:', auth);
} catch (error) {
  console.error('❌ Failed to initialize Firebase Auth:', error);
}