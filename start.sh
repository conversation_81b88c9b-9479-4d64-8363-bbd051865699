#!/bin/bash

# <PERSON><PERSON><PERSON> Messenger - Complete Startup & Testing Script
# This script sets up and tests the entire application

echo \"🕉️  Namoshkar Messenger - Complete Setup & Test Script\"
echo \"================================================\"

# Colors for output
RED='\\033[0;31m'
GREEN='\\033[0;32m'
YELLOW='\\033[1;33m'
BLUE='\\033[0;34m'
NC='\\033[0m' # No Color

# Function to print status
print_status() {
    echo -e \"${BLUE}[INFO]${NC} $1\"
}

print_success() {
    echo -e \"${GREEN}[SUCCESS]${NC} $1\"
}

print_warning() {
    echo -e \"${YELLOW}[WARNING]${NC} $1\"
}

print_error() {
    echo -e \"${RED}[ERROR]${NC} $1\"
}

# Check if required tools are installed
check_requirements() {
    print_status \"Checking requirements...\"
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error \"Node.js is not installed. Please install Node.js 16+\"
        exit 1
    fi
    print_success \"Node.js $(node --version) found\"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error \"npm is not installed. Please install npm\"
        exit 1
    fi
    print_success \"npm $(npm --version) found\"
    
    # Check if .env exists
    if [ ! -f \".env\" ]; then
        print_error \".env file not found. Please create it with Firebase configuration\"
        exit 1
    fi
    print_success \".env file found\"
}

# Install dependencies
install_dependencies() {
    print_status \"Installing frontend dependencies...\"
    npm install
    if [ $? -ne 0 ]; then
        print_error \"Failed to install frontend dependencies\"
        exit 1
    fi
    print_success \"Frontend dependencies installed\"
    
    print_status \"Installing backend dependencies...\"
    cd backend
    npm install
    if [ $? -ne 0 ]; then
        print_error \"Failed to install backend dependencies\"
        exit 1
    fi
    print_success \"Backend dependencies installed\"
    cd ..
}

# Start services
start_services() {
    print_status \"Starting services in background...\"
    
    # Start WebSocket server
    print_status \"Starting WebSocket server on port 3002...\"
    cd backend
    node websocket-server.js > ../logs/websocket.log 2>&1 &
    WEBSOCKET_PID=$!
    cd ..
    echo $WEBSOCKET_PID > .websocket.pid
    sleep 3
    
    # Start backend API server
    print_status \"Starting Backend API server on port 3001...\"
    cd backend
    npm run dev > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    cd ..
    echo $BACKEND_PID > .backend.pid
    sleep 5
    
    # Start React Native development server
    print_status \"Starting React Native development server...\"
    npx expo start > logs/expo.log 2>&1 &
    EXPO_PID=$!
    echo $EXPO_PID > .expo.pid
    sleep 5
}

# Test services
test_services() {
    print_status \"Testing services...\"
    
    # Test WebSocket server
    if curl -s http://localhost:3002 > /dev/null; then
        print_success \"WebSocket server is running on port 3002\"
    else
        print_warning \"WebSocket server might not be fully started yet\"
    fi
    
    # Test backend API
    if curl -s http://localhost:3001 > /dev/null; then
        print_success \"Backend API server is running on port 3001\"
    else
        print_warning \"Backend API server might not be fully started yet\"
    fi
    
    # Test Expo server
    if curl -s http://localhost:8081 > /dev/null; then
        print_success \"Expo development server is running on port 8081\"
    else
        print_warning \"Expo development server might not be fully started yet\"
    fi
}

# Create logs directory
create_logs_dir() {
    if [ ! -d \"logs\" ]; then
        mkdir logs
        print_status \"Created logs directory\"
    fi
}

# Stop services function
stop_services() {
    print_status \"Stopping services...\"
    
    if [ -f \".websocket.pid\" ]; then
        kill $(cat .websocket.pid) 2>/dev/null
        rm .websocket.pid
        print_success \"WebSocket server stopped\"
    fi
    
    if [ -f \".backend.pid\" ]; then
        kill $(cat .backend.pid) 2>/dev/null
        rm .backend.pid
        print_success \"Backend API server stopped\"
    fi
    
    if [ -f \".expo.pid\" ]; then
        kill $(cat .expo.pid) 2>/dev/null
        rm .expo.pid
        print_success \"Expo development server stopped\"
    fi
}

# Function to show service status
show_status() {
    echo \"\"
    echo \"🚀 Service Status:\"
    echo \"=================\"
    
    if [ -f \".websocket.pid\" ] && kill -0 $(cat .websocket.pid) 2>/dev/null; then
        echo \"✅ WebSocket Server: Running (PID: $(cat .websocket.pid))\"
    else
        echo \"❌ WebSocket Server: Not running\"
    fi
    
    if [ -f \".backend.pid\" ] && kill -0 $(cat .backend.pid) 2>/dev/null; then
        echo \"✅ Backend API: Running (PID: $(cat .backend.pid))\"
    else
        echo \"❌ Backend API: Not running\"
    fi
    
    if [ -f \".expo.pid\" ] && kill -0 $(cat .expo.pid) 2>/dev/null; then
        echo \"✅ Expo Development Server: Running (PID: $(cat .expo.pid))\"
    else
        echo \"❌ Expo Development Server: Not running\"
    fi
    
    echo \"\"
    echo \"📱 Access Points:\"
    echo \"=================\"
    echo \"• WebSocket Server: http://localhost:3002\"
    echo \"• Backend API: http://localhost:3001\"
    echo \"• Admin Dashboard: http://localhost:3001\"
    echo \"• Expo Metro: http://localhost:8081\"
    echo \"\"
    echo \"📋 Quick Commands:\"
    echo \"==================\"
    echo \"• Stop all services: ./start.sh stop\"
    echo \"• View logs: tail -f logs/*.log\"
    echo \"• Test messaging: Open Expo Go app and scan QR code\"
    echo \"\"
}

# Show usage information
show_usage() {
    echo \"Usage: $0 [start|stop|restart|status|test]\"
    echo \"\"
    echo \"Commands:\"
    echo \"  start   - Start all services (default)\"
    echo \"  stop    - Stop all services\"
    echo \"  restart - Restart all services\"
    echo \"  status  - Show service status\"
    echo \"  test    - Run service tests\"
    echo \"\"
    echo \"Examples:\"
    echo \"  $0 start    # Start all services\"
    echo \"  $0 stop     # Stop all services\"
    echo \"  $0 status   # Check status\"
}

# Main script logic
case \"${1:-start}\" in
    \"start\")
        print_status \"Starting Namoshkar Messenger...\"
        check_requirements
        create_logs_dir
        install_dependencies
        start_services
        test_services
        show_status
        print_success \"🕉️ Namoshkar Messenger is now running!\"
        print_status \"Use 'tail -f logs/*.log' to monitor service logs\"
        print_status \"Use './start.sh stop' to stop all services\"
        ;;
    \"stop\")
        stop_services
        print_success \"All services stopped\"
        ;;
    \"restart\")
        stop_services
        sleep 2
        create_logs_dir
        start_services
        test_services
        show_status
        print_success \"🕉️ Namoshkar Messenger restarted!\"
        ;;
    \"status\")
        show_status
        ;;
    \"test\")
        test_services
        ;;
    \"help\"|\"--help\"|\"-h\")
        show_usage
        ;;
    *)
        print_error \"Unknown command: $1\"
        show_usage
        exit 1
        ;;
esac"