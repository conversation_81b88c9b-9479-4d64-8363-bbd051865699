import { Platform } from 'react-native';
import { initializeApp, getApp, FirebaseApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID,
};

// Initialize Firebase app
let app: FirebaseApp;
try {
  app = getApp();
  console.log('✅ Firebase app already initialized');
} catch {
  app = initializeApp(firebaseConfig);
  console.log('✅ Firebase app initialized');
}

// Export Firestore and Storage instances
export const db = getFirestore(app);
export const storage = getStorage(app);
export { app };

// Firebase Auth initialization with proper persistence
let authInstance: any = null;
let authInitializationPromise: Promise<any> | null = null;

export async function getAuth() {
  // Return existing instance if already initialized
  if (authInstance) {
    return authInstance;
  }
  
  // If initialization is in progress, return the same promise
  if (authInitializationPromise) {
    return authInitializationPromise;
  }
  
  // Start initialization
  authInitializationPromise = initializeAuthWithPersistence();
  authInstance = await authInitializationPromise;
  return authInstance;
}

async function initializeAuthWithPersistence() {
  try {
    console.log('⏳ Initializing Firebase Auth with persistence...');
    
    if (Platform.OS === 'web') {
      // For web, import and use browser persistence
      const { getAuth, browserLocalPersistence, setPersistence } = await import('firebase/auth');
      const auth = getAuth(app);
      await setPersistence(auth, browserLocalPersistence);
      console.log('✅ Firebase Auth persistence set to browserLocalPersistence for web');
      console.log('✅ Firebase Auth initialized successfully with persistence');
      return auth;
    } else {
      // For React Native, try to use AsyncStorage persistence
      try {
        const AsyncStorage = (await import('@react-native-async-storage/async-storage')).default;
        
        // Use require for React Native specific imports to avoid TypeScript issues
        const { initializeAuth } = await import('firebase/auth');
        const reactNativeAuth = require('firebase/auth');
        
        // Initialize auth with React Native persistence
        const auth = initializeAuth(app, {
          persistence: reactNativeAuth.getReactNativePersistence(AsyncStorage)
        });
        
        console.log('✅ Firebase Auth persistence set to React Native AsyncStorage');
        console.log('✅ Firebase Auth initialized successfully with persistence');
        return auth;
      } catch (error) {
        console.warn('⚠️  Failed to set up React Native persistence, falling back to default auth initialization:', error);
        // Fallback to basic initialization
        const { getAuth } = await import('firebase/auth');
        const auth = getAuth(app);
        console.log('✅ Firebase Auth initialized without persistence');
        return auth;
      }
    }
  } catch (error) {
    console.error('❌ Failed to initialize Firebase Auth with persistence:', error);
    return null;
  }
}