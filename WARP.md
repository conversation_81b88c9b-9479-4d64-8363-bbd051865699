# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Project Overview
Namoshkar Messenger is a React Native/Expo messaging app with a Next.js backend, using Firebase for authentication, database (Firestore), and storage. It's designed as a sacred messaging platform for the Hindu community.

## Architecture

### Frontend (React Native + Expo)
- **Navigation**: Expo Router with file-based routing
- **State Management**: React Context API (AuthContext)
- **Real-time**: WebSocket via Socket.IO for instant messaging
- **UI Components**: Custom components with TypeScript

### Backend (Next.js + Socket.IO)
- **API Server**: Next.js on port 3001 (`backend/` directory)
- **WebSocket Server**: Socket.IO on port 3002
- **Authentication**: Firebase Admin SDK with JWT tokens
- **Database**: Firebase Firestore for persistent storage

### Key Services
- `authService.ts`: Firebase authentication with persistence
- `chatService.ts`: Chat management and real-time updates
- `websocketService.ts`: WebSocket connection management
- `messagingService.ts`: Unified messaging interface
- `notificationService.ts`: Push notifications via FCM

## Common Commands

### Quick Start (All Services)
```bash
# Start everything with one command
./start.sh start

# Stop all services
./start.sh stop

# Check service status
./start.sh status

# Test connections
./start.sh test
```

### Manual Development Commands
```bash
# Install dependencies
npm install
cd backend && npm install && cd ..

# Start WebSocket server (Terminal 1)
cd backend && node websocket-server.js

# Start Backend API (Terminal 2)
cd backend && npm run dev

# Start React Native (Terminal 3)
npm start

# Run on iOS
npm run ios

# Run on Android
npm run android

# Lint code
npm run lint
```

### Testing
```bash
# Run Playwright tests
npx playwright test

# Test authentication persistence
node test-auth-persistence.js

# Test Firebase connection
node test-firebase.js
```

## Authentication Flow & Persistence

### Current Implementation
The app uses Firebase Authentication with proper persistence configured for both web and React Native:

1. **Web**: Uses `browserLocalPersistence`
2. **React Native**: Uses `AsyncStorage` via `@react-native-async-storage/async-storage`

### Authentication State Management
The `AuthContext` handles:
- Initial auth state restoration on app launch
- Real-time auth state changes
- User profile fetching from Firestore
- Profile completion tracking (displayName + photoURL)

### Known Issue: Expo Go Authentication Persistence
**Problem**: Authentication doesn't persist on app reload in Expo Go
**Solution Applied**: The AuthContext now properly waits for Firebase Auth to restore the persisted session before checking the current user

## Critical Files & Their Purposes

### Authentication & User Management
- `config/firebase.ts`: Firebase initialization with platform-specific persistence
- `contexts/AuthContext.tsx`: Global auth state management
- `services/authService.ts`: Authentication operations

### Navigation & Routing
- `app/_layout.tsx`: Root layout with AuthProvider
- `app/(tabs)/_layout.tsx`: Tab navigation with auth guards
- `app/sign-in.tsx`, `app/sign-up.tsx`: Authentication screens
- `app/profile-setup.tsx`: Profile completion screen

### Real-time Communication
- `backend/websocket-server.ts`: Socket.IO server implementation
- `services/websocketService.ts`: Client-side WebSocket management
- `services/messagingService.ts`: Message handling abstraction

### Database Schema (Firestore)
Collections:
- `users`: User profiles with searchTokens
- `chats`: Chat metadata and participants
- `messages`: Individual messages with reactions
- `contacts`: User contacts relationships
- `contactRequests`: Pending contact requests

## Environment Setup

Required `.env` variables:
```env
EXPO_PUBLIC_FIREBASE_API_KEY=
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=
EXPO_PUBLIC_FIREBASE_PROJECT_ID=
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=
EXPO_PUBLIC_FIREBASE_APP_ID=
NEXT_PUBLIC_API_URL=http://localhost:3001
WEBSOCKET_URL=http://localhost:3002
```

Backend requires `serviceAccountKey.json` in `backend/` directory.

## Development Guidelines

### Type Safety
- All interfaces defined in `types/messenger.ts`
- Backward compatibility maintained with legacy properties
- Nullable properties properly typed (e.g., `photoURL: string | null`)

### Real-time Features
- WebSocket events follow consistent naming: `join-chat`, `send-message`, `typing-start`
- Server broadcasts to relevant users only
- Offline message queuing via Firebase

### Authentication Flow
1. User signs up → Create Firebase Auth account → Create Firestore profile
2. User signs in → Verify credentials → Load profile → Check completion
3. Profile incomplete → Redirect to profile-setup
4. Profile complete → Access main app tabs

### API Authentication
All API calls require Firebase ID token:
```javascript
headers: { 'Authorization': `Bearer ${idToken}` }
```

## Troubleshooting

### Auth Not Persisting in Expo Go
- Ensure `@react-native-async-storage/async-storage` is installed
- Check that `AuthContext` properly waits for auth restoration
- Verify Firebase persistence configuration in `config/firebase.ts`

### WebSocket Connection Issues
- Verify WebSocket server running on port 3002
- Check WEBSOCKET_URL in environment
- Ensure proper CORS configuration

### Firestore Permission Errors
- Check Firebase security rules
- Verify service account key is present
- Ensure user is authenticated before queries

## Project Status
- ✅ Authentication system with persistence
- ✅ Real-time messaging with WebSocket
- ✅ Contact management system
- ✅ Group chat functionality
- ✅ Push notifications setup
- ✅ Admin dashboard
- ✅ Cross-platform support (iOS/Android/Web)

## Next Steps
Based on `COMPLETION_SUMMARY.md`, all core features are implemented. Future enhancements could include:
- Voice/video calling with WebRTC
- Message encryption
- File sharing improvements
- Message search functionality
