'use client';

import { useEffect, useState } from 'react';

// Declare alert for browser environment
declare const alert: (message: string) => void;

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalChats: number;
  totalMessages: number;
  onlineUsers: number;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalChats: 0,
    totalMessages: 0,
    onlineUsers: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      // In a real implementation, you would fetch these from your API
      // For now, we'll simulate the data
      setTimeout(() => {
        setStats({
          totalUsers: 150,
          activeUsers: 45,
          totalChats: 89,
          totalMessages: 2847,
          onlineUsers: 12,
        });
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-orange-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-orange-700">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-orange-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-orange-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="text-3xl mr-3">🙏</div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Namoshkar Messenger</h1>
                <p className="text-sm text-orange-600">Admin Dashboard</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-600">System Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <StatCard
            title="Total Users"
            value={stats.totalUsers.toLocaleString()}
            icon="👥"
            color="blue"
          />
          <StatCard
            title="Active Users"
            value={stats.activeUsers.toLocaleString()}
            subtitle="Last 7 days"
            icon="🟢"
            color="green"
          />
          <StatCard
            title="Online Now"
            value={stats.onlineUsers.toLocaleString()}
            icon="⚡"
            color="yellow"
          />
          <StatCard
            title="Total Chats"
            value={stats.totalChats.toLocaleString()}
            icon="💬"
            color="purple"
          />
          <StatCard
            title="Messages Sent"
            value={stats.totalMessages.toLocaleString()}
            subtitle="All time"
            icon="📨"
            color="orange"
          />
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* System Status */}
          <div className="bg-white rounded-xl shadow-md border border-orange-200 p-6">
            <div className="flex items-center mb-4">
              <div className="text-2xl mr-3">🛠️</div>
              <h2 className="text-xl font-semibold text-gray-900">System Status</h2>
            </div>
            <div className="space-y-3">
              <StatusItem label="WebSocket Server" status="online" />
              <StatusItem label="Firebase Database" status="online" />
              <StatusItem label="Push Notifications" status="online" />
              <StatusItem label="File Storage" status="online" />
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-xl shadow-md border border-orange-200 p-6">
            <div className="flex items-center mb-4">
              <div className="text-2xl mr-3">📈</div>
              <h2 className="text-xl font-semibold text-gray-900">Recent Activity</h2>
            </div>
            <div className="space-y-3">
              <ActivityItem 
                action="New user registered" 
                details="<EMAIL>" 
                time="2 minutes ago" 
              />
              <ActivityItem 
                action="Group chat created" 
                details="Spiritual Discussions (5 members)" 
                time="15 minutes ago" 
              />
              <ActivityItem 
                action="Message sent" 
                details="450 messages in the last hour" 
                time="1 hour ago" 
              />
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <div className="bg-white rounded-xl shadow-md border border-orange-200 p-6">
            <div className="flex items-center mb-6">
              <div className="text-2xl mr-3">⚡</div>
              <h2 className="text-xl font-semibold text-gray-900">Quick Actions</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <ActionButton 
                title="Broadcast Message"
                description="Send announcement to all users"
                icon="📢"
                onClick={() => alert('Feature coming soon!')}
              />
              <ActionButton 
                title="View Reports"
                description="Generate usage analytics"
                icon="📉"
                onClick={() => alert('Feature coming soon!')}
              />
              <ActionButton 
                title="Manage Users"
                description="User administration panel"
                icon="👤"
                onClick={() => alert('Feature coming soon!')}
              />
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-12 text-center text-gray-500">
          <p className="text-sm">
            🙏 Namoshkar Messenger Admin Dashboard - Spreading spiritual connections with technology
          </p>
          <p className="text-xs mt-2">
            Version 1.0.0 | Built with Next.js, Firebase & Socket.IO
          </p>
        </div>
      </div>
    </div>
  );
}

// Stat Card Component
function StatCard({ 
  title, 
  value, 
  subtitle, 
  icon, 
  color 
}: { 
  title: string;
  value: string;
  subtitle?: string;
  icon: string;
  color: string;
}) {
  const colorClasses = {
    blue: 'from-blue-500 to-blue-600',
    green: 'from-green-500 to-green-600',
    yellow: 'from-yellow-500 to-yellow-600',
    purple: 'from-purple-500 to-purple-600',
    orange: 'from-orange-500 to-orange-600',
  };

  return (
    <div className="bg-white rounded-xl shadow-md border border-orange-200 p-6 hover:shadow-lg transition-shadow">
      <div className="flex items-center justify-between mb-2">
        <div className="text-2xl">{icon}</div>
        <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${colorClasses[color as keyof typeof colorClasses]} flex items-center justify-center`}>
          <div className="w-6 h-6 bg-white rounded opacity-20"></div>
        </div>
      </div>
      <div className="text-2xl font-bold text-gray-900 mb-1">{value}</div>
      <div className="text-sm font-medium text-gray-600">{title}</div>
      {subtitle && <div className="text-xs text-gray-500 mt-1">{subtitle}</div>}
    </div>
  );
}

// Status Item Component
function StatusItem({ label, status }: { label: string; status: 'online' | 'offline' }) {
  return (
    <div className="flex items-center justify-between py-2">
      <span className="text-sm text-gray-600">{label}</span>
      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${
          status === 'online' ? 'bg-green-500 animate-pulse' : 'bg-red-500'
        }`}></div>
        <span className={`text-xs font-medium ${
          status === 'online' ? 'text-green-600' : 'text-red-600'
        }`}>
          {status === 'online' ? 'Online' : 'Offline'}
        </span>
      </div>
    </div>
  );
}

// Activity Item Component
function ActivityItem({ action, details, time }: { action: string; details: string; time: string }) {
  return (
    <div className="flex items-start space-x-3 py-2">
      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
      <div className="flex-1">
        <p className="text-sm font-medium text-gray-900">{action}</p>
        <p className="text-xs text-gray-500">{details}</p>
      </div>
      <span className="text-xs text-gray-400">{time}</span>
    </div>
  );
}

// Action Button Component
function ActionButton({ 
  title, 
  description, 
  icon, 
  onClick 
}: { 
  title: string;
  description: string;
  icon: string;
  onClick: () => void;
}) {
  return (
    <button
      onClick={onClick}
      className="p-4 border border-orange-200 rounded-lg hover:bg-orange-50 transition-colors text-left group"
    >
      <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">{icon}</div>
      <div className="font-semibold text-gray-900 mb-1">{title}</div>
      <div className="text-sm text-gray-600">{description}</div>
    </button>
  );
}
