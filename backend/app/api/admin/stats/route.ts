import { AuthenticatedRequest, createAuthMiddleware } from '../../../../lib/auth-middleware';
import { adminDb } from '../../../../lib/firebase-admin';

// GET - Get admin dashboard statistics
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function getAdminStats(_req: AuthenticatedRequest) {
  try {
    // In a real app, you would check if user has admin privileges
    // For now, we'll allow any authenticated user to view stats
    
    const stats = {
      totalUsers: 0,
      activeUsers: 0,
      onlineUsers: 0,
      totalChats: 0,
      totalMessages: 0,
      groupChats: 0,
      directChats: 0,
      messagesLast24h: 0,
      newUsersLast7d: 0,
    };

    // Get total users count
    const usersSnapshot = await adminDb.collection('users').count().get();
    stats.totalUsers = usersSnapshot.data().count;

    // Get online users count
    const onlineUsersSnapshot = await adminDb
      .collection('users')
      .where('isOnline', '==', true)
      .count()
      .get();
    stats.onlineUsers = onlineUsersSnapshot.data().count;

    // Get active users (users who were online in last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const activeUsersSnapshot = await adminDb
      .collection('users')
      .where('lastSeen', '>=', sevenDaysAgo)
      .count()
      .get();
    stats.activeUsers = activeUsersSnapshot.data().count;

    // Get new users in last 7 days
    const newUsersSnapshot = await adminDb
      .collection('users')
      .where('createdAt', '>=', sevenDaysAgo)
      .count()
      .get();
    stats.newUsersLast7d = newUsersSnapshot.data().count;

    // Get total chats count
    const chatsSnapshot = await adminDb.collection('chats').count().get();
    stats.totalChats = chatsSnapshot.data().count;

    // Get group chats count
    const groupChatsSnapshot = await adminDb
      .collection('chats')
      .where('type', '==', 'group')
      .count()
      .get();
    stats.groupChats = groupChatsSnapshot.data().count;
    stats.directChats = stats.totalChats - stats.groupChats;

    // Get total messages count
    const messagesSnapshot = await adminDb.collection('messages').count().get();
    stats.totalMessages = messagesSnapshot.data().count;

    // Get messages in last 24 hours
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);
    
    const recentMessagesSnapshot = await adminDb
      .collection('messages')
      .where('timestamp', '>=', oneDayAgo)
      .count()
      .get();
    stats.messagesLast24h = recentMessagesSnapshot.data().count;

    return new Response(
      JSON.stringify({ stats }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error getting admin stats:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export const GET = createAuthMiddleware(getAdminStats);