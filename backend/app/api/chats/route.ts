import { AuthenticatedRequest, createAuthMiddleware } from '../../../lib/auth-middleware';
import { adminDb } from '../../../lib/firebase-admin';

// Firebase Admin SDK for notifications
import * as admin from 'firebase-admin';

// GET - Get user's chats (both direct and group)
async function getUserChats(req: AuthenticatedRequest) {
  try {
    const userId = req.user!.uid;
    
    // Get all chats where user is a participant
    const chatsQuery = adminDb
      .collection('chats')
      .where('participants', 'array-contains', userId)
      .orderBy('lastActivity', 'desc');

    const snapshot = await chatsQuery.get();
    const chats = [];

    for (const doc of snapshot.docs) {
      const data = doc.data();
      
      // Format chat data
      const chat = {
        id: doc.id,
        participants: data.participants,
        participantDetails: data.participantDetails,
        type: data.type,
        name: data.name,
        description: data.description,
        photoURL: data.photoURL,
        createdBy: data.createdBy,
        createdAt: data.createdAt.toDate(),
        lastMessage: data.lastMessage ? {
          text: data.lastMessage.text,
          senderId: data.lastMessage.senderId,
          timestamp: data.lastMessage.timestamp.toDate(),
          type: data.lastMessage.type
        } : null,
        lastActivity: data.lastActivity.toDate(),
        unreadCount: data.unreadCount || {},
      };

      chats.push(chat);
    }

    return new Response(
      JSON.stringify({ chats }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error getting user chats:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// POST - Create a new chat (direct or group)
async function createChat(req: AuthenticatedRequest) {
  try {
    const userId = req.user!.uid;
    const body = await req.json();
    const { participantIds, name, description, photoURL, type = 'direct' } = body as {
      participantIds: string[];
      name?: string;
      description?: string;
      photoURL?: string;
      type?: 'direct' | 'group';
    };

    // Validate input
    if (type === 'group' && (!name || !name.trim())) {
      return new Response(
        JSON.stringify({ error: 'Group name is required for group chats' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (!participantIds || !Array.isArray(participantIds) || participantIds.length === 0) {
      return new Response(
        JSON.stringify({ error: 'Participant IDs are required' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Add creator to participants if not already included
    const allParticipants = [...new Set([userId, ...participantIds])];

    // For direct chats, check if a chat already exists between the two users
    if (type === 'direct' && allParticipants.length === 2) {
      const otherParticipantId = allParticipants.find(id => id !== userId)!;
      const existingChatQuery = adminDb
        .collection('chats')
        .where('type', '==', 'direct')
        .where('participants', 'array-contains', userId);

      const existingChats = await existingChatQuery.get();
      
      for (const doc of existingChats.docs) {
        const chatData = doc.data();
        if (chatData.participants.includes(otherParticipantId) && chatData.participants.length === 2) {
          return new Response(
            JSON.stringify({ 
              chatId: doc.id,
              message: 'Direct chat already exists'
            }),
            { 
              status: 200, // Use 200 OK as we are returning an existing resource
              headers: { 'Content-Type': 'application/json' }
            }
          );
        }
      }
    }

    // Get participant details for the chat object
    const participantDetails: { [userId: string]: { name: string; email: string; photoURL?: string } } = {};
    for (const pId of allParticipants) {
      const userDoc = await adminDb.collection('users').doc(pId).get();
      if (userDoc.exists) {
        const userData = userDoc.data()!;
        participantDetails[pId] = {
          name: userData.displayName || userData.email,
          email: userData.email,
          photoURL: userData.photoURL || null,
        };
      }
    }

    // Create chat data object
    const chatData = {
      participants: allParticipants,
      participantDetails,
      type,
      name: type === 'group' ? name!.trim() : null,
      description: description?.trim() || null,
      photoURL: photoURL || null,
      createdBy: userId,
      createdAt: new Date(),
      lastMessage: null,
      lastActivity: new Date(),
      unreadCount: allParticipants.reduce((acc, id) => ({ ...acc, [id]: 0 }), {}),
    };

    // Create the chat document in Firestore
    const chatRef = await adminDb.collection('chats').add(chatData);

    // Send notifications to participants for group chats
    if (type === 'group') {
      const creatorName = participantDetails[userId]?.name || 'Someone';
      
      for (const participantId of allParticipants) {
        // Skip notification for the creator
        if (participantId === userId) continue;

        try {
          // Create notification for group addition
          await adminDb.collection('notifications').add({
            recipientId: participantId,
            senderId: userId,
            title: 'Added to Group',
            body: `${creatorName} added you to the group "${name}"`,
            data: {
              type: 'group_added',
              chatId: chatRef.id,
              groupName: name,
              groupId: chatRef.id,
            },
            imageUrl: photoURL || null,
            chatId: chatRef.id,
            sentAt: new Date(),
            delivered: false,
            read: false,
          });

          console.log(`🔔 Group addition notification created for user ${participantId}`);
        } catch (error) {
          console.error(`Error creating group notification for user ${participantId}:`, error);
        }
      }
    }

    return new Response(
      JSON.stringify({ 
        chatId: chatRef.id,
        message: type === 'group' ? 'Group chat created successfully' : 'Direct chat created successfully'
      }),
      { 
        status: 201,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error creating chat:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export const GET = createAuthMiddleware(getUserChats);
export const POST = createAuthMiddleware(createChat);