import { AuthenticatedRequest, createAuthMiddlewareWithParams } from '../../../../lib/auth-middleware';
import { adminDb } from '../../../../lib/firebase-admin';

interface RouteParams {
  params: {
    chatId: string;
  };
}

// GET - Get specific chat details
async function getChatDetails(req: AuthenticatedRequest, { params }: RouteParams) {
  try {
    const userId = req.user!.uid;
    const { chatId } = params;

    const chatDoc = await adminDb.collection('chats').doc(chatId).get();
    
    if (!chatDoc.exists) {
      return new Response(
        JSON.stringify({ error: 'Chat not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const chatData = chatDoc.data()!;
    
    // Check if user is a participant
    if (!chatData.participants.includes(userId)) {
      return new Response(
        JSON.stringify({ error: 'Access denied' }),
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const chat = {
      id: chatDoc.id,
      participants: chatData.participants,
      participantDetails: chatData.participantDetails,
      type: chatData.type,
      name: chatData.name,
      description: chatData.description,
      photoURL: chatData.photoURL,
      createdBy: chatData.createdBy,
      createdAt: chatData.createdAt.toDate(),
      lastMessage: chatData.lastMessage ? {
        text: chatData.lastMessage.text,
        senderId: chatData.lastMessage.senderId,
        timestamp: chatData.lastMessage.timestamp.toDate(),
        type: chatData.lastMessage.type
      } : null,
      lastActivity: chatData.lastActivity.toDate(),
      unreadCount: chatData.unreadCount || {},
    };

    return new Response(
      JSON.stringify({ chat }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error getting chat details:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// PUT - Update chat details (name, description, add/remove members)
async function updateChat(req: AuthenticatedRequest, { params }: RouteParams) {
  try {
    const userId = req.user!.uid;
    const { chatId } = params;
    const body = await req.json();
    const { name, description, photoURL, action, participantId } = body as {
      name?: string;
      description?: string;
      photoURL?: string;
      action?: string;
      participantId?: string;
    };

    const chatRef = adminDb.collection('chats').doc(chatId);
    const chatDoc = await chatRef.get();
    
    if (!chatDoc.exists) {
      return new Response(
        JSON.stringify({ error: 'Chat not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const chatData = chatDoc.data()!;
    
    // Check if user is a participant
    if (!chatData.participants.includes(userId)) {
      return new Response(
        JSON.stringify({ error: 'Access denied' }),
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Handle different actions
    if (action === 'add-member') {
      if (!participantId) {
        return new Response(
          JSON.stringify({ error: 'Participant ID is required' }),
          { 
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      if (chatData.participants.includes(participantId)) {
        return new Response(
          JSON.stringify({ error: 'User is already a member' }),
          { 
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      // Get new participant details
      const userDoc = await adminDb.collection('users').doc(participantId).get();
      if (!userDoc.exists) {
        return new Response(
          JSON.stringify({ error: 'User not found' }),
          { 
            status: 404,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      const userData = userDoc.data()!;
      const newParticipantDetails = {
        ...chatData.participantDetails,
        [participantId]: {
          name: userData.displayName || userData.email,
          email: userData.email,
          photoURL: userData.photoURL,
        }
      };

      const newUnreadCount = {
        ...chatData.unreadCount,
        [participantId]: 0
      };

      await chatRef.update({
        participants: [...chatData.participants, participantId],
        participantDetails: newParticipantDetails,
        unreadCount: newUnreadCount,
        lastActivity: new Date()
      });

      return new Response(
        JSON.stringify({ message: 'Member added successfully' }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );

    } else if (action === 'remove-member') {
      if (!participantId) {
        return new Response(
          JSON.stringify({ error: 'Participant ID is required' }),
          { 
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      if (!chatData.participants.includes(participantId)) {
        return new Response(
          JSON.stringify({ error: 'User is not a member' }),
          { 
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      // Don't allow removing the last member or creator (you can modify this logic)
      if (chatData.participants.length <= 2) {
        return new Response(
          JSON.stringify({ error: 'Cannot remove member from a chat with only 2 participants' }),
          { 
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      const newParticipants = chatData.participants.filter((id: string) => id !== participantId);
      const newParticipantDetails = { ...chatData.participantDetails };
      const newUnreadCount = { ...chatData.unreadCount };
      
      delete newParticipantDetails[participantId];
      delete newUnreadCount[participantId];

      await chatRef.update({
        participants: newParticipants,
        participantDetails: newParticipantDetails,
        unreadCount: newUnreadCount,
        lastActivity: new Date()
      });

      return new Response(
        JSON.stringify({ message: 'Member removed successfully' }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );

    } else {
      // Regular update (name, description, photoURL)
      const updateData: { [key: string]: string | Date | null } = {
        lastActivity: new Date()
      };

      if (name !== undefined) updateData.name = name.trim();
      if (description !== undefined) updateData.description = description.trim() || null;
      if (photoURL !== undefined) updateData.photoURL = photoURL || null; // Ensure null instead of undefined

      await chatRef.update(updateData);

      return new Response(
        JSON.stringify({ message: 'Chat updated successfully' }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

  } catch (error) {
    console.error('Error updating chat:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// DELETE - Delete chat (only for group creator or leave chat)
async function deleteChat(req: AuthenticatedRequest, { params }: RouteParams) {
  try {
    const userId = req.user!.uid;
    const { chatId } = params;

    const chatRef = adminDb.collection('chats').doc(chatId);
    const chatDoc = await chatRef.get();
    
    if (!chatDoc.exists) {
      return new Response(
        JSON.stringify({ error: 'Chat not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const chatData = chatDoc.data()!;
    
    // Check if user is a participant
    if (!chatData.participants.includes(userId)) {
      return new Response(
        JSON.stringify({ error: 'Access denied' }),
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (chatData.type === 'direct' || chatData.participants.length <= 2) {
      // For direct chats or groups with 2 people, delete the entire chat
      // Also delete all messages in this chat
      const messagesQuery = adminDb.collection('messages').where('chatId', '==', chatId);
      const messagesSnapshot = await messagesQuery.get();
      
      const batch = adminDb.batch();
      messagesSnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });
      batch.delete(chatRef);
      
      await batch.commit();
      
      return new Response(
        JSON.stringify({ message: 'Chat deleted successfully' }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    } else {
      // For group chats, just remove the user
      const newParticipants = chatData.participants.filter((id: string) => id !== userId);
      const newParticipantDetails = { ...chatData.participantDetails };
      const newUnreadCount = { ...chatData.unreadCount };
      
      delete newParticipantDetails[userId];
      delete newUnreadCount[userId];

      await chatRef.update({
        participants: newParticipants,
        participantDetails: newParticipantDetails,
        unreadCount: newUnreadCount,
        lastActivity: new Date()
      });

      return new Response(
        JSON.stringify({ message: 'Left chat successfully' }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

  } catch (error) {
    console.error('Error deleting chat:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export const GET = createAuthMiddlewareWithParams(getChatDetails);
export const PUT = createAuthMiddlewareWithParams(updateChat);
export const DELETE = createAuthMiddlewareWithParams(deleteChat);


