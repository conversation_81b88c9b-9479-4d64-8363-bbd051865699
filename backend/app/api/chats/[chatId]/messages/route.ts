import { AuthenticatedRequest, createAuthMiddlewareWithParams } from '../../../../../lib/auth-middleware';
import { adminDb } from '../../../../../lib/firebase-admin';

// Define the shape of a message object for type safety
interface Message {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  senderEmail: string;
  text: string;
  type: string;
  timestamp: Date;
  replyTo?: string | null;
  reactions: Record<string, string[]>; // Maps reaction emoji to array of user IDs
  edited: boolean;
  editedAt?: Date | null;
  imageUrl?: string | null;
  fileUrl?: string | null;
  fileName?: string | null;
  fileSize?: number | null;
}

interface RouteParams {
  params: {
    chatId: string;
  };
}

// GET - Get messages for a specific chat
async function getChatMessages(req: AuthenticatedRequest, { params }: RouteParams) {
  try {
    const userId = req.user!.uid;
    const { chatId } = params;
    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const before = searchParams.get('before'); // timestamp for pagination

    // First, verify user has access to this chat
    const chatDoc = await adminDb.collection('chats').doc(chatId).get();
    if (!chatDoc.exists) {
      return new Response(
        JSON.stringify({ error: 'Chat not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const chatData = chatDoc.data()!;
    if (!chatData.participants.includes(userId)) {
      return new Response(
        JSON.stringify({ error: 'Access denied' }),
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Build messages query
    let messagesQuery = adminDb
      .collection('messages')
      .where('chatId', '==', chatId)
      .orderBy('timestamp', 'desc')
      .limit(limit);

    // Add pagination if before timestamp is provided
    if (before) {
      const beforeDate = new Date(before);
      messagesQuery = messagesQuery.where('timestamp', '<', beforeDate);
    }

    const messagesSnapshot = await messagesQuery.get();
    const messages: Message[] = [];

    messagesSnapshot.forEach((doc) => {
      const data = doc.data();
      messages.push({
        id: doc.id,
        chatId: data.chatId,
        senderId: data.senderId,
        senderName: data.senderName,
        senderEmail: data.senderEmail,
        text: data.text,
        type: data.type,
        timestamp: data.timestamp.toDate(),
        replyTo: data.replyTo,
        reactions: data.reactions || {},
        edited: data.edited || false,
        editedAt: data.editedAt?.toDate() || null,
        imageUrl: data.imageUrl,
        fileUrl: data.fileUrl,
        fileName: data.fileName,
        fileSize: data.fileSize
      });
    });

    // Reverse to get chronological order (oldest first)
    messages.reverse();

    return new Response(
      JSON.stringify({ 
        messages,
        hasMore: messagesSnapshot.size === limit,
        nextBefore: messages.length > 0 ? messages[0].timestamp : null
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error getting chat messages:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// POST - Send a message to a chat
async function sendMessage(req: AuthenticatedRequest, { params }: RouteParams) {
  try {
    const userId = req.user!.uid;
    const { chatId } = params;
    const body = await req.json();
    const { text, type = 'text', replyTo, imageUrl, fileUrl, fileName, fileSize } = body as {
      text: string;
      type?: string;
      replyTo?: string;
      imageUrl?: string;
      fileUrl?: string;
      fileName?: string;
      fileSize?: number;
    };

    if (!text || !text.trim()) {
      return new Response(
        JSON.stringify({ error: 'Message text is required' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Verify user has access to this chat
    const chatDoc = await adminDb.collection('chats').doc(chatId).get();
    if (!chatDoc.exists) {
      return new Response(
        JSON.stringify({ error: 'Chat not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const chatData = chatDoc.data()!;
    if (!chatData.participants.includes(userId)) {
      return new Response(
        JSON.stringify({ error: 'Access denied' }),
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get sender details
    const userDoc = await adminDb.collection('users').doc(userId).get();
    const userData = userDoc.exists ? userDoc.data()! : { displayName: 'Unknown User', email: '' };

    const timestamp = new Date();

    // Create message document
    const messageData = {
      chatId,
      senderId: userId,
      senderName: userData.displayName || userData.email || 'Unknown User',
      senderEmail: userData.email || '',
      text: text.trim(),
      type,
      timestamp,
      replyTo: replyTo || null,
      reactions: {},
      edited: false,
      editedAt: null,
      imageUrl: imageUrl || null,
      fileUrl: fileUrl || null,
      fileName: fileName || null,
      fileSize: fileSize || null
    };

    // Save message
    const messageRef = await adminDb.collection('messages').add(messageData);

    // Update chat's last message and activity
    await adminDb.collection('chats').doc(chatId).update({
      lastMessage: {
        text: text.trim(),
        senderId: userId,
        timestamp,
        type
      },
      lastActivity: timestamp,
      // Increment unread count for all participants except sender
      ...chatData.participants.reduce((acc: Record<string, number>, participantId: string) => {
        if (participantId !== userId) {
          const currentUnreadCount = chatData.unreadCount?.[participantId] || 0;
          acc[`unreadCount.${participantId}`] = currentUnreadCount + 1;
        }
        return acc;
      }, {} as Record<string, number>)
    });

    // Return the created message
    const createdMessage = {
      id: messageRef.id,
      ...messageData,
    };

    return new Response(
      JSON.stringify({ 
        message: createdMessage,
        success: true
      }),
      { 
        status: 201,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error sending message:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export const GET = createAuthMiddlewareWithParams(getChatMessages);
export const POST = createAuthMiddlewareWithParams(sendMessage);


