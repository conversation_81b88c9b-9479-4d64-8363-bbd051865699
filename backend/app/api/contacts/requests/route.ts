import { AuthenticatedRequest, createAuthMiddleware } from '../../../../lib/auth-middleware';
import { adminDb } from '../../../../lib/firebase-admin';

// GET - Get all contact requests for the authenticated user
async function getContactRequests(req: AuthenticatedRequest) {
  try {
    const userId = req.user!.uid;
    
    // Get incoming requests
    const incomingQuery = adminDb
      .collection('contactRequests')
      .where('recipientId', '==', userId)
      .where('status', '==', 'pending');

    // Get outgoing requests
    const outgoingQuery = adminDb
      .collection('contactRequests')
      .where('senderId', '==', userId)
      .where('status', '==', 'pending');

    const [incomingSnap, outgoingSnap] = await Promise.all([
      incomingQuery.get(),
      outgoingQuery.get()
    ]);

    const incoming = [];
    const outgoing = [];

    // Process incoming requests
    for (const doc of incomingSnap.docs) {
      const data = doc.data();
      const senderDoc = await adminDb.collection('users').doc(data.senderId).get();
      const senderData = senderDoc.data();

      incoming.push({
        id: doc.id,
        senderId: data.senderId,
        senderName: senderData?.displayName || senderData?.email,
        senderEmail: senderData?.email,
        senderPhotoURL: senderData?.photoURL,
        message: data.message,
        createdAt: data.createdAt.toDate(),
      });
    }

    // Process outgoing requests
    for (const doc of outgoingSnap.docs) {
      const data = doc.data();
      const recipientDoc = await adminDb.collection('users').doc(data.recipientId).get();
      const recipientData = recipientDoc.data();

      outgoing.push({
        id: doc.id,
        recipientId: data.recipientId,
        recipientName: recipientData?.displayName || recipientData?.email,
        recipientEmail: recipientData?.email,
        recipientPhotoURL: recipientData?.photoURL,
        message: data.message,
        createdAt: data.createdAt.toDate(),
      });
    }

    return new Response(
      JSON.stringify({ incoming, outgoing }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error getting contact requests:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// POST - Send a contact request
async function sendContactRequest(req: AuthenticatedRequest) {
  try {
    const userId = req.user!.uid;
    const body = await req.json();
    const { recipientId, message } = body as { recipientId: string; message?: string; };

    if (!recipientId) {
      return new Response(
        JSON.stringify({ error: 'Recipient ID is required' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (recipientId === userId) {
      return new Response(
        JSON.stringify({ error: 'Cannot send contact request to yourself' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Check if recipient exists
    const recipientDoc = await adminDb.collection('users').doc(recipientId).get();
    if (!recipientDoc.exists) {
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Check if users are already contacts
    const contactDoc = await adminDb
      .collection('contacts')
      .where('userId', '==', userId)
      .where('contactId', '==', recipientId)
      .get();

    if (!contactDoc.empty) {
      return new Response(
        JSON.stringify({ error: 'Users are already contacts' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Check if request already exists
    const existingRequest = await adminDb
      .collection('contactRequests')
      .where('senderId', '==', userId)
      .where('recipientId', '==', recipientId)
      .where('status', '==', 'pending')
      .get();

    if (!existingRequest.empty) {
      return new Response(
        JSON.stringify({ error: 'Contact request already sent' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Create contact request
    const requestData = {
      senderId: userId,
      recipientId,
      message: message || '',
      status: 'pending',
      createdAt: new Date(),
    };

    const requestRef = await adminDb.collection('contactRequests').add(requestData);

    return new Response(
      JSON.stringify({ 
        id: requestRef.id,
        message: 'Contact request sent successfully' 
      }),
      { 
        status: 201,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error sending contact request:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export const GET = createAuthMiddleware(getContactRequests);
export const POST = createAuthMiddleware(sendContactRequest);