import { AuthenticatedRequest, createAuthMiddlewareWithParams } from '../../../../../lib/auth-middleware';
import { adminDb } from '../../../../../lib/firebase-admin';

interface RouteParams {
  params: {
    requestId: string;
  };
}

// PUT - Accept or decline a contact request
async function handleContactRequest(req: AuthenticatedRequest, { params }: RouteParams) {
  try {
    const userId = req.user!.uid;
    const { requestId } = params;
    const body = await req.json();
    const { action } = body as { action: string }; // 'accept' or 'decline'

    if (!['accept', 'decline'].includes(action)) {
      return new Response(
        JSON.stringify({ error: 'Action must be "accept" or "decline"' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get the contact request
    const requestDoc = await adminDb.collection('contactRequests').doc(requestId).get();
    
    if (!requestDoc.exists) {
      return new Response(
        JSON.stringify({ error: 'Contact request not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const requestData = requestDoc.data()!;

    // Verify the current user is the recipient
    if (requestData.recipientId !== userId) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Verify request is still pending
    if (requestData.status !== 'pending') {
      return new Response(
        JSON.stringify({ error: 'Contact request already processed' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const batch = adminDb.batch();

    // Update request status
    const requestRef = adminDb.collection('contactRequests').doc(requestId);
    batch.update(requestRef, {
      status: action === 'accept' ? 'accepted' : 'declined',
      respondedAt: new Date(),
    });

    if (action === 'accept') {
      // Create mutual contact relationships
      const contact1Ref = adminDb.collection('contacts').doc();
      const contact2Ref = adminDb.collection('contacts').doc();

      batch.set(contact1Ref, {
        userId: userId,
        contactId: requestData.senderId,
        createdAt: new Date(),
      });

      batch.set(contact2Ref, {
        userId: requestData.senderId,
        contactId: userId,
        createdAt: new Date(),
      });

      // Create a new chat for the contacts
      const chatRef = adminDb.collection('chats').doc();
      batch.set(chatRef, {
        participants: [userId, requestData.senderId],
        type: 'direct',
        createdAt: new Date(),
        lastActivity: new Date(),
        lastMessage: null,
      });
    }

    await batch.commit();

    return new Response(
      JSON.stringify({ 
        message: `Contact request ${action}ed successfully`,
        action 
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error handling contact request:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export const PUT = createAuthMiddlewareWithParams(handleContactRequest);