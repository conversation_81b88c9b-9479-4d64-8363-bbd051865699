import { AuthenticatedRequest, createAuthMiddleware } from '../../../../lib/auth-middleware';
import { adminDb } from '../../../../lib/firebase-admin';

// FCM Admin SDK
import * as admin from 'firebase-admin';

// POST - Send push notification
async function sendNotification(req: AuthenticatedRequest) {
  try {
    const senderId = req.user!.uid;
    const body = await req.json();
    const { recipientUserId, title, body: notificationBody, data = {}, imageUrl, chatId } = body as {
      recipientUserId: string;
      title: string;
      body: string;
      data?: Record<string, string>;
      imageUrl?: string;
      chatId?: string;
    };

    if (!recipientUserId || !title || !notificationBody) {
      return new Response(
        JSON.stringify({ error: 'Recipient ID, title, and body are required' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get recipient's FCM token
    const recipientDoc = await adminDb.collection('users').doc(recipientUserId).get();
    if (!recipientDoc.exists) {
      return new Response(
        JSON.stringify({ error: 'Recipient not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const recipientData = recipientDoc.data()!;
    const fcmToken = recipientData.fcmToken;

    if (!fcmToken) {
      console.log('⚠️ Recipient has no FCM token, skipping push notification but saving to database');
      
      // Save notification to database for history (even without FCM token)
      await adminDb.collection('notifications').add({
        recipientId: recipientUserId,
        senderId,
        title,
        body: notificationBody,
        data,
        imageUrl: imageUrl || null,
        chatId: chatId || null,
        sentAt: new Date(),
        delivered: false, // Not delivered because no FCM token
        read: false,
      });

      return new Response(
        JSON.stringify({ 
          success: true,
          message: 'Notification saved to database (no FCM token for push notification)',
          delivered: false
        }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get sender details
    const senderDoc = await adminDb.collection('users').doc(senderId).get();
    const senderData = senderDoc.exists ? senderDoc.data()! : { displayName: 'Someone' };
    const senderName = senderData.displayName || senderData.email || 'Someone';

    // Prepare notification message
    const message: admin.messaging.Message = {
      token: fcmToken,
      notification: {
        title: title,
        body: notificationBody,
        imageUrl: imageUrl || undefined,
      },
      data: {
        senderId,
        senderName,
        chatId: chatId || '',
        timestamp: new Date().toISOString(),
        ...data,
      },
      android: {
        notification: {
          icon: 'ic_notification',
          color: '#FF6B35',
          channelId: 'messages',
          priority: 'high' as const,
          defaultSound: true,
        },
        priority: 'high',
      },
      apns: {
        payload: {
          aps: {
            alert: {
              title: title,
              body: notificationBody,
            },
            badge: 1,
            sound: 'default',
            'content-available': 1,
            'mutable-content': 1,
          },
        },
        headers: {
          'apns-priority': '10',
          'apns-push-type': 'alert',
        },
      },
    };

    // Send notification
    try {
      const response = await admin.messaging().send(message);
      console.log('✅ Notification sent successfully:', response);

      // Save notification to database for history
      await adminDb.collection('notifications').add({
        recipientId: recipientUserId,
        senderId,
        title,
        body: notificationBody,
        data,
        imageUrl: imageUrl || null,
        chatId: chatId || null,
        sentAt: new Date(),
        delivered: true,
        read: false,
      });

      return new Response(
        JSON.stringify({ 
          success: true,
          messageId: response,
          message: 'Notification sent successfully'
        }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );

    } catch (fcmError: unknown) {
      console.error('❌ FCM Error:', fcmError);
      
      const error = fcmError as { code?: string };

      // Handle specific FCM errors
      let errorMessage = 'Failed to send notification';
      if (error.code === 'messaging/registration-token-not-registered') {
        errorMessage = 'Device token is no longer valid';
        // Optionally remove the invalid token from user profile
        await adminDb.collection('users').doc(recipientUserId).update({
          fcmToken: null,
        });
      } else if (error.code === 'messaging/invalid-argument') {
        errorMessage = 'Invalid notification payload';
      }

      return new Response(
        JSON.stringify({ 
          error: errorMessage,
          code: error.code
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

  } catch (error) {
    console.error('Error sending notification:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export const POST = createAuthMiddleware(sendNotification);