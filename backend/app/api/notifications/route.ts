import { AuthenticatedRequest, createAuthMiddleware } from '../../../lib/auth-middleware';
import { adminDb } from '../../../lib/firebase-admin';

// GET - Get user's notifications
async function getUserNotifications(req: AuthenticatedRequest) {
  try {
    const userId = req.user!.uid;
    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const unreadOnly = searchParams.get('unreadOnly') === 'true';

    let notificationsQuery = adminDb
      .collection('notifications')
      .where('recipientId', '==', userId)
      .orderBy('sentAt', 'desc')
      .limit(limit);

    if (unreadOnly) {
      notificationsQuery = notificationsQuery.where('read', '==', false);
    }

    const snapshot = await notificationsQuery.get();
    const notifications = [];

    for (const doc of snapshot.docs) {
      const data = doc.data();
      
      // Get sender details
      let senderDetails = null;
      if (data.senderId) {
        const senderDoc = await adminDb.collection('users').doc(data.senderId).get();
        if (senderDoc.exists) {
          const senderData = senderDoc.data()!;
          senderDetails = {
            id: data.senderId,
            name: senderData.displayName || senderData.email,
            email: senderData.email,
            photoURL: senderData.photoURL,
          };
        }
      }

      notifications.push({
        id: doc.id,
        title: data.title,
        body: data.body,
        data: data.data || {},
        imageUrl: data.imageUrl,
        chatId: data.chatId,
        senderId: data.senderId,
        senderDetails,
        sentAt: data.sentAt.toDate(),
        delivered: data.delivered || false,
        read: data.read || false,
      });
    }

    return new Response(
      JSON.stringify({ 
        notifications,
        unreadCount: notifications.filter(n => !n.read).length
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error getting user notifications:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// PUT - Mark notifications as read
async function markNotificationsAsRead(req: AuthenticatedRequest) {
  try {
    const userId = req.user!.uid;
    const body = await req.json();
    const { notificationIds, markAllAsRead } = body as { notificationIds?: string[]; markAllAsRead?: boolean; };

    if (markAllAsRead) {
      // Mark all user notifications as read
      const notificationsQuery = adminDb
        .collection('notifications')
        .where('recipientId', '==', userId)
        .where('read', '==', false);

      const snapshot = await notificationsQuery.get();
      const batch = adminDb.batch();

      snapshot.docs.forEach(doc => {
        batch.update(doc.ref, { read: true, readAt: new Date() });
      });

      await batch.commit();

      return new Response(
        JSON.stringify({ 
          message: `Marked ${snapshot.size} notifications as read`
        }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );

    } else if (notificationIds && Array.isArray(notificationIds)) {
      // Mark specific notifications as read
      const batch = adminDb.batch();

      for (const notificationId of notificationIds) {
        const notificationRef = adminDb.collection('notifications').doc(notificationId);
        const notificationDoc = await notificationRef.get();
        
        if (notificationDoc.exists && notificationDoc.data()!.recipientId === userId) {
          batch.update(notificationRef, { read: true, readAt: new Date() });
        }
      }

      await batch.commit();

      return new Response(
        JSON.stringify({ 
          message: `Marked ${notificationIds.length} notifications as read`
        }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );

    } else {
      return new Response(
        JSON.stringify({ error: 'Either provide notificationIds array or set markAllAsRead to true' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

  } catch (error) {
    console.error('Error marking notifications as read:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export const GET = createAuthMiddleware(getUserNotifications);
export const PUT = createAuthMiddleware(markNotificationsAsRead);