import { NextRequest } from 'next/server';
import { createOptionalAuthMiddleware } from '../../../../lib/auth-middleware';
import { adminDb } from '../../../../lib/firebase-admin';

async function handler(req: NextRequest) {
  if (req.method !== 'GET') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '10');

    if (!query || query.trim().length < 2) {
      return new Response(
        JSON.stringify({ error: 'Search query must be at least 2 characters' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Search users by email or display name
    const usersRef = adminDb.collection('users');
    
    // Create queries for different search fields
    const emailQuery = usersRef
      .where('email', '>=', query.toLowerCase())
      .where('email', '<=', query.toLowerCase() + '\uf8ff')
      .limit(limit);

    const displayNameQuery = usersRef
      .where('searchTokens', 'array-contains-any', generateSearchTokens(query))
      .limit(limit);

    // Execute both queries
    const [emailResults, nameResults] = await Promise.all([
      emailQuery.get(),
      displayNameQuery.get()
    ]);

    // Combine and deduplicate results
    const userMap = new Map();
    
    emailResults.forEach((doc) => {
      const userData = doc.data();
      userMap.set(doc.id, {
        id: doc.id,
        email: userData.email,
        displayName: userData.displayName,
        photoURL: userData.photoURL || null,
        isOnline: userData.isOnline || false,
        lastSeen: userData.lastSeen?.toDate() || null,
      });
    });

    nameResults.forEach((doc) => {
      const userData = doc.data();
      userMap.set(doc.id, {
        id: doc.id,
        email: userData.email,
        displayName: userData.displayName,
        photoURL: userData.photoURL || null,
        isOnline: userData.isOnline || false,
        lastSeen: userData.lastSeen?.toDate() || null,
      });
    });

    const users = Array.from(userMap.values()).slice(0, limit);

    return new Response(
      JSON.stringify({ users }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error searching users:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

function generateSearchTokens(text: string): string[] {
  const tokens = [];
  const lowerText = text.toLowerCase();
  
  // Generate all possible substrings starting from the beginning
  for (let i = 1; i <= lowerText.length; i++) {
    tokens.push(lowerText.substring(0, i));
  }
  
  // Generate tokens for each word
  const words = lowerText.split(' ');
  words.forEach(word => {
    for (let i = 1; i <= word.length; i++) {
      tokens.push(word.substring(0, i));
    }
  });
  
  return [...new Set(tokens)]; // Remove duplicates
}

export const GET = createOptionalAuthMiddleware(handler);