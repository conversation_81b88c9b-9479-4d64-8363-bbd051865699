import { Server as HttpServer } from 'http';
import { Socket, Server as SocketIOServer } from 'socket.io';
import { v4 as uuidv4 } from 'uuid';
import { adminAuth, adminDb } from './lib/firebase-admin';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userEmail?: string;
}

interface UserSocketMap {
  [userId: string]: string[]; // Array to handle multiple connections per user
}

class WebSocketServer {
  private io: SocketIOServer;
  private userSockets: UserSocketMap = {};

  constructor(server: HttpServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: "*", // Configure appropriately for production
        methods: ["GET", "POST"]
      }
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth.token;
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        // Verify Firebase ID token
        const decodedToken = await adminAuth.verifyIdToken(token);
        
        socket.userId = decodedToken.uid;
        socket.userEmail = decodedToken.email || '';
        
        console.log(`✅ User ${decodedToken.email} authenticated via WebSocket`);
        next();
      } catch (error) {
        console.error('WebSocket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      console.log(`🔌 User ${socket.userEmail} connected: ${socket.id}`);
      
      if (socket.userId) {
        this.handleUserConnection(socket);
      }

      // Handle disconnection
      socket.on('disconnect', () => {
        this.handleUserDisconnection(socket);
      });

      // Join chat rooms
      socket.on('join-chat', (chatId: string) => {
        socket.join(chatId);
        console.log(`📱 User ${socket.userEmail} joined chat: ${chatId}`);
      });

      // Leave chat rooms
      socket.on('leave-chat', (chatId: string) => {
        socket.leave(chatId);
        console.log(`📱 User ${socket.userEmail} left chat: ${chatId}`);
      });

      // Send message
      socket.on('send-message', async (data: {
        chatId: string;
        text: string;
        type: 'text' | 'image';
        replyTo?: string;
      }) => {
        await this.handleSendMessage(socket, data);
      });

      // Typing indicators
      socket.on('typing-start', (chatId: string) => {
        socket.to(chatId).emit('user-typing', {
          userId: socket.userId,
          userEmail: socket.userEmail,
          chatId
        });
      });

      socket.on('typing-stop', (chatId: string) => {
        socket.to(chatId).emit('user-stopped-typing', {
          userId: socket.userId,
          userEmail: socket.userEmail,
          chatId
        });
      });

      // Message reactions
      socket.on('add-reaction', async (data: {
        messageId: string;
        reaction: string;
        chatId: string;
      }) => {
        await this.handleAddReaction(socket, data);
      });

      // User presence
      socket.on('update-presence', async (isOnline: boolean) => {
        await this.updateUserPresence(socket.userId!, isOnline);
      });
    });
  }

  private handleUserConnection(socket: AuthenticatedSocket) {
    if (!socket.userId) return;

    // Add socket to user mapping
    if (!this.userSockets[socket.userId]) {
      this.userSockets[socket.userId] = [];
    }
    this.userSockets[socket.userId].push(socket.id);

    // Update user online status
    this.updateUserPresence(socket.userId, true);

    // Join user to their personal notification room
    socket.join(`user:${socket.userId}`);
  }

  private handleUserDisconnection(socket: AuthenticatedSocket) {
    if (!socket.userId) return;

    console.log(`🔌 User ${socket.userEmail} disconnected: ${socket.id}`);

    // Remove socket from user mapping
    if (this.userSockets[socket.userId]) {
      this.userSockets[socket.userId] = this.userSockets[socket.userId]
        .filter(socketId => socketId !== socket.id);

      // If no more sockets for this user, mark as offline
      if (this.userSockets[socket.userId].length === 0) {
        delete this.userSockets[socket.userId];
        this.updateUserPresence(socket.userId, false);
      }
    }
  }

  private async handleSendMessage(socket: AuthenticatedSocket, data: {
    chatId: string;
    text: string;
    type: 'text' | 'image';
    replyTo?: string;
  }) {
    try {
      const messageId = uuidv4();
      const timestamp = new Date();

      // Create message object
      const message = {
        id: messageId,
        chatId: data.chatId,
        senderId: socket.userId,
        senderEmail: socket.userEmail,
        text: data.text,
        type: data.type,
        timestamp,
        replyTo: data.replyTo || null,
        reactions: {},
        edited: false,
        editedAt: null
      };

      // Save message to Firestore
      await adminDb.collection('messages').doc(messageId).set(message);

      // Get chat details to find participants
      const chatDoc = await adminDb.collection('chats').doc(data.chatId).get();
      if (!chatDoc.exists) {
        socket.emit('message-error', { error: 'Chat not found' });
        return;
      }

      const chatData = chatDoc.data()!;
      
      // Update chat's last message
      await adminDb.collection('chats').doc(data.chatId).update({
        lastMessage: {
          text: data.text,
          senderId: socket.userId,
          timestamp,
          type: data.type
        },
        lastActivity: timestamp
      });

      // Broadcast message to all users in the chat
      this.io.to(data.chatId).emit('new-message', message);

      // Send push notifications to offline users
      await this.sendPushNotificationsToOfflineUsers(
        chatData.participants,
        socket.userId!,
        socket.userEmail!,
        data.text,
        data.chatId,
        chatData.type === 'group' ? chatData.name : undefined
      );

      console.log(`📨 Message sent in chat ${data.chatId} by ${socket.userEmail}`);
    } catch (error) {
      console.error('Error sending message:', error);
      socket.emit('message-error', { error: 'Failed to send message' });
    }
  }

  private async handleAddReaction(socket: AuthenticatedSocket, data: {
    messageId: string;
    reaction: string;
    chatId: string;
  }) {
    try {
      const messageRef = adminDb.collection('messages').doc(data.messageId);
      const messageDoc = await messageRef.get();

      if (!messageDoc.exists) {
        socket.emit('reaction-error', { error: 'Message not found' });
        return;
      }

      const messageData = messageDoc.data();
      const reactions = messageData?.reactions || {};
      
      // Toggle reaction
      if (reactions[data.reaction] && reactions[data.reaction].includes(socket.userId)) {
        // Remove reaction
        reactions[data.reaction] = reactions[data.reaction].filter(
          (userId: string) => userId !== socket.userId
        );
        if (reactions[data.reaction].length === 0) {
          delete reactions[data.reaction];
        }
      } else {
        // Add reaction
        if (!reactions[data.reaction]) {
          reactions[data.reaction] = [];
        }
        reactions[data.reaction].push(socket.userId);
      }

      // Update message with new reactions
      await messageRef.update({ reactions });

      // Broadcast reaction update
      this.io.to(data.chatId).emit('reaction-updated', {
        messageId: data.messageId,
        reactions,
        userId: socket.userId
      });

    } catch (error) {
      console.error('Error handling reaction:', error);
      socket.emit('reaction-error', { error: 'Failed to update reaction' });
    }
  }

  private async updateUserPresence(userId: string, isOnline: boolean) {
    try {
      await adminDb.collection('users').doc(userId).update({
        isOnline,
        lastSeen: new Date()
      });

      // Notify contacts about presence change
      this.io.emit('user-presence-changed', {
        userId,
        isOnline,
        lastSeen: new Date()
      });
    } catch (error) {
      console.error('Error updating user presence:', error);
    }
  }

  // Method to send push notifications to offline users and create notifications for all users
  private async sendPushNotificationsToOfflineUsers(
    participants: string[],
    senderId: string,
    senderEmail: string,
    messageText: string,
    chatId: string,
    chatName?: string
  ): Promise<void> {
    try {
      // Get sender details
      const senderDoc = await adminDb.collection('users').doc(senderId).get();
      const senderData = senderDoc.exists ? senderDoc.data()! : {};
      const senderName = senderData.displayName || senderEmail || 'Someone';

      // Find offline participants
      for (const participantId of participants) {
        // Skip sender
        if (participantId === senderId) continue;

        // Check if user is online (has active socket connections)
        const isOnline = !!this.userSockets[participantId];
        
        if (isOnline) {
          console.log(`🟢 User ${participantId} is online, creating notification without push`);
        } else {
          console.log(`🔴 User ${participantId} is offline, sending push notification`);
        }

        // Create notification in database for all users (online and offline)
        const title = chatName || `${senderName}`;
        const body = messageText.length > 100 ? `${messageText.substring(0, 100)}...` : messageText;

        await adminDb.collection('notifications').add({
          recipientId: participantId,
          senderId,
          title,
          body,
          data: {
            chatId,
            senderId,
            senderName,
            type: 'message',
          },
          chatId,
          sentAt: new Date(),
          delivered: true,
          read: false,
        });

        // Only send push notifications to offline users
        if (!isOnline) {
          // Get participant's FCM token
          const participantDoc = await adminDb.collection('users').doc(participantId).get();
          if (!participantDoc.exists) continue;

          const participantData = participantDoc.data()!;
          const fcmToken = participantData.fcmToken;

          if (!fcmToken) {
            console.log(`🔕 User ${participantId} has no FCM token`);
            continue;
          }

          // Prepare notification payload
          const notificationPayload = {
            token: fcmToken,
            notification: {
              title,
              body,
            },
            data: {
              chatId,
              senderId,
              senderName,
              type: 'message',
              timestamp: new Date().toISOString(),
            },
            android: {
              notification: {
                icon: 'ic_notification',
                color: '#FF6B35',
                channelId: 'messages',
                priority: 'high' as const,
              },
            },
            apns: {
              payload: {
                aps: {
                  alert: { title, body },
                  badge: 1,
                  sound: 'default',
                },
              },
            },
          };

          // Send push notification
          try {
            const admin = (await import('./lib/firebase-admin')).default;
            const response = await admin.messaging().send(notificationPayload);
            console.log(`📤 Push notification sent to ${participantId}:`, response);
          } catch (fcmError: any) {
            console.error(`❌ Failed to send push notification to ${participantId}:`, fcmError);
            
            // Handle invalid tokens
            if (fcmError.code === 'messaging/registration-token-not-registered') {
              await adminDb.collection('users').doc(participantId).update({ fcmToken: null });
            }
          }
        }
      }
    } catch (error) {
      console.error('Error sending push notifications:', error);
    }
  }

  // Method to get online users count
  public getOnlineUsersCount(): number {
    return Object.keys(this.userSockets).length;
  }
}

export default WebSocketServer;