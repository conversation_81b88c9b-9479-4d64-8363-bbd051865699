import * as admin from 'firebase-admin';
import * as fs from 'fs';

// Check if Firebase Admin is already initialized
if (!admin.apps.length) {
  try {
    // Initialize with service account key for development
    // In production, this should use environment variables or default credentials
    const serviceAccountPath = process.env.FIREBASE_ADMIN_SDK_PATH || './serviceAccountKey.json';
    
    if (process.env.NODE_ENV === 'production') {
      // For production deployment (e.g., Vercel, Heroku)
      const serviceAccount = {
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      };
      
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });
    } else {
      // For development with service account file
      try {
        const serviceAccountFile = fs.readFileSync(serviceAccountPath, 'utf8');
        const serviceAccount = JSON.parse(serviceAccountFile);
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
        });
      } catch {
        console.log('⚠️ Service account key not found, using fallback Firebase Admin initialization');
        admin.initializeApp({
          projectId: process.env.FIREBASE_PROJECT_ID || process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID || 'namoshkar-messenger',
        });
      }
    }
    
    console.log('✅ Firebase Admin initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing Firebase Admin:', error);
    // Create a fallback initialization for development
    if (process.env.NODE_ENV === 'development') {
      console.log('⚠️ Using fallback Firebase Admin initialization');
      admin.initializeApp({
        projectId: process.env.FIREBASE_PROJECT_ID || process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID || 'namoshkar-messenger',
      });
    }
  }
}

export const adminAuth = admin.auth();
export const adminDb = admin.firestore();
export default admin;