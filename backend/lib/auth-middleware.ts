import { NextRequest } from 'next/server';
import { adminAuth } from './firebase-admin';

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    uid: string;
    email: string;
    name?: string;
  };
}

export async function verifyAuthToken(req: NextRequest): Promise<{ uid: string; email: string; name?: string } | null> {
  try {
    const authHeader = req.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    if (!idToken) {
      return null;
    }

    // Verify the Firebase ID token
    const decodedToken = await adminAuth.verifyIdToken(idToken);
    
    return {
      uid: decodedToken.uid,
      email: decodedToken.email || '',
      name: decodedToken.name,
    };
  } catch (error) {
    console.error('Error verifying auth token:', error);
    return null;
  }
}

export function createAuthMiddleware(handler: (req: AuthenticatedRequest) => Promise<Response>) {
  return async (req: NextRequest) => {
    const user = await verifyAuthToken(req);
    
    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Add user to request object
    (req as AuthenticatedRequest).user = user;
    
    return handler(req as AuthenticatedRequest);
  };
}

export function createAuthMiddlewareWithParams<C extends { params: unknown }>(
  handler: (req: AuthenticatedRequest, context: C) => Promise<Response>
) {
  // This returned function is the actual route handler that Next.js will call.
  // It is typed to accept params as a promise, which is a feature in Next.js 15+.
  return async (req: NextRequest, context: { params: Promise<C['params']> | C['params'] }) => {
    
    // We await the params to resolve them before passing them to the actual logic.
    const resolvedParams = await context.params;
    const resolvedContext = { params: resolvedParams } as C;

    const user = await verifyAuthToken(req);
    
    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Add user to request object
    (req as AuthenticatedRequest).user = user;
    
    // Call the original handler with the resolved context.
    return handler(req as AuthenticatedRequest, resolvedContext);
  };
}

export function createOptionalAuthMiddleware(handler: (req: AuthenticatedRequest) => Promise<Response>) {
  return async (req: NextRequest) => {
    const user = await verifyAuthToken(req);
    
    // Add user to request object (will be null if not authenticated)
    (req as AuthenticatedRequest).user = user || undefined;
    
    return handler(req as AuthenticatedRequest);
  };
}