import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  // For preflight OPTIONS requests, return a response with CORS headers immediately.
  if (request.method === 'OPTIONS') {
    const response = new Response(null, { status: 204 });
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    response.headers.set('Access-Control-Max-Age', '86400'); // Cache for 24 hours
    return response;
  }

  // For all other requests, pass them through and add the origin header to the response.
  const response = NextResponse.next();
  response.headers.set('Access-Control-Allow-Origin', '*');

  return response;
}

// This configures the middleware to run on all API routes.
export const config = {
  matcher: '/api/:path*',
};