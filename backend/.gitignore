# Backend specific .gitignore

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.lerna-debug.log*

# Production
/build
/dist
/.next/
/coverage/
*.log

# Environment files
.env
.env*.local
.env.development
.env.production

# Firebase
# Protect sensitive Firebase service account keys
serviceAccountKey.json
*serviceAccountKey*.json
firebase-service-account.json
*-firebase-adminsdk-*.json

# Logs
logs
*.log

# Temporary files
tmp/
temp/

# Database
*.sqlite
*.sqlite3

# Security
*.pem
*.key
*.csr
*.crt

# Expo
.expo/
web-build/
npm-debug.*
*.jks
*.p8
*.p12