{"name": "backend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "express": "^5.1.0", "firebase-admin": "^13.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0", "socket.io": "^4.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "nodemon": "^3.1.10", "tailwindcss": "^4", "typescript": "^5"}}