rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read user data for search functionality
    // But only allow users to update/delete their own data
    match /users/{userId} {
      allow read: if request.auth != null;
      allow update, delete: if request.auth != null && request.auth.uid == userId;
      allow create: if request.auth != null;
    }
    
    // Allow users to read and write their own chats
    match /chats/{chatId} {
      allow read, update, delete: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // Allow users to read and write their own messages
    match /messages/{messageId} {
      allow read, update, delete: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // Allow users to read and write their own contacts
    match /contacts/{contactId} {
      allow read, update, delete: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // Allow users to read and write their own contact requests
    match /contactRequests/{requestId} {
      allow read, update, delete: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // Allow authenticated users to access notifications
    match /notifications/{notificationId} {
      // Allow all authenticated users to read and create notifications
      allow read, create: if request.auth != null;
      
      // Only allow updating and deleting if the user is the recipient
      allow update, delete: if request.auth != null && 
        ((resource != null && resource.data != null && resource.data.recipientId == request.auth.uid) || 
         (request.resource != null && request.resource.data != null && request.resource.data.recipientId == request.auth.uid));
    }
    
    // Allow users to read and write stories
    match /stories/{storyId} {
      // Allow all authenticated users to read stories (filtered by visibility in query)
      allow read: if request.auth != null;
      
      // Allow users to create stories
      allow create: if request.auth != null;
      
      // Allow users to update and delete their own stories
      allow update, delete: if request.auth != null && 
        ((resource != null && resource.data != null && resource.data.userId == request.auth.uid) || 
         (request.resource != null && request.resource.data != null && request.resource.data.userId == request.auth.uid));
    }
    
    // Default deny for all other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}