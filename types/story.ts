export interface Story {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  mediaUrl: string;
  mediaType: 'image' | 'video';
  caption?: string;
  timestamp: Date;
  expiresAt: Date;
  viewedBy: string[]; // Array of user IDs who have viewed the story
  isVisibleTo: string[]; // Array of user IDs who can view the story
  createdAt: Date;
}

export interface StoryViewer {
  userId: string;
  userName: string;
  viewedAt: Date;
}

export interface StoryUploadData {
  mediaUri: string;
  mediaType: 'image' | 'video';
  caption?: string;
  visibleTo: string[]; // Array of user IDs
}

export interface StoryStats {
  totalViews: number;
  viewerList: StoryViewer[];
}