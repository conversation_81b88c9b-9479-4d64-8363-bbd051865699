// User interfaces
export interface User {
  id: string;
  email?: string; // Made optional for backward compatibility
  displayName?: string; // Made optional for backward compatibility
  name: string; // Display name alias for backward compatibility
  userId?: string; // User ID alias for backward compatibility
  photoURL?: string | null; // Changed from optional to nullable for consistency with UserProfile
  avatar?: string; // Avatar alias for backward compatibility
  isOnline: boolean;
  lastSeen?: Date; // Made optional for backward compatibility
  searchTokens?: string[];
  createdAt?: Date;
}

export interface UserProfile extends User {
  uid: string;
}

// Message interfaces
export interface Message {
  id: string;
  chatId?: string; // Made optional for mock data
  senderId: string;
  senderName?: string; // Made optional for mock data
  senderEmail?: string; // Made optional for mock data
  text: string;
  type: 'text' | 'image' | 'file' | 'video';
  timestamp: Date;
  replyTo?: string;
  reactions?: { [emoji: string]: string[] }; // Made optional for mock data
  edited?: boolean;
  editedAt?: Date | null;
  deleted?: boolean;
  deletedAt?: Date | null;
  imageUrl?: string;
  imageUri?: string; // Legacy property for backward compatibility
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  // Video-related fields
  videoUrl?: string;
  videoThumbnail?: string;
  videoDuration?: number;
  videoSize?: number;
  // Image dimensions
  imageWidth?: number;
  imageHeight?: number;
  videoWidth?: number;
  videoHeight?: number;
}

export interface SocketMessage extends Message {
  // Additional fields for WebSocket messages
}

// Chat interfaces
export interface Chat {
  id: string;
  participants: string[];
  participantDetails: { [userId: string]: { name: string; email: string; photoURL?: string | null } }; // Updated to nullable
  type: 'direct' | 'group';
  name?: string;
  description?: string;
  photoURL?: string | null; // Updated to nullable
  groupAvatar?: string; // Group avatar alias for backward compatibility
  createdBy?: string;
  createdAt: Date;
  messages?: Message[]; // Messages array for backward compatibility
  isGroup?: boolean; // Computed property for backward compatibility
  lastMessage?: {
    text: string;
    senderId: string;
    timestamp: Date;
    type: string;
  };
  lastActivity: Date;
  unreadCount?: { [userId: string]: number };
}

export interface ChatListItem {
  id: string;
  name: string;
  lastMessage: string;
  lastMessageTime: Date;
  unreadCount: number;
  isGroup: boolean;
  participants: string[];
  photoURL?: string | null; // Updated to nullable
}

// Legacy interface for backward compatibility
export interface ChatPreview extends ChatListItem {
  timestamp: Date;
  avatar?: string;
  isOnline?: boolean;
}

// Contact interfaces
export interface Contact {
  id: string;
  contactId: string;
  contactName: string;
  contactEmail: string;
  contactPhotoURL?: string | null; // Updated to nullable
  isOnline: boolean;
  lastSeen: Date;
  createdAt: Date;
}

export interface ContactRequest {
  id: string;
  senderId: string;
  senderName: string;
  senderEmail: string;
  senderPhotoURL?: string | null; // Updated to nullable
  recipientId?: string;
  recipientName?: string;
  recipientEmail?: string;
  recipientPhotoURL?: string | null; // Updated to nullable
  message: string;
  status: 'pending' | 'accepted' | 'declined';
  createdAt: Date;
  type: 'incoming' | 'outgoing';
}

// WebSocket interfaces
export interface TypingUser {
  userId: string;
  userEmail: string;
  chatId: string;
}

export interface UserPresence {
  userId: string;
  isOnline: boolean;
  lastSeen: Date;
}

// Authentication interfaces
export interface AuthResponse {
  success: boolean;
  user?: UserProfile;
  error?: string;
}

// Legacy reaction interface for backward compatibility
export interface Reaction {
  emoji: string;
  userId: string;
}

// Notification interfaces
export interface Notification {
  id: string;
  type: 'message' | 'contact_request' | 'contact_accepted' | 'group_invite';
  title: string;
  body: string;
  data?: any;
  timestamp: Date;
  read: boolean;
  userId: string;
}