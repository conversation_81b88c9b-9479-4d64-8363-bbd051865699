{"indexes": [{"collectionGroup": "chats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participants", "arrayConfig": "CONTAINS"}, {"fieldPath": "lastActivity", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "chatId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "contacts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "recipientId", "order": "ASCENDING"}, {"fieldPath": "sentAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "recipientId", "order": "ASCENDING"}, {"fieldPath": "read", "order": "ASCENDING"}, {"fieldPath": "sentAt", "order": "DESCENDING"}]}, {"collectionGroup": "contactRequests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "recipientId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "contactRequests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "senderId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "stories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isVisibleTo", "arrayConfig": "CONTAINS"}, {"fieldPath": "expiresAt", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": []}