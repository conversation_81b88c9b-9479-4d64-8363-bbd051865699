const { chromium } = require('playwright');

async function testAuthPersistence() {
  console.log('🔍 Starting authentication persistence test...');
  
  // Launch browser
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Navigate to the app (now on port 8086)
    console.log('🌐 Navigating to http://localhost:8086');
    await page.goto('http://localhost:8086');
    
    // Wait for sign-in page to load
    console.log('⏳ Waiting for sign-in page...');
    await page.waitForSelector('input[type="email"]', { timeout: 15000 });
    console.log('✅ Sign-in page loaded');
    
    // Fill in credentials
    console.log('🔐 Entering credentials...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'k4rm4k4r');
    
    // Submit the form by clicking the sign-in button
    console.log('🚀 Submitting login form...');
    // The sign-in button has text "Sign In" and is a TouchableOpacity
    await page.click('text=Sign In');
    
    // Wait for redirect to main app (either tabs or profile setup)
    console.log('⏳ Waiting for authentication...');
    await page.waitForTimeout(5000);
    
    // Check current URL
    const currentUrl = page.url();
    console.log(`📍 Current URL: ${currentUrl}`);
    
    // Check that we're not on the sign-in page anymore
    if (currentUrl.includes('sign-in')) {
      console.log('❌ Failed to authenticate');
      return;
    }
    
    // Check if we're on the tabs page or profile setup page
    const isOnTabs = currentUrl.includes('(tabs)');
    const isOnProfileSetup = currentUrl.includes('profile-setup');
    
    console.log(`📊 Authentication status - Tabs: ${isOnTabs}, Profile Setup: ${isOnProfileSetup}`);
    
    if (!isOnTabs && !isOnProfileSetup) {
      console.log('❌ Not on expected page after login');
      return;
    }
    
    console.log('✅ Successfully logged in');
    
    // Store the current URL for comparison after reload
    const originalDestination = currentUrl;
    console.log(`💾 Original destination: ${originalDestination}`);
    
    // Reload the page
    console.log('🔄 Reloading page...');
    await page.reload({ waitUntil: 'networkidle' });
    
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    // Check that we're still authenticated
    const newUrl = page.url();
    console.log(`📍 New URL after reload: ${newUrl}`);
    
    // Should still be on the same destination (tabs or profile setup)
    let success = false;
    if (originalDestination.includes('(tabs)') && newUrl.includes('(tabs)')) {
      console.log('✅ Successfully remained on tabs page after reload');
      success = true;
    } else if (originalDestination.includes('profile-setup') && newUrl.includes('profile-setup')) {
      console.log('✅ Successfully remained on profile setup page after reload');
      success = true;
    }
    
    if (success) {
      console.log('🎉 Authentication persistence test PASSED');
    } else {
      console.log('❌ Authentication persistence test FAILED');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  } finally {
    // Close browser
    await browser.close();
    console.log('🔒 Browser closed');
  }
}

// Run the test
testAuthPersistence().catch(console.error);