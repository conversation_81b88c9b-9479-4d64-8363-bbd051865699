import { test, expect } from '@playwright/test';

// Test credentials
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'k4rm4k4r';

test.describe('Authentication Persistence', () => {
  test('should persist authentication state after page reload', async ({ page }) => {
    console.log('🔍 Testing authentication persistence after page reload...');
    
    // Navigate to the app
    console.log('🌐 Navigating to http://localhost:8085');
    await page.goto('http://localhost:8085');
    
    // Wait for sign-in page to load
    console.log('⏳ Waiting for sign-in page...');
    await page.waitForSelector('input[type="email"]', { timeout: 15000 });
    console.log('✅ Sign-in page loaded');
    
    // Fill in credentials
    console.log('🔐 Entering credentials...');
    await page.fill('input[type="email"]', TEST_EMAIL);
    await page.fill('input[type="password"]', TEST_PASSWORD);
    
    // Submit the form
    console.log('🚀 Submitting login form...');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to main app (either tabs or profile setup)
    console.log('⏳ Waiting for authentication...');
    await page.waitForTimeout(5000);
    
    // Check current URL
    const currentUrl = page.url();
    console.log(`📍 Current URL: ${currentUrl}`);
    
    // Check that we're not on the sign-in page anymore
    expect(currentUrl).not.toContain('sign-in');
    
    // Check if we're on the tabs page or profile setup page
    const isOnTabs = currentUrl.includes('(tabs)');
    const isOnProfileSetup = currentUrl.includes('profile-setup');
    
    console.log(`📊 Authentication status - Tabs: ${isOnTabs}, Profile Setup: ${isOnProfileSetup}`);
    expect(isOnTabs || isOnProfileSetup).toBeTruthy();
    
    // Store the current URL for comparison after reload
    const originalDestination = currentUrl;
    console.log(`💾 Original destination: ${originalDestination}`);
    
    // Reload the page
    console.log('🔄 Reloading page...');
    await page.reload({ waitUntil: 'networkidle' });
    
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    // Check that we're still authenticated
    const newUrl = page.url();
    console.log(`📍 New URL after reload: ${newUrl}`);
    
    // Should still be on the same destination (tabs or profile setup)
    if (originalDestination.includes('(tabs)')) {
      expect(newUrl).toContain('(tabs)');
      console.log('✅ Successfully remained on tabs page after reload');
    } else if (originalDestination.includes('profile-setup')) {
      expect(newUrl).toContain('profile-setup');
      console.log('✅ Successfully remained on profile setup page after reload');
    }
    
    console.log('🎉 Authentication persistence test PASSED');
  });
  
  test('should maintain auth state in new browser context', async ({ browser }) => {
    console.log('🔍 Testing authentication persistence in new browser context...');
    
    // Create a new context to simulate a fresh browser session
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Navigate to the app
    console.log('🌐 Navigating to http://localhost:8085');
    await page.goto('http://localhost:8085');
    
    // Wait for sign-in page to load
    console.log('⏳ Waiting for sign-in page...');
    await page.waitForSelector('input[type="email"]', { timeout: 15000 });
    console.log('✅ Sign-in page loaded');
    
    // Fill in credentials
    console.log('🔐 Entering credentials...');
    await page.fill('input[type="email"]', TEST_EMAIL);
    await page.fill('input[type="password"]', TEST_PASSWORD);
    
    // Submit the form
    console.log('🚀 Submitting login form...');
    await page.click('button[type="submit"]');
    
    // Wait for redirect
    console.log('⏳ Waiting for authentication...');
    await page.waitForTimeout(5000);
    
    // Check current URL
    const currentUrl = page.url();
    console.log(`📍 Current URL: ${currentUrl}`);
    
    // Check that we're authenticated
    const isOnTabs = currentUrl.includes('(tabs)');
    const isOnProfileSetup = currentUrl.includes('profile-setup');
    
    console.log(`📊 Authentication status - Tabs: ${isOnTabs}, Profile Setup: ${isOnProfileSetup}`);
    expect(isOnTabs || isOnProfileSetup).toBeTruthy();
    
    // Close the context and create a new one (simulating browser restart)
    console.log('🚪 Closing current context...');
    await context.close();
    
    console.log('🚪 Creating new browser context...');
    const newContext = await browser.newContext();
    const newPage = await newContext.newPage();
    
    // Navigate to the app again
    console.log('🌐 Navigating to http://localhost:8085 in new context');
    await newPage.goto('http://localhost:8085');
    
    // Wait for page to load
    await newPage.waitForTimeout(5000);
    
    // Check current URL in new context
    const newUrl = newPage.url();
    console.log(`📍 URL in new context: ${newUrl}`);
    
    // Should still be authenticated (browser-level persistence)
    if (currentUrl.includes('(tabs)')) {
      expect(newUrl).toContain('(tabs)');
      console.log('✅ Successfully remained on tabs page in new context');
    } else if (currentUrl.includes('profile-setup')) {
      expect(newUrl).toContain('profile-setup');
      console.log('✅ Successfully remained on profile setup page in new context');
    }
    
    await newContext.close();
    console.log('🎉 Browser context persistence test PASSED');
  });
});