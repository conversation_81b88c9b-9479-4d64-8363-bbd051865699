import { Platform } from 'react-native';

/**
 * Utility function to safely get platform version as a number
 * Handles both string (iOS) and number (Android) versions
 */
export function getPlatformVersionAsNumber(): number {
  if (typeof Platform.Version === 'number') {
    return Platform.Version;
  }
  // For iOS, Platform.Version is a string like "16.3.1"
  // Parse the major version number
  return parseInt(Platform.Version, 10) || 0;
}

/**
 * Check if Android version is at least the specified API level
 */
export function isAndroidVersionAtLeast(minVersion: number): boolean {
  return Platform.OS === 'android' && getPlatformVersionAsNumber() >= minVersion;
}

/**
 * Check if iOS version is at least the specified major version
 */
export function isIOSVersionAtLeast(minVersion: number): boolean {
  return Platform.OS === 'ios' && getPlatformVersionAsNumber() >= minVersion;
}

/**
 * Get platform-specific padding for Android versions >= 30 (Android 11+)
 * Returns the appropriate value based on the Android version
 */
export function getAndroidVersionAwarePadding(standardValue: number, android11Value: number): number {
  return Platform.OS === 'android' 
    ? (getPlatformVersionAsNumber() >= 30 ? android11Value : standardValue)
    : standardValue;
}