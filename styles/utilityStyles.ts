import { StyleSheet } from 'react-native';

/**
 * Utility styles to help with common styling patterns and avoid deprecated prop usage
 */
export const utilityStyles = StyleSheet.create({
  /**
   * Use these styles for pointer events instead of direct props
   * e.g., style={[styles.yourStyle, utilityStyles.pointerEventsNone]}
   */
  pointerEventsNone: { 
    pointerEvents: 'none'
  },
  pointerEventsAuto: { 
    pointerEvents: 'auto'
  },
  pointerEventsBoxNone: { 
    pointerEvents: 'box-none'
  },
  pointerEventsBoxOnly: { 
    pointerEvents: 'box-only'
  },
  
  // Other utility styles
  fullSize: {
    width: '100%',
    height: '100%',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  shadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
});