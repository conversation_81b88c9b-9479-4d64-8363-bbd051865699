# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Frontend (Expo App)
```bash
# Start development server
npm run dev

# Build and run on specific platforms
npm run android
npm run ios
npm run web

# Lint the code
npm run lint
```

## Firebase Setup Requirements

### Required Firestore Indexes

The application requires the following composite Firestore indexes to function properly:

**Stories Collection Index:**
- **Fields**: `isVisibleTo` (array), `userId` (string), `timestamp` (descending), `expiresAt` (descending)
- **Purpose**: Used by the story listener to query contact stories efficiently
- **Create URL**: https://console.firebase.google.com/v1/r/project/namoshkar-messenger/firestore/indexes?create_composite=ClNwcm9qZWN0cy9uYW1vc2hrYXItbWVzc2VuZ2VyL2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9zdG9yaWVzL2luZGV4ZXMvXxABGg8KC2lzVmlzaWJsZVRvGAEaCgoGdXNlcklkEAEaDQoJdGltZXN0YW1wEAIaDQoJZXhwaXJlc0F0EAIaDAoIX19uYW1lX18QAg

**To create this index:**
1. Visit the Firebase Console URL provided above
2. Click "Create Index" 
3. Wait for the index to build (this may take a few minutes)

The application includes fallback logic that uses a simplified query while the index is building, but full functionality requires the composite index.

### Backend (Next.js API Server)
```bash
# Start backend server (runs on port 3001)
cd backend && npm run dev

# Build backend
cd backend && npm run build

# Start production backend
cd backend && npm run start

# Lint backend
cd backend && npm run lint
```

## Architecture Overview

### Project Structure
This is a React Native messenger app with Firebase backend and a separate Next.js API server:

- **Frontend**: Expo Router-based React Native app with tabs navigation
- **Backend**: Next.js API server with WebSocket support for real-time messaging
- **Database**: Firebase Firestore for data persistence
- **Authentication**: Firebase Authentication

### Key Components

#### Frontend Architecture
- **Tab Navigation**: Main app uses Expo Router with `(tabs)` directory for bottom navigation
- **Notification System**: Global notification context with shared `NotificationBell` component
- **Chat System**: Real-time messaging with WebSocket integration
- **Contact Management**: User profiles and contact request system

#### Backend Architecture
- **API Routes**: RESTful endpoints for chats, contacts, users, and notifications
- **WebSocket Server**: Real-time messaging, typing indicators, and presence management
- **Firebase Admin**: Server-side Firebase operations for database management

### Critical Data Flow

#### Notification System
**Current Issue**: WebSocket server only creates notifications for offline users, causing notification bell to show 0 count when users are active.

**Notification Creation Points**:
1. **Messages**: Created in `backend/websocket-server.ts` via `sendPushNotificationsToOfflineUsers()`
2. **Contact Requests**: Created in `backend/app/api/contacts/requests/route.ts`
3. **Contact Acceptances**: Created in `backend/app/api/contacts/requests/[requestId]/route.ts`
4. **Group Additions**: Created in `backend/app/api/chats/route.ts`

**Frontend Notification Handling**:
- `contexts/NotificationContext.tsx`: Global state management with 30-second auto-refresh
- `components/NotificationBell.tsx`: Reusable component showing unread count
- `components/NotificationPopup.tsx`: Modal for displaying notifications

#### Message Flow
1. **Send Message**: Frontend → WebSocket → Backend → Firestore
2. **Create Notification**: Backend checks if recipient is online → Creates notification only if offline
3. **Real-time Update**: WebSocket emits `new-message` to all chat participants
4. **UI Update**: Frontend receives message via WebSocket and updates chat interface

### Important Files

#### Frontend Core Files
- `app/_layout.tsx`: Root layout with providers (Auth, Notification)
- `contexts/NotificationContext.tsx`: Global notification state management
- `components/NotificationBell.tsx`: Shared notification component
- `components/NotificationPopup.tsx`: Notification display modal
- `services/notificationApiService.ts`: Firestore notification operations

#### Backend Core Files
- `backend/websocket-server.ts`: Real-time messaging and notification logic
- `backend/app/api/chats/route.ts`: Chat creation with group notifications
- `backend/app/api/contacts/requests/route.ts`: Contact request notifications
- `backend/lib/firebase-admin.ts`: Firebase admin configuration

### Known Issues

#### Notification Bell Problem
The notification bell shows 0 count despite receiving messages because:
1. WebSocket server's `sendPushNotificationsToOfflineUsers()` only creates notifications for offline users
2. When testing the app while active, users are considered "online" so no notifications are created
3. The system should create notifications for all messages but only send push notifications to offline users

#### Frontend-Backend Integration
- Frontend uses Firebase SDK for direct database access
- Backend uses Firebase Admin for server-side operations
- WebSocket handles real-time communication
- API routes handle contact requests and user management

### Firebase Collections
- `users`: User profiles and authentication data
- `chats`: Chat metadata and participant lists
- `messages`: Individual chat messages
- `notifications`: User notifications (unread count, content)
- `contactRequests`: Pending contact requests
- `contacts`: Established contact relationships

### Development Tips
- The notification system has extensive debug logging (🔔 DEBUG prefixes)
- Test notifications using the "Test Notification" button in Settings
- WebSocket server logs show online/offline user status
- Backend runs on port 3001, frontend on default Expo ports