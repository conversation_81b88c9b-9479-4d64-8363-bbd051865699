{"compilerOptions": {"strict": true, "paths": {"@/*": ["./*"]}, "jsx": "react-native", "target": "esnext", "lib": ["esnext"], "allowJs": true, "skipLibCheck": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "types": ["node"]}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules/**/*", "node_modules/expo-haptics/**/*", "node_modules/expo-image/**/*"], "extends": "./node_modules/expo/tsconfig.base.json"}