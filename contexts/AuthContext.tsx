import { doc, getDoc } from 'firebase/firestore';
import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { getAuth, db } from '../config/firebase';
import { AuthResponse, authService, UserProfile } from '../services/authService';

// Remove direct import of User from firebase/auth
// We'll use 'any' type for now to avoid import issues
type UserType = any;

interface AuthContextType {
  user: UserType | null;
  userProfile: UserProfile | null;
  loading: boolean;
  hasCompletedProfile: boolean;
  signIn: (email: string, password: string) => Promise<AuthResponse>;
  signUp: (email: string, password: string, displayName: string) => Promise<AuthResponse>;
  signOut: () => Promise<AuthResponse>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<AuthResponse>;
  getAuthToken: () => Promise<string | null>;
  setHasCompletedProfile: (completed: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<UserType | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [hasCompletedProfile, setHasCompletedProfile] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('AuthProvider mounted, setting up auth state listener');
    
    // Subscribe to auth state changes
    let unsubscribe: (() => void) | undefined;
    let mounted = true;
    
    const setupAuthListener = async () => {
      try {
        // Wait for auth to be initialized with persistence
        const auth = await getAuth();
        if (!auth) {
          console.error('Firebase Auth not available');
          if (mounted) setLoading(false);
          return;
        }
        
        console.log('Firebase Auth initialized, setting up listener');
        
        // Import onAuthStateChanged dynamically
        const { onAuthStateChanged } = await import('firebase/auth');
        
        // Wait for auth to restore the persisted session
        // This is crucial for React Native persistence
        await new Promise<void>((resolve) => {
          const unsubscribeTemp = onAuthStateChanged(auth, (user) => {
            console.log('Initial auth state resolved:', user ? 'User found' : 'No user');
            unsubscribeTemp();
            resolve();
          });
        });
        
        // Now get the current user after persistence has been restored
        const currentUser = auth.currentUser;
        console.log('Current user after persistence check:', currentUser);
        
        if (currentUser && mounted) {
          setUser(currentUser);
          
          try {
            // Get user profile from Firestore
            const userDoc = await getDoc(doc(db, 'users', currentUser.uid));
            if (userDoc.exists()) {
              const profile = userDoc.data() as UserProfile;
              console.log('User profile from Firestore on mount:', profile);
              if (mounted) {
                setUserProfile(profile);
                
                // Check if user has completed profile setup
                // A user has completed their profile if they have both displayName and photoURL
                const isProfileComplete = !!(profile.displayName && profile.photoURL);
                console.log('Profile completion check on mount:', {
                  displayName: profile.displayName,
                  photoURL: profile.photoURL,
                  isProfileComplete
                });
                setHasCompletedProfile(isProfileComplete);
              }
            } else {
              console.log('User document not found in Firestore on mount');
              if (mounted) {
                setUserProfile(null);
                setHasCompletedProfile(false);
              }
            }
          } catch (error) {
            console.error('Error fetching user profile on mount:', error);
            if (mounted) {
              setUserProfile(null);
              setHasCompletedProfile(false);
            }
          }
        } else {
          // No current user
          console.log('No current user on mount');
          if (mounted) {
            setUser(null);
            setUserProfile(null);
            setHasCompletedProfile(false);
          }
        }
        
        // Set loading to false after initial check
        if (mounted) setLoading(false);
        
        // Subscribe to ongoing auth state changes
        unsubscribe = onAuthStateChanged(auth, async (user) => {
          if (!mounted) return;
          
          console.log('Auth state changed in AuthContext:', user ? 'User logged in' : 'User logged out');
          console.log('AuthContext - User UID:', user ? user.uid : 'null');
          setUser(user);
          
          if (user) {
            try {
              // Get user profile from Firestore
              const userDoc = await getDoc(doc(db, 'users', user.uid));
              if (userDoc.exists()) {
                const profile = userDoc.data() as UserProfile;
                console.log('User profile from Firestore in AuthContext:', profile);
                if (mounted) {
                  setUserProfile(profile);
                  
                  // Check if user has completed profile setup
                  // A user has completed their profile if they have both displayName and photoURL
                  const isProfileComplete = !!(profile.displayName && profile.photoURL);
                  console.log('Profile completion check in AuthContext:', {
                    displayName: profile.displayName,
                    photoURL: profile.photoURL,
                    isProfileComplete
                  });
                  setHasCompletedProfile(isProfileComplete);
                }
              } else {
                console.log('User document not found in Firestore');
                if (mounted) {
                  setUserProfile(null);
                  setHasCompletedProfile(false);
                }
              }
            } catch (error) {
              console.error('Error fetching user profile in AuthContext:', error);
              if (mounted) {
                setUserProfile(null);
                setHasCompletedProfile(false);
              }
            }
          } else {
            console.log('No user in AuthContext, clearing profile');
            if (mounted) {
              setUserProfile(null);
              setHasCompletedProfile(false);
            }
          }
        });
      } catch (error) {
        console.error('Error setting up auth listener:', error);
        setLoading(false);
      }
    };
    
    setupAuthListener();
    
    // Cleanup subscription on unmount
    return () => {
      console.log('AuthProvider unmounting, unsubscribing from auth state changes');
      mounted = false;
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  const signIn = async (email: string, password: string): Promise<AuthResponse> => {
    setLoading(true);
    try {
      const result = await authService.signInUser(email, password);
      if (result.success && result.user) {
        // Get fresh auth instance and user
        const auth = await getAuth();
        if (auth) {
          setUser(auth.currentUser);
        }
        setUserProfile(result.user);
        // Check if user has completed profile setup
        // A user has completed their profile if they have both displayName and photoURL
        const isProfileComplete = !!(result.user.displayName && result.user.photoURL);
        console.log('Profile completion check in signIn:', {
          displayName: result.user.displayName,
          photoURL: result.user.photoURL,
          isProfileComplete
        });
        setHasCompletedProfile(isProfileComplete);
      }
      return result;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (
    email: string, 
    password: string, 
    displayName: string
  ): Promise<AuthResponse> => {
    setLoading(true);
    try {
      const result = await authService.registerUser(email, password, displayName);
      if (result.success && result.user) {
        // Get fresh auth instance and user
        const auth = await getAuth();
        if (auth) {
          setUser(auth.currentUser);
        }
        setUserProfile(result.user);
        // After sign up, user hasn't completed profile yet (no photoURL)
        setHasCompletedProfile(false);
      }
      return result;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async (): Promise<AuthResponse> => {
    setLoading(true);
    try {
      const result = await authService.signOutUser();
      if (result.success) {
        setUser(null);
        setUserProfile(null);
        setHasCompletedProfile(false);
      }
      return result;
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>): Promise<AuthResponse> => {
    if (!user || !userProfile) {
      return { success: false, error: 'No authenticated user' };
    }

    try {
      const result = await authService.updateUserProfile(updates);
      if (result.success) {
        // Update local profile state
        setUserProfile({ ...userProfile, ...updates });
        // If we're updating the photoURL, mark profile as completed
        if (updates.photoURL) {
          setHasCompletedProfile(true);
        }
      }
      return result;
    } catch (error) {
      return { success: false, error: 'Failed to update profile' };
    }
  };

  const getAuthToken = async (): Promise<string | null> => {
    return await authService.getAuthToken();
  };

  const contextValue: AuthContextType = {
    user,
    userProfile,
    loading,
    hasCompletedProfile,
    signIn,
    signUp,
    signOut,
    updateProfile,
    getAuthToken,
    setHasCompletedProfile,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use the AuthContext
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}