import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Appearance, AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: ThemeMode;
  currentTheme: 'light' | 'dark';
  setTheme: (theme: ThemeMode) => void;
  isDark: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [theme, setThemeState] = useState<ThemeMode>('system');
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>('light');

  // Load saved theme preference
  useEffect(() => {
    let isMounted = true;
    
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem('theme');
        if (isMounted) {
          if (savedTheme) {
            setThemeState(savedTheme as ThemeMode);
          } else {
            setThemeState('system'); // Default to system
          }
        }
      } catch (error) {
        console.error('Error loading theme preference:', error);
        if (isMounted) {
          setThemeState('system'); // Default to system on error
        }
      }
    };
    
    loadTheme();
    
    return () => {
      isMounted = false;
    };
  }, []);

  // Initialize current theme based on system preference
  useEffect(() => {
    if (theme === 'system') {
      setCurrentTheme(Appearance.getColorScheme() || 'light');
    } else {
      setCurrentTheme(theme);
    }
  }, [theme]);

  // Handle theme changes
  useEffect(() => {
    const handleAppearanceChange = ({ colorScheme }: { colorScheme: 'light' | 'dark' }) => {
      if (theme === 'system') {
        setCurrentTheme(colorScheme);
      }
    };

    const subscription = Appearance.addChangeListener(handleAppearanceChange);
    
    return () => {
      subscription.remove();
    };
  }, [theme]);

  // Update current theme when theme mode changes
  useEffect(() => {
    if (theme === 'system') {
      setCurrentTheme(Appearance.getColorScheme() || 'light');
    } else {
      setCurrentTheme(theme);
    }
  }, [theme]);

  // Save theme preference
  useEffect(() => {
    let isMounted = true;
    
    const saveTheme = async () => {
      try {
        await AsyncStorage.setItem('theme', theme);
      } catch (error) {
        console.error('Error saving theme preference:', error);
      }
    };
    
    saveTheme();
    
    return () => {
      isMounted = false;
    };
  }, [theme]);

  const setTheme = (newTheme: ThemeMode) => {
    setThemeState(newTheme);
  };

  const isDark = currentTheme === 'dark';

  const value: ThemeContextType = {
    theme,
    currentTheme,
    setTheme,
    isDark,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}