# Sign Out Feature Implementation

## Overview
Added a sign out option at the bottom of the settings page in the Namoshkar Messenger application.

## Changes Made

### 1. Updated Imports
Added the following imports to `/app/(tabs)/settings.tsx`:
```typescript
import { authService } from '@/services/authService';
import { useRouter } from 'expo-router';
```

### 2. Added Router Instance
Added the router instance for navigation:
```typescript
const router = useRouter();
```

### 3. Implemented Sign Out Handler
Added the `handleSignOut` function that:
- Shows a confirmation alert to the user
- Calls the `authService.signOutUser()` method
- Redirects to the sign-in page on successful sign out
- Shows error messages if sign out fails

```typescript
const handleSignOut = () => {
  Alert.alert(
    'Sign Out',
    'Are you sure you want to sign out?',
    [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Sign Out',
        style: 'destructive',
        onPress: async () => {
          try {
            const response = await authService.signOutUser();
            if (response.success) {
              router.replace('/sign-in');
            } else {
              Alert.alert('Error', response.error || 'Failed to sign out');
            }
          } catch (error) {
            Alert.alert('Error', 'An unexpected error occurred while signing out');
          }
        },
      },
    ]
  );
};
```

### 4. Added Sign Out UI Element
Added the sign out option at the bottom of the settings page:
```typescript
{/* Sign Out */}
<View style={styles.section}>
  <SettingsItem
    icon="arrow.right.square.fill"
    title="Sign Out"
    subtitle="Sign out of your account"
    onPress={handleSignOut}
  />
</View>
```

## Features
- Confirmation dialog to prevent accidental sign outs
- Proper error handling for sign out failures
- Visual feedback through alerts
- Clean navigation to sign-in page after sign out
- Consistent styling with other settings items
- Uses the existing authService for authentication management

## Testing
The implementation has been verified to:
- Compile without errors
- Pass linting checks
- Follow the existing code patterns and styling
- Use the correct navigation paths