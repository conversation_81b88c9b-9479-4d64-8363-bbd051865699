// Simple script to add a test notification to Firestore
// This should be run in a Node.js environment with Firebase Admin SDK

// @ts-ignore
import * as admin from 'firebase-admin';

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  try {
    // Try to use service account key
    const serviceAccount = require('./serviceAccountKey.json');
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
    });
  } catch (error) {
    console.log('Service account key not found, using default initialization');
    admin.initializeApp();
  }
}

const db = admin.firestore();

// Add a test notification
async function addTestNotification() {
  try {
    // You'll need to replace these with actual user IDs from your database
    const recipientId = 'TEST_USER_ID'; // Replace with actual recipient user ID
    const senderId = 'TEST_SENDER_ID'; // Replace with actual sender user ID
    
    const notification = {
      recipientId: recipientId,
      senderId: senderId,
      title: 'Test Notification',
      body: 'This is a test notification from the Namoshkar Messenger app',
      data: {
        type: 'test',
        timestamp: new Date().toISOString(),
      },
      imageUrl: null,
      chatId: null,
      sentAt: new Date(),
      delivered: true,
      read: false,
    };

    const result = await db.collection('notifications').add(notification);
    console.log('Test notification added with ID:', result.id);
  } catch (error) {
    console.error('Error adding test notification:', error);
  }
}

// Run the function
addTestNotification();